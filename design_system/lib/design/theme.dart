import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'colors.design.dart';

import 'package:flutter/material.dart';

class AppTheme {
  // Define your color palette
  static const Color primaryColor = Color(0xFF584294);
  static const Color secondaryColor = Color(0xFFFBF0D5);
  static const Color AppBarLogoColor = Color(0xff3A2665);
  static const Color loginAppBarColor = Color(0xffFEF3DD);
  static const Color loginButtonColor = Color(0xffFAEFD6);
  static const Color buttonColorWhite = Color(0xFFffffff);


  static const Color accentColor = Color(0xFFFFD700);

  // Define text styles
  static  TextStyle headlineLarge = GoogleFonts.dmSerifDisplay(
    fontSize: 50,
    fontWeight: FontWeight.bold,
    color: Color(0xff30285D),
  );
  static  TextStyle headlineMedium = GoogleFonts.mulish(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: Color(0xff30285D),
  );
  static
  TextStyle bodyText1 = GoogleFonts.mulish(
    fontSize: 16,
    color: Colors.black87,
  );
  static  TextStyle bodyText2 = GoogleFonts.mulish(
    fontSize: 16,
    color: Colors.white,
  );



  // Define the theme data
  static ThemeData lightTheme = ThemeData(
    primaryColor: primaryColor,
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: AppBarTheme(
      color: primaryColor,
      elevation: 0,
    ),
    textTheme: TextTheme(
      headlineLarge: headlineLarge,
      headlineMedium: headlineMedium,
      bodySmall: bodyText1,
      bodyMedium: bodyText2

    ),
    buttonTheme: ButtonThemeData(
      buttonColor: primaryColor,
      textTheme: ButtonTextTheme.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
  );

}
