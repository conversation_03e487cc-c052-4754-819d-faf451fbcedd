# Include pubspec.lock for app only
**/pubspec.lock

# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
.metadata
*.orig

# Python virtual environment and binaries
venv/
bin/
rtm_custom.py
rtm_new.py

# Android native build artifacts
/app/android/app/.cxx/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VSCode settings (optional to ignore)
#.vscode/

# Flutter / Dart / Pub related
**.dart_tool/
**.flutter-plugins
.**flutter-plugins-dependencies
**.packages
**.pub-cache/
**.pub/
**/build/
coverage/*
coverage_report/*
junit_test_reports/*
test_reports/*
test_results.xml
lib/generated_plugin_registrant.dart
.fvm/flutter_sdk

# Specific to your Flutter project
app/android/keystore.properties

# Node related
node_modules/

# Keep empty coverage and report folders
!coverage/.gitkeep
!coverage_report/.gitkeep
!junit_test_reports/.gitkeep
!test_reports/.gitkeep

# Test and CI tools
**/.bundle
**/fastlane/report.xml

# Freezed / Json Serializable / Codegen
#*.freezed.dart
#*.g.dart

# Exceptions for Flutter tooling
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages
 No newline at end of file
