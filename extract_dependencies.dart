// import 'package:yaml/yaml.dart';
// import 'dart:io';
//
// void main() {
//   // Map to store dependencies without duplicates
//   final allDependencies = <String, String>{};
//
//   // Function to extract dependencies from a given pubspec.lock file
//   void extractDependencies(String path) {
//     final pubspecLockFile = File(path);
//     if (pubspecLockFile.existsSync()) {
//       final content = pubspecLockFile.readAsStringSync();
//       final yaml = loadYaml(content) as YamlMap;
//
//       if (yaml.containsKey('packages')) {
//         final packages = yaml['packages'] as YamlMap;
//         packages.forEach((package, details) {
//           if (details is YamlMap && details.containsKey('version')) {
//             final version = details['version'];
//             if (version is String) {
//               allDependencies[packages] = version;
//             }
//           }
//         });
//       }
//     }
//   }
//
//   // Read the packages from a yaml file
//   final packagesYamlFile = File('melos.yaml');
//   if (packagesYamlFile.existsSync()) {
//     final yamlContent = packagesYamlFile.readAsStringSync();
//     final yaml = loadYaml(yamlContent) as YamlMap;
//
//     if (yaml.containsKey('packages')) {
//       final packages = yaml['packages'] as YamlList;
//
//       // Iterate through all package directories and extract dependencies
//       for (var package in packages) {
//         print(package);
//         final pubspecLockPath = '$package/pubspec.lock';
//         extractDependencies(pubspecLockPath);
//       }
//     }
//   } else {
//     print('packages.yaml file not found.');
//     exit(1);
//   }
//
//   // Write dependencies to a CSV file
//   final csvContent = StringBuffer();
//   csvContent.writeln('Dependency,Version');
//   allDependencies.forEach((package, version) {
//     csvContent.writeln('$package,$version');
//   });
//
//   final outputFile = File('dependencies.csv');
//   outputFile.writeAsStringSync(csvContent.toString());
//
//   print('Dependencies written to dependencies.csv');
// }
