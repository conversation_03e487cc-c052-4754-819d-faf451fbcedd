name: 📦🚀 Build & deploy Android app for an environment

on:
  workflow_call:
    inputs:
      environment-name:
        required: true
        type: string
      environment-url:
        required: true
        type: string
      package-name:
        required: true
        type: string
      release-status:
        required: true
        type: string
      short-environment-name:
        required: true
        type: string
      flavor:
        required: true
        type: string
      new-pubspec-version:
        required: true
        type: string

jobs:
  deployAndroid:
    name: 🤖📦🚀 Build & deploy Android ${{ inputs.short-environment-name }} release
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment-name }}
      url: ${{ inputs.environment-url }}
    steps:
      - name: ⬇️ Checkout repository
        uses: actions/checkout@v3
      - name: ⚙️ Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: "12.x"
          cache: 'gradle'
        id: java
      - name: ⚙️ Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.13.9'
          channel: 'stable'
          cache: true
        id: flutter
      - name: 🔐 Retrieve base64 keystore and decode it to a file
        env:
          KEYSTORE_BASE64: ${{ secrets.KEYSTORE_FILE_BASE64 }}
        run: echo $KEYSTORE_BASE64 | base64 --decode > "${{ github.workspace }}/android-keystore.jks"
      - name: 📝🔐 Create keystore.properties file
        env:
          KEYSTORE_PROPERTIES_PATH: ${{ github.workspace }}/app/android/keystore.properties
        run: |
          echo '${{ inputs.flavor }}StoreFile=${{ github.workspace }}/android-keystore.jks' > $KEYSTORE_PROPERTIES_PATH
          echo '${{ inputs.flavor }}KeyAlias=${{ secrets.KEYSTORE_KEY_ALIAS }}' >> $KEYSTORE_PROPERTIES_PATH
          echo '${{ inputs.flavor }}StorePassword=${{ secrets.KEYSTORE_PASSWORD }}' >> $KEYSTORE_PROPERTIES_PATH
          echo '${{ inputs.flavor }}KeyPassword=${{ secrets.KEYSTORE_KEY_PASSWORD }}' >> $KEYSTORE_PROPERTIES_PATH
      - name: 📝 Edit pubspec version
        run: |
          sed -Ei "s/^version: (.*)/version: ${{ inputs.new-pubspec-version }}/" app/pubspec.yaml
      - name: ⚙️ Setup Melos
        uses: bluefireteam/melos-action@v2
      - name: ⚙️ Install dependencies for all packages
        run: melos build:pub_get:all
      - name: 🤖📦 Create Android ${{ inputs.short-environment-name }} appbundle release
        run: |
          pushd app
          mkdir debug-symbols
          flutter build appbundle \
            --release \
            --flavor ${{ inputs.flavor }} \
            --target lib/main_${{ inputs.flavor }}.dart
          popd
      - name: 🤖🚀 Upload Android ${{ inputs.short-environment-name }} Release to Play Store
        uses: r0adkll/upload-google-play@v1.0.19
        with:
          packageName: ${{ inputs.package-name }}
          track: internal
          status: ${{ inputs.release-status }}
          releaseFiles: ${{ github.workspace }}/app/build/app/outputs/bundle/${{ inputs.flavor }}Release/app-${{ inputs.flavor }}-release.aab
          serviceAccountJsonPlainText: "${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_KEY_JSON }}"
#      - name: 💬 Pick a random success catchphrase
#        if: success()
#        id: success_catchphrase
#        run: |
#          sentences=('🤩 AMAZING !' 'Woop woop 🎉' 'Oh wow 😮' '😎 Yeahhhh !' '📣 Amazing announcement !' '📢 Your attention please...' '👏 Great work !' '🍾 Champagne !' '🙌 High five !' '🥳 Time to celebrate !')
#          arrayLength=${#sentences[@]}
#          randomNumber=$(($RANDOM%$arrayLength))
#          pickedSentence=${sentences[$randomNumber]}
#          echo "picked_sentence=$pickedSentence" >> $GITHUB_OUTPUT
#      - name: 🔔✅ Send success notif to Discord
#        if: success()
#        uses: sarisia/actions-status-discord@v1
#        with:
#          webhook: ${{ secrets.DISCORD_DEPLOYMENT_WEBHOOK_URL }}
#          avatar_url: ${{ secrets.DISCORD_DEPLOYMENT_WEBHOOK_AVATAR }}
#          username: "${{ inputs.short-environment-name }} notifier"
#          content: "${{ steps.success_catchphrase.outputs.picked_sentence }}"
#          title: "🤖 New version of ${{ inputs.environment-name }} app available !"
#          description: |
#            Version `${{ inputs.new-pubspec-version }}`
#            Click [here](${{ inputs.environment-url }}) to download
#          url: ${{ inputs.environment-url }}
#          nofail: true
#          nodetail: true
#      - name: 🔔❌ Send failure notif to Discord
#        if: failure()
#        uses: sarisia/actions-status-discord@v1
#        env:
#          RUN_URL: "https://github.com/orevial/flutter-ci-cd-demo/actions/runs/${{ github.run_id }}"
#        with:
#          webhook: ${{ secrets.DISCORD_BUILD_WEBHOOK_URL }}
#          avatar_url: ${{ secrets.DISCORD_DEPLOYMENT_WEBHOOK_AVATAR }}
#          username: "${{ inputs.short-environment-name }} notifier"
#          content: "Oh no 😢"
#          title: "🤖 Release of ${{ inputs.environment-name }} app has failed..."
#          description: |
#            Failed job: 🍏📦🚀 Build & deploy iOS ${{ inputs.short-environment-name }} release
#            Failed to release version `${{ inputs.new-pubspec-version }}` of ${{ inputs.environment-name }} app
#            Click [here](${{ env.RUN_URL }}) to go to failed run output
#          url: ${{ env.RUN_URL }}
#          nofail: true
#          nodetail: true