name: 📦🚀 Build & deploy UAT release

permissions: write-all
on: workflow_dispatch
#on:
#  push:
#    branches:
#      - 'test'
#    paths-ignore:
#      - '**.md'
#      - 'doc/**'
#      - '.git/'
#      - '.vscode/'

jobs:
  testAndCoverage:
    name: 🧪 Test
    uses: ./.github/workflows/_test_with_coverage.yml
    secrets: inherit

  gitRelease:
    name: Create git release for UAT app
    runs-on: ubuntu-latest
    outputs:
      new_pubspec_version: "${{ steps.get_new_pubspec_version.outputs.next_pubspec_version }}"
    steps:
      - name: ⬇️ Checkout repository with tags
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: 🏷️🧪 Get latest UAT release
        id: get_latest_uat_release
        uses: "WyriHaximus/github-action-get-previous-tag@v1"
        with:
          prefix: "release/uat/"
          fallback: 0.0.1
      - name: ⚙️ Prepare semantic release configuration
        run: |
          mv .releaserc.uat.json .releaserc.json
      - name: 🏷️🔮 Get next UAT release semver version
        id: semantic_release_info
        uses: cycjimmy/semantic-release-action@v3.2.0
        env:
          GITHUB_TOKEN: ${{ secrets.RELEASE_AUTOMATOR_PAT }}
        with:
          semantic_version: 19
      - name: 📝 Calculate complete UAT version for next version
        id: get_new_pubspec_version
        run: |
          last_uat_release=$(echo "${{ steps.get_latest_uat_release.outputs.tag }}" | sed -E "s/release\/uat\/(.*)/\1/")
          next_pubspec_version=$(./scripts/semver.sh "$last_uat_release" "${{ steps.semantic_release_info.outputs.new_release_version }}")
          echo "next_pubspec_version=$next_pubspec_version" >> $GITHUB_OUTPUT
      - name: 🏷️✍️ Create new UAT release tag
        uses: rickstaa/action-create-tag@v1
        with:
          tag: "release/uat/${{ steps.get_new_pubspec_version.outputs.next_pubspec_version }}"
          message: "UAT release ${{ steps.get_new_pubspec_version.outputs.next_pubspec_version }}"
          github_token: ${{ secrets.RELEASE_AUTOMATOR_PAT }}

  deployUat:
    name: Deploy UAT
    uses: ./.github/workflows/_deploy-env-apps.yml
    needs: gitRelease
    secrets: inherit
    with:
      android-environment-name: 'Android UAT'
      android-environment-url: 'https://play.google.com/console/u/0/developers/8257228763944703769/app/4975474988058507059/tracks/internal-testing'
      android-package-name: 'com.junotechno.junoplus.uat'
      android-release-status: 'draft'
#      ios-environment-name: 'iOS UAT'
#      ios-environment-url: 'https://appstoreconnect.apple.com/apps/1664853019/testflight'
      short-environment-name: 'UAT'
      flavor: 'uat'
      new-pubspec-version: ${{ needs.gitRelease.outputs.new_pubspec_version }}
