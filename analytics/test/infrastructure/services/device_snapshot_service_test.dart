// import 'dart:async';
// import 'package:analytics/infrastructure/services/device_snapshot_service.dart';
// import 'package:analytics/domain/models/device_connection_state_model.dart';
// import 'package:analytics/analytics.dart';
// import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
// import 'package:bluetooth/bluetooth.dart';
// import 'package:remote/remote.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mockito/mockito.dart';
// import 'package:mockito/annotations.dart';

// import 'device_snapshot_service_test.mocks.dart';

// @GenerateMocks([
//   IBluetoothFacade,
//   ILocalTelemetryDataSource,
// ])
// void main() {
//   group('DeviceSnapshotService Connection State Tests', () {
//     late DeviceSnapshotService service;
//     late MockIBluetoothFacade mockBluetoothFacade;
//     late MockILocalTelemetryDataSource mockLocalDataSource;
//     late StreamController<Either<Failure, DeviceModel>> deviceStreamController;

//     setUp(() {
//       mockBluetoothFacade = MockIBluetoothFacade();
//       mockLocalDataSource = MockILocalTelemetryDataSource();
//       deviceStreamController =
//           StreamController<Either<Failure, DeviceModel>>.broadcast();

//       // Setup mock bluetooth facade to return the stream
//       when(mockBluetoothFacade.getDeviceInformation())
//           .thenAnswer((_) => deviceStreamController.stream);

//       // Setup mock local data source to return success
//       when(mockLocalDataSource.saveDeviceConnectionState(any))
//           .thenAnswer((_) async => true);
//       when(mockLocalDataSource.saveDeviceSnapshot(any))
//           .thenAnswer((_) async => true);

//       service = DeviceSnapshotService(
//         mockBluetoothFacade,
//         mockLocalDataSource,
//       );
//     });

//     tearDown(() {
//       deviceStreamController.close();
//       service.dispose();
//     });

//     test('should track connection state changes', () async {
//       const sessionId = 'test-session';
//       final connectionStates = <DeviceConnectionStateModel>[];

//       // Listen to connection state stream
//       service.connectionStateStream.listen((state) {
//         connectionStates.add(state);
//       });

//       // Start monitoring
//       await service.startMonitoring(sessionId);

//       // Wait a moment for initial state
//       await Future.delayed(const Duration(milliseconds: 100));

//       // Simulate device connection success
//       final mockDevice = DeviceModel(
//         deviceInfo: const DeviceInfoModel(
//           deviceId: 'test-device',
//           batteryLevel: BatteryLevelModel(batteryLevel: 85),
//         ),
//         heatLevel: const HeatLevelModel(selectedHeatLevel: 5),
//         tensLevel: const TensLevelModel(selectedTensLevel: 3, mode: 1),
//       );

//       deviceStreamController.add(Right(mockDevice));

//       // Wait for processing
//       await Future.delayed(const Duration(milliseconds: 100));

//       // Simulate device disconnection
//       deviceStreamController
//           .add(Left(const UnknownFailure('Device disconnected')));

//       // Wait for processing
//       await Future.delayed(const Duration(milliseconds: 100));

//       // Stop monitoring
//       await service.stopMonitoring();

//       // Verify connection states were captured
//       expect(connectionStates.length, greaterThanOrEqualTo(2));

//       // Verify initial connecting state
//       expect(connectionStates.first.status, 'connecting');
//       expect(connectionStates.first.sessionId, sessionId);

//       // Verify connection state changes
//       final connectedStates =
//           connectionStates.where((s) => s.status == 'connected').toList();
//       final disconnectedStates =
//           connectionStates.where((s) => s.status == 'disconnected').toList();

//       expect(connectedStates.isNotEmpty, true);
//       expect(disconnectedStates.isNotEmpty, true);

//       // Verify save was called
//       verify(mockLocalDataSource.saveDeviceConnectionState(any))
//           .called(greaterThan(0));
//     });

//     test('should not duplicate connection state when state unchanged',
//         () async {
//       const sessionId = 'test-session';
//       final connectionStates = <DeviceConnectionStateModel>[];

//       // Listen to connection state stream
//       service.connectionStateStream.listen((state) {
//         connectionStates.add(state);
//       });

//       // Start monitoring
//       await service.startMonitoring(sessionId);

//       // Wait a moment for initial state
//       await Future.delayed(const Duration(milliseconds: 100));

//       // Simulate multiple successful device information calls (should stay connected)
//       final mockDevice = DeviceModel(
//         deviceInfo: const DeviceInfoModel(
//           deviceId: 'test-device',
//           batteryLevel: BatteryLevelModel(batteryLevel: 85),
//         ),
//         heatLevel: const HeatLevelModel(selectedHeatLevel: 5),
//         tensLevel: const TensLevelModel(selectedTensLevel: 3, mode: 1),
//       );

//       // Send same success multiple times
//       deviceStreamController.add(Right(mockDevice));
//       await Future.delayed(const Duration(milliseconds: 50));
//       deviceStreamController.add(Right(mockDevice));
//       await Future.delayed(const Duration(milliseconds: 50));
//       deviceStreamController.add(Right(mockDevice));

//       // Wait for processing
//       await Future.delayed(const Duration(milliseconds: 100));

//       // Stop monitoring
//       await service.stopMonitoring();

//       // Count connected states (should not have duplicates)
//       final connectedStates =
//           connectionStates.where((s) => s.status == 'connected').toList();

//       // Should only have one connected state despite multiple successful calls
//       expect(connectedStates.length, 1);
//     });
//   });
// }
