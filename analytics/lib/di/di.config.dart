// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:analytics/analytics.dart' as _i548;
import 'package:analytics/application/analytics_initialization_bloc/analytics_initialization_bloc.dart'
    as _i701;
import 'package:analytics/domain/facade/analytics_facade.dart' as _i414;
import 'package:analytics/infrastructure/analytics_facade_impl.dart' as _i724;
import 'package:analytics/infrastructure/datasources/cloud_telemetry_datasource.dart'
    as _i442;
import 'package:analytics/infrastructure/datasources/local_telemetry_datasource.dart'
    as _i418;
import 'package:analytics/infrastructure/services/analytics_initialization_service.dart'
    as _i970;
import 'package:analytics/infrastructure/services/device_snapshot_service.dart'
    as _i660;
import 'package:analytics/infrastructure/services/session_state_manager.dart'
    as _i451;
import 'package:authentication/domain/facade/i_auth_facade.dart' as _i690;
import 'package:bluetooth/domain/facade/bluetooth_facade.dart' as _i909;
import 'package:cloud_firestore/cloud_firestore.dart' as _i974;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:remote/domain/facade/remote_control_facade.dart' as _i509;
import 'package:shared_preferences/shared_preferences.dart' as _i460;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.lazySingleton<_i418.ILocalTelemetryDataSource>(() =>
        _i418.SharedPrefsTelemetryDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i442.ICloudTelemetryDataSource>(
        () => _i442.FirebaseTelemetryDataSource(
              gh<_i974.FirebaseFirestore>(),
              gh<_i690.IAuthFacade>(),
            ));
    gh.lazySingleton<_i660.DeviceSnapshotService>(
        () => _i660.DeviceSnapshotService(
              gh<_i909.IBluetoothFacade>(),
              gh<_i548.ILocalTelemetryDataSource>(),
            ));
    gh.lazySingleton<_i451.SessionStateManager>(() => _i451.SessionStateManager(
          gh<_i548.ILocalTelemetryDataSource>(),
          gh<_i548.ICloudTelemetryDataSource>(),
          gh<_i509.RemoteControlFacade>(),
          gh<_i460.SharedPreferences>(),
        ));
    gh.lazySingleton<_i548.IAnalyticsFacade>(() => _i724.AnalyticsFacadeImpl(
          gh<_i548.ILocalTelemetryDataSource>(),
          gh<_i548.ICloudTelemetryDataSource>(),
          gh<_i548.SessionStateManager>(),
        ));
    gh.lazySingleton<_i970.AnalyticsInitializationService>(
        () => _i970.AnalyticsInitializationService(
              gh<_i414.IAnalyticsFacade>(),
              gh<_i418.ILocalTelemetryDataSource>(),
              gh<_i460.SharedPreferences>(),
              gh<_i509.RemoteControlFacade>(),
            ));
    gh.factory<_i701.AnalyticsInitializationBloc>(() =>
        _i701.AnalyticsInitializationBloc(
            gh<_i970.AnalyticsInitializationService>()));
    return this;
  }
}
