// Import statements for dependency injection and service locator
import 'package:injectable/injectable.dart';
import 'package:get_it/get_it.dart';
import 'di.config.dart'; // Importing the generated dependency injection configuration file

// Creating a singleton instance of GetIt for managing dependencies
GetIt getIt = GetIt.instance;

// Function to configure dependencies using injectable and GetIt
@InjectableInit()
void configureDependencies({
  required String env, // Environment parameter for configuring dependencies
  bool isTest = false, // Flag indicating if the application is in test mode
}) {
  // Initialize GetIt with the specified environment
  getIt.init(environment: env);

  // Allow reassignment of dependencies in test mode if needed
  if (isTest) {
    getIt.allowReassignment = true;
  }
}
