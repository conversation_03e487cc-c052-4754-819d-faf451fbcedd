part of 'analytics_initialization_bloc.dart';

abstract class AnalyticsInitializationState {}

/// Initial state before analytics initialization
class AnalyticsInitializationInitial extends AnalyticsInitializationState {}

/// State when analytics initialization is in progress
class AnalyticsInitializationLoading extends AnalyticsInitializationState {}

/// State when analytics initialization completed successfully
class AnalyticsInitializationSuc<PERSON> extends AnalyticsInitializationState {
  final String message;
  final Map<String, dynamic> stats;

  AnalyticsInitializationSuccess({
    required this.message,
    required this.stats,
  });
}

/// State when analytics initialization failed
class AnalyticsInitializationFailure extends AnalyticsInitializationState {
  final String error;

  AnalyticsInitializationFailure({required this.error});
}
