
import 'package:analytics/domain/models/timestamp_converters.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'device_connection_state_model.g.dart';

@JsonSerializable(explicitToJson: true)
class DeviceConnectionStateModel {
  final String status; // e.g., 'connected', 'disconnected', 'connecting'
  final String? sessionId;
  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  final DateTime? timestamp;

  DeviceConnectionStateModel({
    required this.sessionId,
    required this.status,
    required this.timestamp,
  });

  factory DeviceConnectionStateModel.fromJson(Map<String, dynamic> json) =>
      _$DeviceConnectionStateModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceConnectionStateModelToJson(this);

  // Local JSON serialization (ISO8601 for timestamp)
  factory DeviceConnectionStateModel.fromLocal<PERSON>son(Map<String, dynamic> json) {
    return DeviceConnectionStateModel(
      status: json['status'] as String,
      timestamp: json['timestamp'] != null
          ? DateTime.tryParse(json['timestamp'] as String)
          : null,
      sessionId: json['sessionId'] as String,
    );
  }

  Map<String, dynamic> toLocalJson() => {
        'status': status,
        'timestamp': timestamp?.toIso8601String(),
        'sessionId': sessionId,
      };
}