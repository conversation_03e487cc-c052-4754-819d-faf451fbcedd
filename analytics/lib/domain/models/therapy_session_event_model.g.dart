// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'therapy_session_event_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TherapySessionEventModel _$TherapySessionEventModelFromJson(
        Map<String, dynamic> json) =>
    TherapySessionEventModel(
      eventId: json['eventId'] as String,
      sessionId: json['sessionId'] as String?,
      eventType: json['eventType'] as String,
      timestamp: firestoreTimestampFromJson(json['timestamp']),
      eventData: json['eventData'] as Map<String, dynamic>,
      isDeviceInitiated: json['isDeviceInitiated'] as bool,
      isSyncedToCloud: json['isSyncedToCloud'] as bool? ?? false,
    );

Map<String, dynamic> _$TherapySessionEventModelToJson(
        TherapySessionEventModel instance) =>
    <String, dynamic>{
      'eventId': instance.eventId,
      'sessionId': instance.sessionId,
      'eventType': instance.eventType,
      'timestamp': firestoreTimestampToJson(instance.timestamp),
      'eventData': instance.eventData,
      'isDeviceInitiated': instance.isDeviceInitiated,
      'isSyncedToCloud': instance.isSyncedToCloud,
    };
