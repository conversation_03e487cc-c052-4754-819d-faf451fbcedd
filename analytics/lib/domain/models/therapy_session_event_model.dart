import 'package:analytics/domain/models/timestamp_converters.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'therapy_session_event_model.g.dart';
@JsonSerializable(explicitToJson: true)
class TherapySessionEventModel {
  final String eventId;
  String? sessionId;
  final String eventType;
  @JsonKey(
      fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  final DateTime? timestamp;
  final Map<String, dynamic> eventData;
  final bool isDeviceInitiated;
  final bool isSyncedToCloud ;

  TherapySessionEventModel({
    required this.eventId,
    this.sessionId,
    required this.eventType,
    required this.timestamp,
    required this.eventData,
    required this.isDeviceInitiated,
    this.isSyncedToCloud = false,
  });

  factory TherapySessionEventModel.fromJson(Map<String, dynamic> json) =>
      _$TherapySessionEventModelFromJson(json);

  // For loading from SharedPreferences
  factory TherapySessionEventModel.fromLocalJson(Map<String, dynamic> json) {
    return TherapySessionEventModel(
      eventId: json['eventId'] as String,
      sessionId: json['sessionId'] as String,
      eventType: json['eventType'] as String,
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'] as String)
          : null,
      eventData: Map<String, dynamic>.from(json['eventData'] as Map),
      isDeviceInitiated: json['isDeviceInitiated'] as bool,
      isSyncedToCloud: json['isSyncedToCloud'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() => _$TherapySessionEventModelToJson(this);

  // For SharedPreferences - uses ISO8601 strings
  Map<String, dynamic> toLocalJson() {
    return {
      'eventId': eventId,
      'sessionId': sessionId,
      'eventType': eventType,
      'timestamp': timestamp?.toIso8601String(),
      'eventData': eventData,
      'isDeviceInitiated': isDeviceInitiated,
      'isSyncedToCloud': isSyncedToCloud,
    };
  }

  // For Firestore - uses Timestamp objects
  Map<String, dynamic> toFirestoreJson() {
    return {
      'eventId': eventId,
      'sessionId': sessionId,
      'eventType': eventType,
      'timestamp': timestamp != null ? Timestamp.fromDate(timestamp!) : null,
      'eventData': eventData,
      'isDeviceInitiated': isDeviceInitiated,
      'isSyncedToCloud': isSyncedToCloud,
    };
  }

  TherapySessionEventModel copyWith({
    String? eventId,
    String? sessionId,
    String? eventType,
    DateTime? timestamp,
    Map<String, dynamic>? eventData,
    bool? isDeviceInitiated,
    bool? isSyncedToCloud,
  }) {
    return TherapySessionEventModel(
      eventId: eventId ?? this.eventId,
      sessionId: sessionId ?? this.sessionId,
      eventType: eventType ?? this.eventType,
      timestamp: timestamp ?? this.timestamp,
      eventData: eventData ?? this.eventData,
      isDeviceInitiated: isDeviceInitiated ?? this.isDeviceInitiated,
      isSyncedToCloud: this.isSyncedToCloud,
    );
  }

  factory TherapySessionEventModel.settingChange({
    required String eventId,
    required String sessionId,
    required String settingType,
    required int oldValue,
    required int newValue,
  }) {
    return TherapySessionEventModel(
      sessionId: sessionId,
      eventId: eventId,
      eventType: 'setting_change',
      timestamp: DateTime.now(),
      isDeviceInitiated: false,
      eventData: {
        'type': settingType,
        'oldValue': oldValue,
        'newValue': newValue,
      },
    );
  }
}