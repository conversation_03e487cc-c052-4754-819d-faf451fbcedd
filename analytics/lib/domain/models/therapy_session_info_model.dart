import 'package:analytics/domain/models/therapy_session_model.dart';
import 'package:analytics/domain/models/timestamp_converters.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'therapy_session_info_model.g.dart';

    @JsonSerializable(explicitToJson: true)
    class TherapySessionInfoModel {
      final String sessionId;
      final String? userId;
      @JsonKey(
          fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
      final DateTime? therapyStartTime;
      @JsonKey(
          fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
      final DateTime? therapyEndTime;
      final int therapyDuration; // Duration in minutes

      TherapySessionInfoModel({
        required this.sessionId,
        this.userId,
        this.therapyStartTime,
        this.therapyEndTime,
        this.therapyDuration = 0,
      });

      factory TherapySessionInfoModel.fromJson(Map<String, dynamic> json) =>
          _$TherapySessionInfoModelFromJson(json);

      Map<String, dynamic> toJson() => _$TherapySessionInfoModelToJson(this);

      // === Local JSON serialization ===
      factory TherapySessionInfoModel.fromLocalJson(Map<String, dynamic> json) {
        return TherapySessionInfoModel(
          sessionId: json['sessionId'] as String,
          userId: json['userId'] as String?,
          therapyStartTime: json['therapyStartTime'] != null
              ? DateTime.tryParse(json['therapyStartTime'])
              : null,
          therapyEndTime: json['therapyEndTime'] != null
              ? DateTime.tryParse(json['therapyEndTime'])
              : null,
          therapyDuration: json['therapyDuration'] as int? ?? 0,
        );
      }

      Map<String, dynamic> toLocalJson() => {
            'sessionId': sessionId,
            'userId': userId,
            'therapyStartTime': therapyStartTime?.toIso8601String(),
            'therapyEndTime': therapyEndTime?.toIso8601String(),
            'therapyDuration': therapyDuration,
          };

      //copyWith method for immutability
      TherapySessionInfoModel copyWith({
        String? sessionId,
        String? userId,
        DateTime? therapyStartTime,
        DateTime? therapyEndTime,
        int? therapyDuration,
      }) {
        return TherapySessionInfoModel(
          sessionId: sessionId ?? this.sessionId,
          userId: userId ?? this.userId,
          therapyStartTime: therapyStartTime ?? this.therapyStartTime,
          therapyEndTime: therapyEndTime ?? this.therapyEndTime,
          therapyDuration: therapyDuration ?? this.therapyDuration,
        );
      }
    }