import 'package:cloud_firestore/cloud_firestore.dart' as firestore;

// === 🔁 Timestamp Converters ===
DateTime? firestoreTimestampFromJson(dynamic value) {
  if (value == null) return null;
  if (value is firestore.Timestamp) return value.toDate();
  if (value is String) return DateTime.tryParse(value);
  return null;
}

firestore.Timestamp? firestoreTimestampToJson(DateTime? dateTime) =>
    dateTime != null ? firestore.Timestamp.fromDate(dateTime) : null;

String? localTimestampToJson(DateTime? dateTime) => dateTime?.toIso8601String();
