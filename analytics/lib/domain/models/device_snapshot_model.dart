import 'package:analytics/domain/models/therapy_session_model.dart';
import 'package:analytics/domain/models/timestamp_converters.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'device_snapshot_model.g.dart';

@JsonSerializable(explicitToJson: true)
class DeviceSnapshotModel {
  final String snapshotId;
  final String sessionId;
  @JsonKey(
      fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  final DateTime? timestamp;
  final int heatLevel;
  final int tensLevel;
  final int mode;
  final int batteryLevel;
  final Map<String, dynamic> additionalData;

  DeviceSnapshotModel({
    required this.snapshotId,
    required this.sessionId,
    required this.timestamp,
    required this.heatLevel,
    required this.tensLevel,
    required this.mode,
    required this.batteryLevel,
    required this.additionalData,
  });

  factory DeviceSnapshotModel.fromJson(Map<String, dynamic> json) =>
      _$DeviceSnapshotModelFromJson(json);

  // For loading from SharedPreferences
  factory DeviceSnapshotModel.fromLocalJson(Map<String, dynamic> json) {
    return DeviceSnapshotModel(
      snapshotId: json['snapshotId'] as String,
      sessionId: json['sessionId'] as String,
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'] as String)
          : null,
      heatLevel: json['heatLevel'] as int,
      tensLevel: json['tensLevel'] as int,
      mode: json['mode'] as int,
      batteryLevel: json['batteryLevel'] as int,
      additionalData: Map<String, dynamic>.from(json['additionalData'] as Map),
    );
  }

  Map<String, dynamic> toJson() => _$DeviceSnapshotModelToJson(this);

  // For SharedPreferences - uses ISO8601 strings
  Map<String, dynamic> toLocalJson() {
    return {
      'snapshotId': snapshotId,
      'sessionId': sessionId,
      'timestamp': timestamp?.toIso8601String(),
      'heatLevel': heatLevel,
      'tensLevel': tensLevel,
      'mode': mode,
      'batteryLevel': batteryLevel,
      'additionalData': additionalData,
    };
  }

  // For Firestore - uses Timestamp objects
  Map<String, dynamic> toFirestoreJson() {
    return {
      'snapshotId': snapshotId,
      'sessionId': sessionId,
      'timestamp': timestamp != null ? Timestamp.fromDate(timestamp!) : null,
      'heatLevel': heatLevel,
      'tensLevel': tensLevel,
      'mode': mode,
      'batteryLevel': batteryLevel,
      'additionalData': additionalData,
    };
  }

  DeviceSnapshotModel copyWith({
    String? snapshotId,
    String? sessionId,
    DateTime? timestamp,
    int? heatLevel,
    int? tensLevel,
    int? mode,
    int? batteryLevel,
    Map<String, dynamic>? additionalData,
  }) {
    return DeviceSnapshotModel(
      snapshotId: snapshotId ?? this.snapshotId,
      sessionId: sessionId ?? this.sessionId,
      timestamp: timestamp ?? this.timestamp,
      heatLevel: heatLevel ?? this.heatLevel,
      tensLevel: tensLevel ?? this.tensLevel,
      mode: mode ?? this.mode,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}