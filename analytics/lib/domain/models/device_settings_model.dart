
    import 'package:analytics/domain/models/therapy_session_model.dart';
import 'package:analytics/domain/models/timestamp_converters.dart';
    import 'package:freezed_annotation/freezed_annotation.dart';
    part 'device_settings_model.g.dart';

    @JsonSerializable(explicitToJson: true)
    class DeviceSettingsModel {
      int heatLevel;
      int tensLevel;
      int tensMode;
      int batteryLevel;
      @JsonKey(
          fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
      DateTime? timestamp;

      DeviceSettingsModel({
        required this.heatLevel,
        required this.tensLevel,
        required this.tensMode,
        required this.batteryLevel,
        required this.timestamp,
      });

      factory DeviceSettingsModel.fromJson(Map<String, dynamic> json) =>
          _$DeviceSettingsModelFromJson(json);

      Map<String, dynamic> toJson() => _$DeviceSettingsModelToJson(this);

      // Local JSON serialization (ISO8601 for timestamp)
      factory DeviceSettingsModel.fromLocalJson(Map<String, dynamic> json) {
        return DeviceSettingsModel(
          heatLevel: json['heatLevel'] as int,
          tensLevel: json['tensLevel'] as int,
          tensMode: json['tensMode'] as int,
          batteryLevel: json['batteryLevel'] as int,
          timestamp: json['timestamp'] != null
              ? DateTime.tryParse(json['timestamp'] as String)
              : null,
        );
      }

      Map<String, dynamic> toLocalJson() => {
            'heatLevel': heatLevel,
            'tensLevel': tensLevel,
            'tensMode': tensMode,
            'batteryLevel': batteryLevel,
            'timestamp': timestamp?.toIso8601String(),
          };
    }