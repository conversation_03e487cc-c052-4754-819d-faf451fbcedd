import 'dart:async';
import 'package:analytics/analytics.dart';
import 'package:analytics/domain/models/device_connection_state_model.dart';
import 'package:analytics/domain/models/device_snapshot_model.dart';
import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';

@LazySingleton()
class DeviceSnapshotService {
  final IBluetoothFacade _remoteControlFacade;
  final ILocalTelemetryDataSource _localDataSource;

  final _uuid = const Uuid();

  // Timers and subscriptions
  Timer? _snapshotTimer;
  Timer? _deviceLogTimer;
  Timer? _connectionCheckTimer;
  StreamSubscription? _deviceDataSubscription;

  // Constants
  static const Duration _snapshotInterval =
      Duration(minutes: 7); // Changed to capture every 10 seconds
  static const Duration _deviceLogInterval = Duration(minutes: 7);


  // Current session tracking
  String? _currentSessionId;
  bool _isMonitoring = false;
  String? _lastConnectionState; // Track previous connection state

  // Stream controllers
  final _snapshotController = StreamController<DeviceSnapshotModel>.broadcast();
  final _deviceLogController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _connectionStateController = StreamController<DeviceConnectionStateModel>.broadcast();

  DeviceSnapshotService(
    this._remoteControlFacade,
    this._localDataSource
  );

  // Getters
  Stream<DeviceSnapshotModel> get snapshotStream => _snapshotController.stream;
  Stream<Map<String, dynamic>> get deviceLogStream =>
      _deviceLogController.stream;
  Stream<DeviceConnectionStateModel> get connectionStateStream =>
      _connectionStateController.stream;
  bool get isMonitoring => _isMonitoring;

  /// Start monitoring device for snapshots and logs
  Future<void> startMonitoring(String sessionId) async {
    if (_isMonitoring) {
      await stopMonitoring();
    }

    _currentSessionId = sessionId;
    _isMonitoring = true;

    debugPrint('📱 Starting device monitoring for session: $sessionId');

    // Start snapshot timer (every 10 seconds)
    _snapshotTimer =
        Timer.periodic(_snapshotInterval, (_) => _captureDeviceSnapshot());

    // Start device log timer (every 3 seconds)
    _deviceLogTimer =
        Timer.periodic(_deviceLogInterval, (_) => _captureDeviceLog());



    // Subscribe to device information stream for connection state monitoring
    _deviceDataSubscription = _remoteControlFacade
        .getDeviceInformation()
        .listen((deviceResult) {
      deviceResult.mapBoth(
        onLeft: (failure) {
          _logError('Failed to get device information: $failure');
          // On failure, consider device disconnected
          _handleConnectionStateChange('disconnected');
        },
        onRight: (deviceModel) {
          // Handle device model update
          debugPrint('📱 Device information updated for session: $_currentSessionId');
          // When we successfully get device info, device is connected
          _handleConnectionStateChange('connected');
        },
      );
    });

    // Capture initial snapshot and device log
    await _captureDeviceSnapshot();
    await _captureDeviceLog();
    
    // Set initial connection state to connecting
    await _handleConnectionStateChange('connecting');
  }

  /// Stop monitoring device
  Future<void> stopMonitoring() async {
    _isMonitoring = false;
    _currentSessionId = null;
    _lastConnectionState = null; // Reset connection state

    _snapshotTimer?.cancel();
    _deviceLogTimer?.cancel();
    _connectionCheckTimer?.cancel();
    _deviceDataSubscription?.cancel();

    _snapshotTimer = null;
    _deviceLogTimer = null;
    _connectionCheckTimer = null;
    _deviceDataSubscription = null;
  }

  /// Capture a device snapshot
  Future<void> _captureDeviceSnapshot() async {
    if (!_isMonitoring || _currentSessionId == null) return;

    try {
      final deviceData = await _remoteControlFacade.getDeviceInformation().first;
      deviceData.mapBoth(
        onLeft: (failure) {
          _logError('Failed to get device information: $failure');
          return;
        },
        onRight: (deviceInfo) async {
          final snapshot = DeviceSnapshotModel(
            snapshotId: _uuid.v4(),
            sessionId: _currentSessionId!,
            timestamp: DateTime.now(),
            heatLevel: deviceInfo.heatLevel?.selectedHeatLevel ?? 0,
            tensLevel: deviceInfo.tensLevel?.selectedTensLevel ?? 0,
            mode: deviceInfo.tensLevel?.mode ?? 0,
            batteryLevel: deviceInfo.deviceInfo.batteryLevel?.batteryLevel ?? 0,
            additionalData: {},
          );

          // Save snapshot locally
          await _localDataSource.saveDeviceSnapshot(
            snapshot
              );
          _snapshotController.add(snapshot);
        },
      );
    } catch (e) {
      _logError('Failed to capture device snapshot: $e');
    }
  }

  /// Capture device log entry
  Future<void> _captureDeviceLog() async {
    if (!_isMonitoring || _currentSessionId == null) return;

    try {
      final deviceData = await _remoteControlFacade.getDeviceInformation().first;
      
      deviceData.mapBoth(
        onLeft: (failure) {
          _logError('Failed to get device information for log: $failure');
          return;
        },
        onRight: (deviceInfo) {
          final logEntry = {
            'id': _uuid.v4(),
            'sessionId': _currentSessionId!,
            'timestamp': DateTime.now().toIso8601String(),
            'type': 'periodic_device_read',
            'batteryLevel': deviceInfo.deviceInfo.batteryLevel?.batteryLevel ?? 0,
            'heatLevel': deviceInfo.heatLevel?.selectedHeatLevel ?? 0,
            'tensLevel': deviceInfo.tensLevel?.selectedTensLevel ?? 0,
            'mode': deviceInfo.tensLevel?.mode ?? 0,
            'source': 'device_snapshot_service',
          };

          _deviceLogController.add(logEntry);
          debugPrint('📊 Device log captured for session: $_currentSessionId');
        },
      );
    } catch (e) {
      _logError('Failed to capture device log: $e');
    }
  }

  /// Proactively check connection status to detect disconnects
  Future<void> _checkConnectionProactively() async {
    if (!_isMonitoring || _currentSessionId == null) return;

    try {
      // Try to get device information with a timeout
      final deviceData = await _remoteControlFacade
          .getDeviceInformation()
          .timeout(const Duration(seconds: 5))
          .first;
      
      deviceData.mapBoth(
        onLeft: (failure) {
          // If we get a failure, device might be disconnected
          debugPrint('🔍 Proactive connection check failed: $failure');
          _handleConnectionStateChange('disconnected');
        },
        onRight: (deviceInfo) {
          // If we successfully get device info, device is still connected
          debugPrint('🔍 Proactive connection check passed');
          _handleConnectionStateChange('connected');
        },
      );
    } on TimeoutException {
      // Timeout suggests device disconnection
      debugPrint('🔍 Proactive connection check timed out - device likely disconnected');
      _handleConnectionStateChange('disconnected');
    } catch (e) {
      // Any other error suggests disconnection
      debugPrint('🔍 Proactive connection check error: $e');
      _handleConnectionStateChange('disconnected');
    }
  }

  /// Handle connection state changes and store them
  Future<void> _handleConnectionStateChange(String newState) async {
    if (!_isMonitoring || _currentSessionId == null) return;
    
    // Only process if state actually changed
    if (_lastConnectionState == newState) return;
    
    _lastConnectionState = newState;
    
    try {
      final connectionState = DeviceConnectionStateModel(
        status: newState,
        sessionId: _currentSessionId!,
        timestamp: DateTime.now(),
      );
      
      // Save connection state locally
      await _localDataSource.saveDeviceConnectionState(connectionState);
      
      // Emit to stream
      _connectionStateController.add(connectionState);
      
      debugPrint('📡 Device connection state changed to: $newState for session: $_currentSessionId');
    } catch (e) {
      _logError('Failed to save connection state: $e');
    }
  }

  /// Log error
  void _logError(String message) {
    print('DeviceSnapshotService Error: $message');
    // TODO: Implement proper logging framework
  }

  /// Dispose resources
  void dispose() {
    stopMonitoring();
    _snapshotController.close();
    _deviceLogController.close();
    _connectionStateController.close();
  }
}
