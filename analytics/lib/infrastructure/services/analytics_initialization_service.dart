import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../analytics_facade_impl.dart';
import '../datasources/local_telemetry_datasource.dart';
import '../../domain/facade/analytics_facade.dart';

@LazySingleton()
class AnalyticsInitializationService {
  // final SessionStateManager _sessionStateManager;
  final IAnalyticsFacade _analyticsFacade;
  final ILocalTelemetryDataSource _localDataSource;
  final SharedPreferences _sharedPreferences;
  final RemoteControlFacade _remoteControlFacade;

  static const String _lastInitKey = 'analytics_last_init';
  static const String _appVersionKey = 'analytics_app_version';

  bool _isInitialized = false;
  StreamSubscription? _settingChangeSubscription;

  AnalyticsInitializationService(
    // this._sessionStateManager,
    this._analyticsFacade,
    this._localDataSource,
    this._sharedPreferences,
    this._remoteControlFacade,
  );

  /// Initialize all analytics services on app startup
  Future<void> initializeOnAppStart() async {
    if (_isInitialized) return;

    try {
      debugPrint('🚀 Initializing Analytics Services...');

      // Record initialization timestamp
      await _recordInitialization();

      // Initialize all services
      await _initializeServices();

      // Restore any active session state
      await _restoreSessionState();

      // Clean up old data
      await _performCleanup();

      // Sync any unsynced sessions to cloud
      await _syncUnsyncedData();

      _isInitialized = true;
      debugPrint('✅ Analytics Services Initialized Successfully');
    } catch (e) {
      debugPrint('❌ Failed to initialize analytics services: $e');
      rethrow;
    }
  }

  /// Initialize individual services
  Future<void> _initializeServices() async {
    // Initialize analytics facade dependencies (resolve circular dependency)
    if (_analyticsFacade is AnalyticsFacadeImpl) {
      await (_analyticsFacade as AnalyticsFacadeImpl).initializeDependencies();
    }

    // Set up setting change event listener from RemoteControlFacade
    _settingChangeSubscription =
        _remoteControlFacade.settingChangeStream.listen((event) {
      // Forward setting change events to analytics
      _analyticsFacade.logSettingChange(event: event);
      debugPrint(
          '📊 Setting change event forwarded to analytics: ${event.eventType}');
    });

    // Initialize session storage manager (handles sync on startup)
    debugPrint('✅ SessionStorageManager initialized');

    // Restore session state manager
    // await _sessionStateManager.initialize();
    debugPrint('✅ SessionStateManager initialized');
  }

  /// Sync unsynced telemetry data (events, heat/tens settings)
  Future<void> _syncUnsyncedData() async {
    try {
      // Get unsynced telemetry events
      final events = await _localDataSource.getEvents();
      final unsyncedEvents = events.where((e) => !e.isSyncedToCloud).toList();

      if (unsyncedEvents.isNotEmpty) {
        debugPrint('📤 Found ${unsyncedEvents.length} unsynced events ');

        // Use analytics facade to sync all data
        final result = await _analyticsFacade.syncSessionsToCloud();

        result.mapBoth(
          onLeft: (error) =>
              debugPrint('❌ Failed to sync unsynced data: $error'),
          onRight: (_) => debugPrint('✅ Successfully synced all unsynced data'),
        );
      }
    } catch (e) {
      debugPrint('❌ Error syncing unsynced data: $e');
    }
  }

  /// Restore session state if app was closed during an active session
  Future<void> _restoreSessionState() async {
    try {
      // Check if there was an active session when app was closed
      debugPrint('✅ Session state restored');
    } catch (e) {
      debugPrint('⚠️ No session state to restore or error restoring: $e');
    }
  }

  /// Perform cleanup of old data
  Future<void> _performCleanup() async {
    try {
      // Clean up old sessions (keep only latest 5)
      await _localDataSource.clearOldSessions(keepCount: 5);

      // Clean up old telemetry events (keep only last 100)
      final events = await _localDataSource.getEvents();
      if (events.length > 100) {
        // This would require implementing a cleanup method in the data source
        debugPrint(
            '⚠️ ${events.length} telemetry events stored - consider cleanup');
      }

      debugPrint('✅ Data cleanup completed');
    } catch (e) {
      debugPrint('⚠️ Error during cleanup: $e');
    }
  }

  /// Record initialization timestamp and app version
  Future<void> _recordInitialization() async {
    final now = DateTime.now().toIso8601String();
    await _sharedPreferences.setString(_lastInitKey, now);

    // You can add app version tracking here if needed
    // await _sharedPreferences.setString(_appVersionKey, appVersion);
  }

  /// Get initialization statistics
  Future<Map<String, dynamic>> getInitializationStats() async {
    final lastInit = _sharedPreferences.getString(_lastInitKey);

    return {
      'isInitialized': _isInitialized,
      'lastInitialization': lastInit,
    };
  }

  /// Force re-initialization (for testing or recovery)
  Future<void> forceReinitialize() async {
    _isInitialized = false;
    await initializeOnAppStart();
  }

  /// Check if analytics services are properly initialized
  bool get isInitialized => _isInitialized;

  /// Dispose resources
  void dispose() {
    _settingChangeSubscription?.cancel();
  }
}
