import 'dart:async';
import 'dart:convert';
import 'package:analytics/analytics.dart';
import 'package:analytics/domain/models/device_connection_state_model.dart';
import 'package:analytics/domain/models/device_settings_model.dart';
import 'package:analytics/domain/models/device_snapshot_model.dart';
import 'package:analytics/domain/models/therapy_session_event_model.dart';
import 'package:analytics/domain/models/therapy_session_info_model.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/model/battery_level_model.dart';
import 'package:remote/domain/model/device_info_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import '../../domain/models/therapy_session_model.dart';
import '../datasources/local_telemetry_datasource.dart';
import '../datasources/cloud_telemetry_datasource.dart';

@LazySingleton()
class SessionStateManager {
  final ILocalTelemetryDataSource _localDataSource;
  final ICloudTelemetryDataSource _cloudDataSource;
  final RemoteControlFacade _remoteControlFacade;
  final SharedPreferences _sharedPreferences;
  DeviceSnapshotService? _deviceSnapshotService;
  String? _sessionId;
  String? get sessionId => _sessionId;
  final Uuid _uuid = Uuid();

  // Constants
  static const Duration _maxSessionDuration =
      Duration(minutes: 120); // Changed from 2 hours to 5 minutes for testing
  static const Duration _sessionWarningDuration =
      Duration(minutes: 110); // 1 minute before max (changed from 110 minutes)
  static const String _sessionStateKey = 'session_state';

  // State
  TherapySessionModel? _currentSession;
  Timer? _sessionDurationTimer;
  Timer? _sessionWarningTimer;
  Timer? _sessionAutoEndTimer;
  bool _isSessionActive = false;
  DateTime? _sessionStartTime;
  DateTime? _lastActivityTime;
  bool _isInitialized = false;

  // Stream controllers
  final _sessionStateController =
      StreamController<TherapySessionModel?>.broadcast();
  final _sessionWarningController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _sessionDurationController = StreamController<Duration>.broadcast();

  SessionStateManager(
    this._localDataSource,
    this._cloudDataSource,
    this._remoteControlFacade,
    this._sharedPreferences,
  );

  // Set the device snapshot service (called after DI setup to avoid circular dependency)
  void setDeviceSnapshotService(DeviceSnapshotService deviceSnapshotService) {
    _deviceSnapshotService = deviceSnapshotService;
  }

  // Getters
  Stream<TherapySessionModel?> get sessionStateStream =>
      _sessionStateController.stream;
  Stream<Map<String, dynamic>> get sessionWarningStream =>
      _sessionWarningController.stream;
  Stream<Duration> get sessionDurationStream =>
      _sessionDurationController.stream;
  TherapySessionModel? get currentSession => _currentSession;
  bool get isSessionActive => _isSessionActive;
  Duration? get sessionDuration => _sessionStartTime != null
      ? DateTime.now().difference(_sessionStartTime!)
      : null;
  Duration? get remainingTime => _sessionStartTime != null
      ? _maxSessionDuration - DateTime.now().difference(_sessionStartTime!)
      : null;

  /// Initialize session state manager
  Future<void> initialize() async {
    if (_isInitialized) return;
    await _restoreSessionState();
    _isInitialized = true;
  }

  /// Start a new therapy session
  Future<String> startSession() async {
    try {
      // End any existing session first
      debugPrint('🔄 Starting new session...');
      if (_isSessionActive) {
        await endSession();
      }
      final now = DateTime.now();
      final sessionId = _uuid.v4();
      _sessionId = sessionId; // Assign to the instance variable
      final deviceData = await _getDeviceInfoAndSettings();
      final deviceInfo = deviceData['deviceInfo'] as DeviceInfoModel;
      final initialSettings =
          deviceData['deviceSettings'] as DeviceSettingsModel;
      _currentSession = TherapySessionModel.startNewSession(
        sessionInfo: TherapySessionInfoModel(
          sessionId: sessionId,
          therapyStartTime: now,
        ),
        deviceInformation: deviceInfo,
        initialSettings: initialSettings,
      );
      _isSessionActive = true;
      _sessionStartTime = now;
      _lastActivityTime = now;

      // Save session state
      await _saveSessionState();

      // Start session timers
      _startSessionTimers();

      // Initialize device monitoring services
      try {
        _deviceSnapshotService?.startMonitoring(sessionId);
        debugPrint(
            '✅ Device monitoring services initialized for session: $sessionId');
      } catch (e) {
        debugPrint('⚠️ Failed to initialize device monitoring services: $e');
      }

      // Log session start
      TherapySessionEventModel sessionStartEvent = TherapySessionEventModel(
        eventId: _uuid.v4(),
        sessionId: sessionId,
        eventType: 'session_started',
        timestamp: now,
        eventData: {
          'deviceId': deviceInfo.deviceId,
          'initialSettings': initialSettings.toLocalJson(),
          'timeStamp': now.toIso8601String(),
        },
        isDeviceInitiated: false,
      );
      await _localDataSource.saveEvent(sessionStartEvent);
      _sessionStateController.add(_currentSession);
      print('🟢 Session started: $sessionId');
      // _initializeMonitoringServices(sessionId);
      return sessionId;
    } catch (e, stack) {
      debugPrint('❌ Error starting session: $e\n$stack');
      rethrow;
    }
  }

  /// End the current therapy session
  Future<void> endSession() async {
    if (!_isSessionActive || _currentSession == null) return;

    final now = DateTime.now();
    final duration = now.difference(_sessionStartTime!);

    // Stop session timers and monitoring services
    _stopSessionTimers();

    try {
      _deviceSnapshotService?.stopMonitoring();
      debugPrint('✅ Device monitoring services stopped');
    } catch (e) {
      debugPrint('⚠️ Failed to stop device monitoring services: $e');
    }

    final deviceData = await _getDeviceInfoAndSettings();
    DeviceSettingsModel finalSettings = deviceData['deviceSettings'];
    await _logSessionEndEvent(now, duration.inMinutes, finalSettings);
    final sessionEvents = await _collectSessionEvents();
    //collect all session snapshots and connectionEvents

    final sessionSnapshots = await _collectSessionSnapshots();
    final connectionEvents = await _collectSessionConnectionEvents();
    _logSessionEventCount(sessionEvents.length);
    _updateCurrentSession(
        now, finalSettings, sessionEvents, sessionSnapshots, connectionEvents);

    await _persistFinalSession();

    await _syncSessionToCloud(_currentSession!);

    await _clearSessionEvents(_currentSession!.sessionInfo.sessionId);

    // Clear session snapshots and connection events
    await _localDataSource.clearAllDeviceSnapshots();
    await _localDataSource.clearAllDeviceConnectionStates();

    await _cleanupSession();
  }

  /// Get device info and initial settings
  Future<Map<String, dynamic>> _getDeviceInfoAndSettings() async {
    final deviceData = await _remoteControlFacade.getDeviceInformation();
    final deviceModel = deviceData.getOrNull();

    DeviceSettingsModel initialSettings = DeviceSettingsModel(
      heatLevel: deviceModel?.heatLevel?.actualHeatLevel ?? 0,
      tensLevel: deviceModel?.tensLevel?.actualTensLevel ?? 0,
      tensMode: deviceModel?.tensLevel?.mode ?? 0,
      batteryLevel: deviceModel?.deviceInfo.batteryLevel?.batteryLevel ?? 0,
      timestamp: DateTime.now(),
    );
    DeviceInfoModel deviceInfo = deviceModel?.deviceInfo ??
        DeviceInfoModel(
          deviceId: 'unknown',
          deviceName: 'unknown',
          batteryLevel: BatteryLevelModel(batteryLevel: 0),
        );

    return {
      'deviceInfo': deviceInfo,
      'deviceSettings': initialSettings,
    };
  }

  Future<List<TherapySessionEventModel>> _collectSessionEvents() async {
    final allLocalEvents = await _localDataSource.getEvents();
    return allLocalEvents
        .where((event) =>
            event.sessionId == _currentSession!.sessionInfo.sessionId)
        .toList();
  }

  Future<List<DeviceSnapshotModel>> _collectSessionSnapshots() async {
    final allSnapshots = await _localDataSource.getDeviceSnapshots();
    return allSnapshots
        .where((snapshot) =>
            snapshot.sessionId == _currentSession!.sessionInfo.sessionId)
        .toList();
  }

  Future<List<DeviceConnectionStateModel>>
      _collectSessionConnectionEvents() async {
    final allConnectionEvents =
        await _localDataSource.getDeviceConnectionStates();
    return allConnectionEvents
        .where((event) =>
            event.sessionId == _currentSession!.sessionInfo.sessionId)
        .toList();
  }

  void _logSessionEventCount(int count) {
    debugPrint(
        '📊 Collecting $count events for session: ${_currentSession!.sessionInfo.sessionId}');
  }

  void _updateCurrentSession(
      DateTime now,
      DeviceSettingsModel? finalSettings,
      List<TherapySessionEventModel> sessionEvents,
      List<DeviceSnapshotModel> sessionSnapshots,
      List<DeviceConnectionStateModel> connectionEvents) {
    _currentSession = _currentSession!.copyWith(
      finalSettings: finalSettings ?? _currentSession!.finalSettings,
      sessionInfo: TherapySessionInfoModel(
        sessionId: _currentSession!.sessionInfo.sessionId,
        therapyStartTime: _currentSession!.sessionInfo.therapyStartTime,
        therapyEndTime: now,
      ),
      deviceSnapshot: sessionSnapshots,
      deviceConnectionStates: connectionEvents,
      sessionEvents: [..._currentSession!.sessionEvents, ...sessionEvents],
      status: 'completed',
    );
  }

  Future<void> _persistFinalSession() async {
    await _localDataSource.saveTherapySession(_currentSession!);
  }

  Future<void> _logSessionEndEvent(
      DateTime now, int duration, DeviceSettingsModel? finalSettings) async {
    TherapySessionEventModel sessionEndEvent = TherapySessionEventModel(
      eventId: _uuid.v4(),
      sessionId: _currentSession!.sessionInfo.sessionId,
      eventType: 'session_ended',
      timestamp: now,
      eventData: {
        'duration': duration,
        'finalSettings': finalSettings?.toLocalJson() ?? {},
      },
      isDeviceInitiated: false,
    );
    await _localDataSource.saveEvent(sessionEndEvent);
  }

  /// Pause the current session
  Future<void> pauseSession() async {
    if (!_isSessionActive || _currentSession == null) return;

    _currentSession = _currentSession!.copyWith(status: 'paused');
    await _saveSessionState();

    // Stop timers but keep session active
    _stopSessionTimers();

    // Log pause event
    TherapySessionEventModel pauseEvent = TherapySessionEventModel(
      eventId: _uuid.v4(),
      sessionId: _sessionId,
      eventType: 'session_paused',
      timestamp: DateTime.now(),
      eventData: {
        'pausedAt': DateTime.now().toIso8601String(),
      },
      isDeviceInitiated: false,
    );

    await _localDataSource.saveEvent(pauseEvent);
    _sessionStateController.add(_currentSession);
  }

  /// Resume the current session
  Future<void> resumeSession() async {
    if (!_isSessionActive || _currentSession == null) return;

    _currentSession = _currentSession!.copyWith(status: 'active');
    await _saveSessionState();

    // Restart session timers
    _startSessionTimers();

    // Log resume event
    TherapySessionEventModel resumeEvent = TherapySessionEventModel(
      eventId: _uuid.v4(),
      sessionId: _sessionId,
      eventType: 'session_resumed',
      timestamp: DateTime.now(),
      eventData: {
        'resumedAt': DateTime.now().toIso8601String(),
      },
      isDeviceInitiated: false,
    );

    await _localDataSource.saveEvent(resumeEvent);
    _sessionStateController.add(_currentSession);
  }

  /// Update last activity time
  Future<void> updateActivity() async {
    _lastActivityTime = DateTime.now();
    await _saveSessionState();
  }

  /// Check if session should be auto-ended due to inactivity
  bool shouldAutoEndSession(
      {Duration inactivityThreshold = const Duration(minutes: 30)}) {
    if (!_isSessionActive || _lastActivityTime == null) return false;

    final inactivityDuration = DateTime.now().difference(_lastActivityTime!);
    return inactivityDuration > inactivityThreshold;
  }

  /// Start session timers
  void _startSessionTimers() {
    // Only start timers if they aren't already running
    if (_sessionDurationTimer != null ||
        _sessionWarningTimer != null ||
        _sessionAutoEndTimer != null) {
      // debugPrint('🔄 Session timers already running, skipping restart');
      return;
    }

    debugPrint('🔄 Starting session timers');

    // Duration timer - updates the session duration stream every second
    _sessionDurationTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (_sessionStartTime != null) {
        final duration = DateTime.now().difference(_sessionStartTime!);
        _sessionDurationController.add(duration);
      }
    });

    // Warning timer - fires 1 minute before max session duration
    _sessionWarningTimer = Timer(_sessionWarningDuration, () {
      final remainingTime = _maxSessionDuration - _sessionWarningDuration;
      _sessionWarningController.add({
        'type': 'session_warning',
        'remainingSeconds': remainingTime.inSeconds,
        'message': 'Session will end in ${remainingTime.inMinutes} minute(s)',
      });
    });

    // Auto end timer - automatically ends the session after max duration
    _sessionAutoEndTimer = Timer(_maxSessionDuration, () async {
      debugPrint('⏰ Session auto-end timer triggered');
      await endSession();
    });
  }

  /// Stop session timers
  void _stopSessionTimers() {
    bool hasActiveTimers = _sessionDurationTimer != null ||
        _sessionWarningTimer != null ||
        _sessionAutoEndTimer != null;

    if (hasActiveTimers) {
      debugPrint('⏹️ Stopping session timers');
    }

    _sessionDurationTimer?.cancel();
    _sessionWarningTimer?.cancel();
    _sessionAutoEndTimer?.cancel();
    _sessionDurationTimer = null;
    _sessionWarningTimer = null;
    _sessionAutoEndTimer = null;
  }

  /// Save session state to SharedPreferences
  Future<void> _saveSessionState() async {
    try {
      final state = {
        'currentSession': _currentSession?.toLocalJson(),
        'isSessionActive': _isSessionActive,
        'sessionStartTime': _sessionStartTime?.toIso8601String(),
        'lastActivityTime': _lastActivityTime?.toIso8601String(),
      };

      final stateJson = jsonEncode(state);
      await _sharedPreferences.setString(_sessionStateKey, stateJson);
    } catch (e) {
      // Silently handle storage errors
    }
  }

  /// Restore session state from SharedPreferences
  Future<void> _restoreSessionState() async {
    try {
      final stateJson = _sharedPreferences.getString(_sessionStateKey);
      if (stateJson == null) return;

      final state = jsonDecode(stateJson) as Map<String, dynamic>;

      _isSessionActive = state['isSessionActive'] ?? false;
      _sessionStartTime = state['sessionStartTime'] != null
          ? DateTime.parse(state['sessionStartTime'])
          : null;
      _lastActivityTime = state['lastActivityTime'] != null
          ? DateTime.parse(state['lastActivityTime'])
          : null;

      if (state['currentSession'] != null) {
        _currentSession =
            TherapySessionModel.fromLocalJson(state['currentSession']);
        _sessionId =
            _currentSession?.sessionInfo.sessionId; // Restore session ID
      }

      // Check if session should be auto-ended due to time or inactivity
      if (_isSessionActive && _sessionStartTime != null) {
        final sessionAge = DateTime.now().difference(_sessionStartTime!);

        if (sessionAge > _maxSessionDuration || shouldAutoEndSession()) {
          // Auto-end expired or inactive session
          await endSession();
        } else {
          // Resume session monitoring
          _startSessionTimers();
          _sessionStateController.add(_currentSession);
        }
      }
    } catch (e) {
      // Clear invalid state
      await _clearSessionState();
    }
  }

  /// Clear session state
  Future<void> _clearSessionState() async {
    try {
      await _sharedPreferences.remove(_sessionStateKey);
    } catch (e) {
      // Silently handle storage errors
    }
  }

  /// Sync a completed session to cloud
  Future<void> _syncSessionToCloud(TherapySessionModel session) async {
    try {
      debugPrint(
          '🔄 SessionStateManager: Auto-syncing completed session to cloud: ${session.sessionInfo.sessionId}');

      final cloudSuccess = await _cloudDataSource.saveTherapySession(session);
      if (cloudSuccess) {
        await _localDataSource.markSessionSynced(session.sessionInfo.sessionId);
        debugPrint(
            '✅ SessionStateManager: Session synced to cloud: ${session.sessionInfo.sessionId}');
      } else {
        debugPrint(
            '⚠️ SessionStateManager: Failed to sync session to cloud: ${session.sessionInfo.sessionId}');
      }
    } catch (e) {
      debugPrint('❌ SessionStateManager: Error syncing session to cloud: $e');
    }
  }

  /// Clear local events for a specific session
  Future<void> _clearSessionEvents(String sessionId) async {
    try {
      final allEvents = await _localDataSource.getEvents();
      final eventsToKeep =
          allEvents.where((event) => event.sessionId != sessionId).toList();

      // Clear all events and save only the ones to keep
      await _localDataSource.clearAllEvents();
      for (final event in eventsToKeep) {
        await _localDataSource.saveEvent(event);
      }

      debugPrint(
          '🧹 Cleared ${allEvents.length - eventsToKeep.length} events for session: $sessionId');
    } catch (e) {
      debugPrint('❌ Error clearing session events: $e');
    }
  }

  /// Clean up session
  Future<void> _cleanupSession() async {
    // Stop all session timers
    _stopSessionTimers();

    // Reset session state
    _isSessionActive = false;
    _sessionStartTime = null;
    _lastActivityTime = null;
    _currentSession = null;
    _sessionId = null;

    // Clear session state
    await _clearSessionState();

    // Keep only the latest 5 sessions
    await _localDataSource.clearOldSessions(keepCount: 5);

    // Update session state stream
    _sessionStateController.add(null);
  }

  /// Get session statistics
  Map<String, dynamic> getSessionStats() {
    if (!_isSessionActive || _sessionStartTime == null) {
      return {
        'isActive': false,
        'duration': 0,
        'remainingTime': 0,
      };
    }

    final duration = DateTime.now().difference(_sessionStartTime!);
    final remaining = _maxSessionDuration - duration;

    return {
      'isActive': _isSessionActive,
      'sessionId': _currentSession?..sessionInfo.sessionId,
      'duration': duration.inSeconds,
      'remainingTime':
          remaining.inSeconds.clamp(0, _maxSessionDuration.inSeconds),
      'status': _currentSession?.status,
      'startTime': _sessionStartTime?.toIso8601String(),
      'lastActivity': _lastActivityTime?.toIso8601String(),
    };
  }

  /// Dispose resources
  void dispose() {
    _stopSessionTimers();
    _sessionStateController.close();
    _sessionWarningController.close();
    _sessionDurationController.close();
  }
}
