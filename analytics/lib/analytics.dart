// Application layer exports
export 'application/analytics_initialization_bloc/analytics_initialization_bloc.dart';

// Domain layer exports
export 'domain/facade/analytics_facade.dart';
export 'domain/models/therapy_session_model.dart';

// Infrastructure layer exports
export 'infrastructure/analytics_facade_impl.dart';
export 'infrastructure/datasources/cloud_telemetry_datasource.dart';
export 'infrastructure/datasources/local_telemetry_datasource.dart';

// Services exports
export 'infrastructure/services/device_snapshot_service.dart';
export 'infrastructure/services/session_state_manager.dart';
export 'infrastructure/services/analytics_initialization_service.dart';

// Dependency injection
export 'di/di.dart';

// Initialize function to bootstrap the analytics package
import 'package:firebase_core/firebase_core.dart';
import 'di/di.dart';

/// Initialize the analytics package
/// This should be called before using any analytics functionality
Future<void> initializeAnalytics() async {
  // Ensure Firebase is initialized
  await Firebase.initializeApp();

  // Configure dependency injection
  configureDependencies(env: 'prod');
}
