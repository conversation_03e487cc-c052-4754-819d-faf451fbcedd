buildscript {
    ext.kotlin_version = '1.9.22' // Keep this updated, '2.0.0' for Kotlin is very new, 1.9.22 is more stable for Android right now
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // Correct classpath for the Android Gradle Plugin (AGP)
        // Use a recent stable version, e.g., 8.4.1. Avoid 8.5.0 for now, as it's just released and might have quirks.
        classpath 'com.android.tools.build:gradle:8.4.1'
        // Correct classpath for the Kotlin Gradle Plugin
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // Correct classpath for the Google Services plugin
        classpath 'com.google.gms:google-services:4.4.1' // 4.4.2 is also fine if it came from recent FlutterFire docs, 4.4.1 is very common.
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// Configure build directories (keep these as they are)
rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

// Define dependencies for the ':app' project if needed (keep these as they are)
subprojects {
    project.evaluationDependsOn(':app')
}

// Clean task to delete the build directory (keep this as it is)
tasks.register("clean", Delete) {
    delete rootProject.buildDir
}