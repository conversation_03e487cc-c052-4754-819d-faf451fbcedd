import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:account_management/domain/model/therapy_feedback_model.dart';

class TherapyFeedbackDialog extends StatefulWidget {
  final String therapyId;

  const TherapyFeedbackDialog({
    Key? key,
    required this.therapyId,
  }) : super(key: key);

  @override
  State<TherapyFeedbackDialog> createState() => _TherapyFeedbackDialogState();
}

class _TherapyFeedbackDialogState extends State<TherapyFeedbackDialog> {
  int _painLevelBefore = 5;
  int _painLevelAfter = 5;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'How was your therapy?',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20.h),
            Text(
              'Pain level before therapy:',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 10.h),
            _buildPainSlider(_painLevelBefore, (value) {
              setState(() {
                _painLevelBefore = value;
              });
            }),
            SizedBox(height: 20.h),
            Text(
              'Pain level after therapy:',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 10.h),
            _buildPainSlider(_painLevelAfter, (value) {
              setState(() {
                _painLevelAfter = value;
              });
            }),
            SizedBox(height: 30.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('Skip'),
                ),
                ElevatedButton(
                  onPressed: () {
                    _submitFeedback(context);
                  },
                  child: Text('Submit'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPainSlider(int value, Function(int) onChanged) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('No Pain'),
            Text('Worst Pain'),
          ],
        ),
        Slider(
          value: value.toDouble(),
          min: 0,
          max: 10,
          divisions: 10,
          label: value.toString(),
          onChanged: (newValue) {
            onChanged(newValue.toInt());
          },
        ),
        Text(
          '$value',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  void _submitFeedback(BuildContext context) {
    // Create a feedback model
    final feedback = TherapyFeedbackModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      therapyId: widget.therapyId,
      painLevelBefore: _painLevelBefore,
      painLevelAfter: _painLevelAfter,
      createdAt: null, // This will be set in the repository
    );



    Navigator.of(context).pop();
  }
}
