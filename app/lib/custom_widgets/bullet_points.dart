import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// The BulletPoint class is a StatelessWidget that displays a bullet point with a number and text.
class BulletPoint extends StatelessWidget {
  final int number;
  final String text;

  BulletPoint({required this.number, required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: 15,
          backgroundColor: AppTheme.primaryColor,
          child: Text(
            number.toString(),
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 18,
            ),
          ),
        ),
        SizedBox(width: 10),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.poppins(
              fontSize: 14,
               color: Colors.black,

            ),
          ),
        ),
      ],
    );
  }
}