import 'package:another_xlider/another_xlider.dart';
import 'package:another_xlider/models/handler.dart';
import 'package:another_xlider/models/slider_step.dart';
import 'package:another_xlider/models/tooltip/tooltip.dart';
import 'package:another_xlider/models/tooltip/tooltip_position_offset.dart';
import 'package:another_xlider/models/trackbar.dart';
import 'package:flutter/material.dart';

class EmojiSlider extends StatefulWidget {
  final List<String> emojis;
  final List<String> labels;
  final double currentValue;
  final double minValue;
  final double maxValue;
  final dynamic Function(int, dynamic, dynamic)? onChanged;

  const EmojiSlider({
    Key? key,
    required this.emojis,
    required this.currentValue,
    required this.minValue,
    required this.maxValue,
    required this.labels,
    required this.onChanged,
  }) : super(key: key);

  @override
  _EmojiSliderState createState() => _EmojiSliderState();
}

class _EmojiSliderState extends State<EmojiSlider> {
  late double _sliderValue;



  @override
  Widget build(BuildContext context) {
    _sliderValue = widget.currentValue;
    return FlutterSlider(
      values: [_sliderValue],
      max: widget.maxValue,
      min: widget.minValue,
      handlerHeight: 60,


      handler: FlutterSliderHandler(
        decoration: const BoxDecoration(
        ),
        child: Container(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            widget.emojis[_sliderValue.toInt()],
            style: const TextStyle(fontSize: 30),
          ),
        ),
      ),
      trackBar: FlutterSliderTrackBar(
        activeTrackBar: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(4)),
        inactiveTrackBar: BoxDecoration(color: Theme.of(context).primaryColor.withOpacity(0.5), borderRadius: BorderRadius.circular(4)),
      ),
      tooltip: FlutterSliderTooltip(
        positionOffset:  FlutterSliderTooltipPositionOffset(top: -10,left: 5),
        custom: (value) {
          return  Stack(
            alignment: AlignmentDirectional
                .bottomCenter,
            children: [
              Container(
                margin: EdgeInsets.zero,
                height: 30,
                width: 100,
                decoration: BoxDecoration(
                  borderRadius:
                  BorderRadius.circular(32),
                  color: Colors.transparent,
                ),
              ),
              Container(
                alignment: Alignment.center,
                margin:
                EdgeInsets.only(left: 10),
                height: 30,
                width: 100,
                decoration: BoxDecoration(
                  borderRadius:
                  BorderRadius.circular(32),
                  color: Color.fromRGBO(
                      207, 204, 235, 1.0),
                ),
                child: Text(
                  widget.labels[(value as double).toInt()],
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color.fromRGBO(
                        48, 40, 93, 1),
                  ),
                ),
              ),
              Container(
                margin:
                EdgeInsets.only(right: 15),
                height: 20,
                width: 20,
                child: Icon(
                  size: 40,
                  Icons
                      .arrow_drop_down_outlined,
                  color: Color.fromRGBO(
                      207, 204, 235, 1.0),
                ),
              )
            ],
          );

        },
        alwaysShowTooltip: true,
        textStyle: const TextStyle(fontSize: 17, color: Colors.black),
      ),
      step: FlutterSliderStep(
        step: 1,
        isPercentRange: false,
      ),
      onDragging: (handlerIndex, lowerValue, upperValue) {
        setState(() {
          if (widget.onChanged != null) {
            widget.onChanged!(handlerIndex, lowerValue, upperValue);
          }
          _sliderValue = lowerValue as double;
        });
      },

    );
  }
}
