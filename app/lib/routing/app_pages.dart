import 'package:auto_route/auto_route.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Page,Route')
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
        /// routes go here
        AutoRoute(page: SplashRoute.page, initial: true),
        AutoRoute(page: WelcomeRoute.page),
        AutoRoute(page: LoginRoute.page),
        AutoRoute(page: RemoteRoute.page),
        AutoRoute(page: SettingsRoute.page),
        AutoRoute(page: HomeRoute.page),
        AutoRoute(page: PairDeviceRoute.page),
        AutoRoute(page: SignUpRoute.page),
        AutoRoute(page: ResetPasswordRoute.page),
        AutoRoute(page: TroubleshootingRoute.page),
        AutoRoute(page: MedicationRoute.page),
        AutoRoute(page: HelpCenterHomeRoute.page),
        CustomRoute(
            page: NotificationsRoute.page,
            transitionsBuilder: TransitionsBuilders.slideLeft,
            durationInMilliseconds: 200,
            reverseDurationInMilliseconds: 300),
        AutoRoute(page: ProfilePictureRoute.page),
        AutoRoute(page: ProfileRoute.page),
        AutoRoute(page: GetStartedRoute.page),
        AutoRoute(page: RemoteOneRoute.page),
        AutoRoute(page: EmailVerificationRoute.page),
        AutoRoute(page: ExtendedCalenderRoute.page),
        AutoRoute(page: PeriodTrackingCalendarRoute.page),
        AutoRoute(page: BarChartRoute.page),
        AutoRoute(page: DashboardRoute.page),
        // Add therapy feedback route with path for deep linking
        // AutoRoute(page: TherapyFeedbackRoute.page, path: '/therapy_feedback'),
      ];
}
