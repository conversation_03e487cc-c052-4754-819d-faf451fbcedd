// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../../widgets/therapy_feedback_dialog.dart';
// import '../../custom_widgets/curved_app_bar.dart';
// import '../../helpers.dart';
//
// @RoutePage()
// class TherapyFeedbackPage extends StatefulWidget {
//   final String? therapyId;
//
//   const TherapyFeedbackPage({Key? key, this.therapyId}) : super(key: key);
//
//   @override
//   State<TherapyFeedbackPage> createState() => _TherapyFeedbackPageState();
// }
//
// class _TherapyFeedbackPageState extends State<TherapyFeedbackPage> {
//   String? _therapyId;
//   bool _isLoading = true;
//
//   @override
//   void initState() {
//     super.initState();
//     _loadTherapyId();
//   }
//
//   Future<void> _loadTherapyId() async {
//     if (widget.therapyId != null) {
//       setState(() {
//         _therapyId = widget.therapyId;
//         _isLoading = false;
//       });
//     } else {
//       // Try to get therapy ID from SharedPreferences
//       final prefs = await SharedPreferences.getInstance();
//       final therapyId = prefs.getString('last_therapy_id');
//
//       setState(() {
//         _therapyId = therapyId ?? 'unknown';
//         _isLoading = false;
//       });
//     }
//
//     // Show the feedback dialog after UI is built
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       _showFeedbackDialog();
//     });
//   }
//
//   void _showFeedbackDialog() {
//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (context) => BlocProvider.value(
//         value: BlocProvider.of<TherapyManagementBloc>(context),
//         child: TherapyFeedbackDialog(therapyId: _therapyId ?? 'unknown'),
//       ),
//     ).then((_) {
//       // After dialog is closed, navigate back if appropriate
//       if (widget.therapyId == null) {
//         context.router.pop();
//       }
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocProvider(
//       create: (context) => getIt<TherapyManagementBloc>(),
//       child: BlocListener<TherapyManagementBloc, TherapyManagementState>(
//         listener: (context, state) {
//           state.maybeMap(
//             feedbackLoggedSuccessfully: (_) {
//               ScaffoldMessenger.of(context).showSnackBar(
//                 SnackBar(content: Text('Feedback submitted successfully')),
//               );
//
//               // Small delay before popping to show snackbar
//               Future.delayed(Duration(seconds: 2), () {
//                 if (context.mounted) {
//                   context.router.pop();
//                 }
//               });
//             },
//             error: (errorState) {
//               ScaffoldMessenger.of(context).showSnackBar(
//                 SnackBar(content: Text(errorState.message)),
//               );
//             },
//             orElse: () {},
//           );
//         },
//         child: Scaffold(
//           appBar: CurvedAppBar(
//             // title: 'Therapy Feedback',
//             topLeftIcon: IconButton(
//               icon: Icon(Icons.arrow_back_rounded, color: Colors.white),
//               onPressed: () => context.router.pop(),
//             ),
//           ),
//           body: _isLoading
//               ? Center(child: CircularProgressIndicator())
//               : Center(
//                   child: Padding(
//                     padding: EdgeInsets.all(20.r),
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Text(
//                           'Thank you for your therapy session!',
//                           style: TextStyle(
//                             fontSize: 22.sp,
//                             fontWeight: FontWeight.bold,
//                           ),
//                           textAlign: TextAlign.center,
//                         ),
//                         SizedBox(height: 20.h),
//                         Text(
//                           'We appreciate your feedback to help improve our services.',
//                           style: TextStyle(fontSize: 16.sp),
//                           textAlign: TextAlign.center,
//                         ),
//                         SizedBox(height: 40.h),
//                         ElevatedButton(
//                           onPressed: _showFeedbackDialog,
//                           child: Text('Provide Feedback'),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//         ),
//       ),
//     );
//   }
// }