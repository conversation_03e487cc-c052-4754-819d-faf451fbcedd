import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:help_center/application/help_center_faq_bloc/help_center_faq_bloc.dart';

class HelpCenterFaqPage extends StatelessWidget {
  const HelpCenterFaqPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HelpCenterFaqBloc, HelpCenterFaqState>(
      listener: (context, state) {
        // Listener for state changes in the Help Center FAQ service
      },
      builder: (context, state) {
        // Builder for the UI based on the current state
        if (state is LoadingFaqs) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (state is SuccessFaqs) {
          return ListView.builder(
              shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: state.faqs.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: buildExpansionTile(
                  context,
                  state.faqs[index]!.question!,
                  state.faqs[index]!.answer!,
                ),
              );}
          );
        } else if (state is FailureFaqs) {
          return Center(
            child: Text(state.failure.toString()),
          );
        } else {
          return const Center(
            child: Text('Something went wrong'),
          );
        }
      },
    );
  }
}
Widget buildExpansionTile(BuildContext context, String title, String answer) {
  return Container(
    constraints: BoxConstraints(minHeight: 80),
    padding: EdgeInsets.all(7),
    decoration: BoxDecoration(
      boxShadow: [
        BoxShadow(
          color: Color(0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
          blurRadius: 4.0, // the blur radius
          offset: Offset(0, 1), // the x,y offset of the shadow
        ),
      ],
      color: Color(0xffFAF2DF),
      borderRadius: BorderRadius.circular(15),
    ),
    child: Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: ExpansionTile(
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
            fontSize: 16,
            color: Color(0xff26204a),
            fontWeight: FontWeight.w500,
          ),
        ),
        children: [
          Text(
            answer,
            style: TextStyle(color: Colors.black),
          ),
        ],
      ),
    ),
  );
}
