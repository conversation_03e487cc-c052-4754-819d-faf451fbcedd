import 'package:carousel_slider_x/carousel_slider_x.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:help_center/application/help_center_videos_bloc/help_center_videos_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class HelpCenterVideosPage extends StatefulWidget {
  const HelpCenterVideosPage({super.key});

  @override
  State<HelpCenterVideosPage> createState() => _HelpCenterVideosPageState();
}

class _HelpCenterVideosPageState extends State<HelpCenterVideosPage> {
  final CarouselControllerX _carouselController = CarouselControllerX();
  bool isEarphonesConnected = false;
  List<VideoPlayerController> _videoControllers = [];
  List<ChewieController> _chewieControllers = [];
  int _current = 0;

  @override
  void dispose() {
    for (var controller in _videoControllers) {
      controller.dispose();
    }
    for (var controller in _chewieControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  Widget buildIndicatorList(int videoListLength) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(videoListLength, (index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _current = index;
              _carouselController.jumpToPage(index);
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5.0),
            child: Container(
              height: 3,
              width: 20,
              color: _current == index ? AppTheme.primaryColor : Colors.grey,
            ),
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HelpCenterVideosBloc, HelpCenterVideosState>(
      builder: (context, state) {
        if (state is LoadingVideos) {
          return Skeleton.leaf(
            child: Container(
              height: 0.95.sw,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(32),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color.fromRGBO(255, 247, 238, 1),
                    Color.fromRGBO(207, 204, 235, 1),
                  ],
                ),
              ),
            ),
          );
        } else if (state is SuccessVideos) {
          return Column(
            children: [
              Container(
                clipBehavior: Clip.hardEdge,
                height: 0.95.sw,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(32),
                  color: Colors.black,
                ),
                child: CarouselSlider.builder(
                  carouselController: _carouselController,
                  options: CarouselOptions(
                    viewportFraction: 1,
                    height: 0.4.sh,
                    autoPlay: false,
                  ),
                  onPageChanged: (index, reason) {
                    setState(() {
                      _current = index;
                    });
                  },
                  itemCount: state.videos.length,
                  itemBuilder: (context, index, realIdx) {
                    VideoPlayerController videoController = VideoPlayerController.network(state.videos[index]!.url!);
                    _videoControllers.add(videoController);

                    ChewieController chewieController = ChewieController(
                      videoPlayerController: videoController,
                      autoPlay: false,
                      looping: false,
                      fullScreenByDefault: true,
                      controlsSafeAreaMinimum: EdgeInsets.symmetric(vertical: 15.0,horizontal: 5.0),
                      materialProgressColors: ChewieProgressColors(
                        playedColor: AppTheme.primaryColor,
                        handleColor: AppTheme.primaryColor,
                      ),
                    );
                    _chewieControllers.add(chewieController);

                    return FutureBuilder(
                      future: videoController.initialize(),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.done) {
                          return Chewie(controller: chewieController);
                        } else {
                          return Center(child: CircularProgressIndicator());
                        }
                      },
                    );
                  },
                ),
              ),
              SizedBox(height: 10),
              buildIndicatorList(state.videos.length),
            ],
          );
        } else if (state is FailureVideos) {
          return Text('Failed to load videos');
        } else {
          return Container();
        }
      },
    );
  }
}
