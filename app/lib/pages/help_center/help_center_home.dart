import 'package:auto_route/annotations.dart';
import 'package:carousel_slider_x/carousel_slider_x.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:help_center/application/help_center_contact_details_bloc/help_center_contact_details_bloc.dart';
import 'package:help_center/application/help_center_faq_bloc/help_center_faq_bloc.dart';
import 'package:help_center/application/help_center_videos_bloc/help_center_videos_bloc.dart';
import 'package:juno_plus/pages/help_center/help_center_faq.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:video_player/video_player.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'help_center_contact_details.dart';
import 'help_center_videos.dart';
@RoutePage()
class HelpCenterHomePage extends StatefulWidget {
  const HelpCenterHomePage({super.key});

  @override
  State<HelpCenterHomePage> createState() => _HelpCenterHomePageState();
}

class _HelpCenterHomePageState extends State<HelpCenterHomePage> {
  final CarouselControllerX _carouselController = CarouselControllerX();
  bool isEarphonesConnected = false;
  List<VideoPlayerController> _videoControllers = [];
  int _current = 0;

  @override
  void dispose() {
    for (var controller in _videoControllers) {
      controller.dispose();
    }
    super.dispose();
  }
  @override
  void initState() {
    super.initState();
  }
  Widget buildIndicatorList(int videoListLength) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(videoListLength, (index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _current = index;
              _carouselController.jumpToPage(index);
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5.0),
            child: Container(
              height: 3,
              width: 20,
              color: _current == index ? AppTheme.primaryColor : Colors.grey,
            ),
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
  providers: [
    BlocProvider<HelpCenterVideosBloc>(
      create: (context) => getIt<HelpCenterVideosBloc>()..add(const HelpCenterVideosEvent.loadHelpCenterVideos()),
    ),
    BlocProvider<HelpCenterFaqBloc>(
      create: (context) => getIt<HelpCenterFaqBloc>()..add(const HelpCenterFaqEvent.loadHelpCenterFaq()),
    ),
    BlocProvider<HelpCenterContactDetailsBloc>(
      create: (context) => getIt<HelpCenterContactDetailsBloc>()..add(const GetContactDetails()),
    ),
  ],
  child: Scaffold(
          appBar: CurvedAppBar(
            appBarColor: AppTheme.primaryColor,
            logoColor: const Color(0xffFAF2DF),
            height: .35.sw,
            topLeftIcon: Container(
              height: 40,
              width: 40,
              child: IconButton(
                icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ),
            topRightIcon: Container(
              height: 40,
              width: 40,
              decoration: const BoxDecoration(
                color: Color(0xffFAF2DF),
                shape: BoxShape.circle,
              ),

              child: const Padding(
                padding: EdgeInsets.all(6.0),
                child: Icon(Icons.notifications_rounded, color: Color(0xff30285D)),
              ),
            ),
          ),
          body: SingleChildScrollView(
            child: Container(
              margin: EdgeInsets.all(20),
              child: Column(
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Learn About JUNO',
                        style: TextStyle(
                          color: Color.fromRGBO(48, 40, 93, 1),
                          fontSize: 36,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                      SizedBox(height: 10),
                      HelpCenterVideosPage(),
                      SizedBox(height: 10),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.center,
                      //   crossAxisAlignment: CrossAxisAlignment.start,
                      //   children: [
                      //     Icon(Icons.circle, size: 15, color: Color.fromRGBO(85, 76, 159, 1)),
                      //     Icon(Icons.circle, size: 15, color: Color.fromRGBO(85, 76, 159, 0.45)),
                      //     Icon(Icons.circle, size: 15, color: Color.fromRGBO(85, 76, 159, 0.45)),
                      //   ],
                      // ),
                      Text(
                        'FAQ',
                        style: TextStyle(
                          color: Color.fromRGBO(48, 40, 93, 1),
                          fontSize: 36,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                      SizedBox(height: 20),
                      HelpCenterFaqPage(),
                      Text(
                        'Support',
                        style: TextStyle(
                          color: Color.fromRGBO(48, 40, 93, 1),
                          fontSize: 36,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  SizedBox(
                      child: HelpCenterContactDetailsPage()),
                ],
              ),
            ),
          ),
  )

);
  }
}
