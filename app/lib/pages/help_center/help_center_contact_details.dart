import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:help_center/application/help_center_contact_details_bloc/help_center_contact_details_bloc.dart';

class HelpCenterContactDetailsPage extends StatelessWidget {
  const HelpCenterContactDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HelpCenterContactDetailsBloc, HelpCenterContactDetailsState>(
      listener: (context, state) {
        // Listener for state changes in the Help Center Contact Details service
      },
      builder: (context, state) {
        // Builder for the UI based on the current state
        if (state is LoadingContactDetails) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (state is SuccessContactDetails) {
          return Column(
            children: [
              TextButton(
                onPressed: () {},
                child: Text(
                  'Contact',
                  style: TextStyle(fontSize: 16),
                ),
                style: ButtonStyle(
                  minimumSize: WidgetStateProperty.all(Size(0.5.sw, 45)),
                  backgroundColor: WidgetStateProperty.all(Color.fromRGBO(88, 66, 148, 1)),
                  foregroundColor: WidgetStateProperty.all(Colors.white),
                ),
              ),
              SizedBox(height: 15),
              TextButton(
                onPressed: () {},
                child: Text(
                  'View Device Manual',
                  style: TextStyle(fontSize: 16),
                ),
                style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(0.5.sw, 45)),
                  backgroundColor: MaterialStateProperty.all(Color.fromRGBO(88, 66, 148, 1)),
                  foregroundColor: MaterialStateProperty.all(Colors.white),
                ),
              ),
            ],);
        } else if (state is FailureContactDetails) {
          return Center(
            child: Text(state.failure.toString()),
          );
        } else {
          return const Center(
            child: Text('Something went wrong'),
          );
        }
      },
    );
  }
}
