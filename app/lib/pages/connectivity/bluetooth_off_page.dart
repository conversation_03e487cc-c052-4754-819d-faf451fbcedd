import 'package:bluetooth/bluetooth.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../custom_widgets/curved_app_bar.dart';

/// The BluetoothOffPage class is a StatelessWidget that displays a page when the Bluetooth service is turned off.
class BluetoothOffPage extends StatelessWidget {
  const BluetoothOffPage({super.key});

  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        height: .35.sw, // The height of your curved app bar
      ),
      body: Column(
        children: [
          SizedBox(height: .1.sw,width: 1.sw,),
          Text('Bluetooth is turned off',style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 30,color: Color(0xff26204a),fontWeight: FontWeight.w600),),
          SvgPicture.asset('assets/remote/alert-icon.svg',height: .3.sh,width: 1.2.sw),
          SizedBox(height: 20,),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal:  40.0),
            child: GestureDetector(
              onTap: (){
                BluetoothSettings.turnOnBluetooth();
              },
              child: Container(
                width: 1.sw,
                height: .12.sw,
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xffBEBADE),width: 1),
                  borderRadius : BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                    bottomLeft: Radius.circular(25),
                    bottomRight: Radius.circular(25),
                  ),
                  color : AppTheme.primaryColor,
                ),
                child: Center(child: Text("Turn on",style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 20,color: Colors.white),)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
