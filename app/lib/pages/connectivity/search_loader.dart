import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

import '../../custom_widgets/curved_app_bar.dart';

/// The SearchLoader class is a StatelessWidget that displays a loader while searching for devices.
class SearchLoader extends StatelessWidget {
  const SearchLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return  PopScope(
    canPop: false,

      child: Scaffold(
        backgroundColor: Color(0xffFAF2DF),
        appBar: CurvedAppBar(
          appBarColor: AppTheme.primaryColor,
          logoColor: Color(0xffFAF2DF),
          height: .35.sw, // The height of your curved app bar
        ),
         body: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
             Lottie.asset('assets/remote/loader.json',width: 1.sw,height: 400),
            ],
          ),
      ),
    );
  }
}
