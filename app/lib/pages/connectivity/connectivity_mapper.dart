import 'package:flutter/material.dart';
import 'package:bluetooth/bluetooth.dart';
import 'package:juno_plus/pages/connectivity/device_list_page.dart';
import 'package:juno_plus/pages/connectivity/search_loader.dart';
import 'package:juno_plus/pages/remote/remote_experiment_Page.dart';
import 'package:juno_plus/pages/remote/remote_one_page.dart';
import '../remote/device_reconnecting_page.dart';
import '../remote/remote_page.dart';
import 'bluetooth_off_page.dart';
import 'device_intro.dart';
import 'no_device_found_page.dart';

/// The ConnectivityMapper class is a StatelessWidget that maps the different states of the Bluetooth service to different pages.
class ConnectivityMapper extends StatelessWidget {
  const ConnectivityMapper({super.key});

  @override
  Widget build(BuildContext context) {
    return BluetoothWrapperPage(
      bluetoothOffPage: BluetoothOffPage(),
      noDeviceFoundPage: NoDeviceFoundPage(),
      bluetoothSearchingPage: SearchLoader(),
      bluetoothDeviceListPage: DeviceListPage(),
      remotePage: RemoteExperimentPage(),
      deviceIntroPage: DeviceIntro(),
      deviceReconnectingPage: DeviceReconnectingPage(),
    );
  }
}
