import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  final Connectivity _connectivity = Connectivity();
  final StreamController<bool> _connectivityController =
  StreamController<bool>.broadcast();

  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  ConnectivityService() {
    // Perform an initial check
    _initializeConnectivity();

    // Subscribe to connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  /// Public stream to listen to connectivity changes (returns true if connected, false otherwise)
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// Helper to check for active internet connection
  Future<bool> hasActiveConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException {
      return false;
    }
  }

  /// Initialize and update the current connectivity status
  Future<void> _initializeConnectivity() async {
    final result = await _connectivity.checkConnectivity();
    await _updateConnectionStatus(result);
  }

  /// Updates the connection status (true if connected to the internet)
  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    final hasConnection = await hasActiveConnection();
    _connectivityController.add(hasConnection);
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription.cancel();
    _connectivityController.close();
  }
}
