import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../routing/app_pages.gr.dart';

/// The PairDevicePage class is a StatelessWidget that displays a page to pair a device.
@RoutePage()
class PairDevicePage extends StatelessWidget {
  const PairDevicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return  Scaffold(
        appBar: CurvedAppBar(
        appBarColor: Theme.of(context).primaryColor,
        logoColor: Color(0xffFAF2DF),
    height: .35.sw, // The height of your curved app bar
    ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
         children: [
            SizedBox(height: .1.sw,width: 1.sw,),
           Text('Pair Device',style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 30,color: Color(0xff26204a),fontWeight: FontWeight.w600),),
           Image.asset('assets/home/<USER>',height: .3.sh,width: 1.2.sw),
           GestureDetector(
             onTap: (){
               context.router.push(HomeRoute());
             },
               child: Image.asset('assets/home/<USER>',width: 1.sw,)),

           Padding(
             padding: const EdgeInsets.symmetric(horizontal: 25.0),
             child: Container(
               width:  1.sw,
               height: .7.sw,
               decoration: BoxDecoration(
                   color: Color(0xffFAF2DF),
                   boxShadow: [
                     BoxShadow(
                       color: Color(0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                       blurRadius: 4.0, // the blur radius
                       offset: Offset(0, 1), // the x,y offset of the shadow
                     ),
                   ],
                   borderRadius: BorderRadius.all(Radius.circular(32))),
                child: Center(
                  child: Stack(
                    children: [
                      Container(
                        width: .5.sw,
                        height: .5.sw,
                  decoration: BoxDecoration(
                  // Apply a linear gradient
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF3D2F66), // Dark purple
                      Color(0xFFC7C0DB), // Light purple
                      Color(0xFFE6D8FF), // Lightest purple
                    ],
                    stops: [0.0, 0.665, 1.0], // Stops for gradient colors
                  ),
                            shape: BoxShape.circle),
                        child: Center(
                          child: Container(
                            width: .4.sw,
                            height: .4.sw,
                            decoration: BoxDecoration(
                              // Apply a linear gradient
                              color: Color(0xffFAF2DF),
                                shape: BoxShape.circle),
                            child: Center(
                              child:SvgPicture.asset('assets/remote/Bluetooth_connected.svg',height: 70,width: 70,color: Color(0xff554C9F),),
                            )
                          ),
                        ),

                      ),


                    ],
                  ),
                )
             ),
           ),
           SizedBox(height: 50),

         ],
    ),
      ));
  }
}
