import 'package:auto_route/auto_route.dart';
import 'package:carousel_slider_x/carousel_slider_x.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../custom_widgets/bullet_points.dart';
import '../../custom_widgets/curved_app_bar.dart';

/// The TroubleshootingPage class is a StatefulWidget that displays a page with troubleshooting tips.
@RoutePage()
class TroubleshootingPage extends StatefulWidget {
  @override
  State<TroubleshootingPage> createState() => _TroubleshootingPageState();
}

class _TroubleshootingPageState extends State<TroubleshootingPage> {
  CarouselControllerX _carouselController = CarouselControllerX();
  bool isEarphonesConnected = false;
  int _current = 0;

  final List<String> troubleShootingImages = [
    'assets/remote/troubleshoot_image_1.png',
    'assets/remote/troubleshoot_image_2.png',
  ];

  Widget buildIndicatorList() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(troubleShootingImages.length, (index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _current = index;
              _carouselController.jumpToPage(index);
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5.0),
            child: Container(
              height: 3,
              width: 20,
              color: _current == index ? AppTheme.primaryColor : Colors.grey,
            ),
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar:Padding(
        padding: EdgeInsets.all(25.0),
        child:               ElevatedButton(
          onPressed: isEarphonesConnected
              ? () {
            context.router.popForced(isEarphonesConnected);
          }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: isEarphonesConnected
                ? AppTheme.primaryColor
                : Colors.grey[300], // Button color
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30.0),
            ),
            minimumSize: Size(double.infinity, 50), // Full-width button
          ),
          child: Text(
            'Next',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.0,
            ),
          ),
        ),

      ),
      appBar: CurvedAppBar(
      appBarColor: AppTheme.primaryColor,
      topLeftIcon: IconButton(
        icon: Icon(Icons.arrow_back_ios,color:  Color(0xffFAF2DF),),
        onPressed: () {
          context.router.popForced(isEarphonesConnected);
        },
      ),
      logoColor: Color(0xffFAF2DF),
      height: .35.sw, // The height of your curved app bar
    ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.all(25.0),
              child: Column(
                children: [
                  Container(
                    height: 0.5.sw,
                    child: CarouselSlider(
                      onPageChanged: (index, reason) {
                        setState(() {
                          _current = index;
                        });
                      },
                      carouselController: _carouselController,
                      options: CarouselOptions(
                        viewportFraction: 1,
                        height: 0.4.sh,
                        autoPlay: true,
        
                      ),
                      items: troubleShootingImages.map((item) {
                        return Image.asset(
                          width: 1.sw,
                          item,
                          fit: BoxFit.cover
                        );
                      }).toList(),
                    ),
                  ),
                  SizedBox(height: 20),
                  buildIndicatorList(),
                  SizedBox(height: 20),
                  Padding(
                    padding: EdgeInsets.all(25.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        BulletPoint(
                          number: 1,
                          text: 'Press and hold the Multi-Function button for 3 seconds. The indicator light will turn on.',
                        ),
                        SizedBox(height: 20),
                        BulletPoint(
                          number: 2,
                          text: 'Go to the Bluetooth device list and tap "Juno Lilly device" to finish pairing',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
        
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  isEarphonesConnected = !isEarphonesConnected;
                });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    isEarphonesConnected
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    color: isEarphonesConnected ? AppTheme.primaryColor : Colors.grey,
                    size: 24.0,
                  ),
                  SizedBox(width: 10.0),
                  Text(
                    'Earphones is connected',
                    style: TextStyle(
                      color: isEarphonesConnected ? Colors.black : Colors.grey,
                      fontSize: 16.0,
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
