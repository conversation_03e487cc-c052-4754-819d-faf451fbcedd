import 'package:bluetooth/bluetooth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

/// The DeviceIntro class is a StatefulWidget that displays an animation when connecting to a device.
class DeviceIntro extends StatefulWidget {

  @override
  State<DeviceIntro> createState() => _DeviceIntroState();
}

class _DeviceIntroState extends State<DeviceIntro> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, state) {
        if (state is ConnectionIntro) {
          return Scaffold(
            body: Center( // Center the Lottie animation
              child: Lottie.asset(
                'assets/remote/intro.json',
                width: 1.sw,
                height: 1.sw,
                fit: BoxFit.fitWidth,
                controller: _controller,
                onLoaded: (composition) {
                  _controller
                    ..duration = composition.duration
                    ..forward().whenComplete(() {
                      context.read<BluetoothServiceBloc>().add(
                          ListenToDevice(state.device) // Use widget.device to pass it to the event
                      );
                    });
                },
              ),
            ),
          );
        } else {
          return Container(); // Handle other states if needed
        }
      },
    );
  }
}
