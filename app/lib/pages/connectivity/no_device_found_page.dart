import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/bluetooth.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/curved_app_bar.dart';

/// The NoDeviceFoundPage class is a StatelessWidget that displays a page when no device is found.
class NoDeviceFoundPage extends StatelessWidget {
  const NoDeviceFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        height: .35.sw, // The height of your curved app bar

      ),
      body: Padding(
        padding: const EdgeInsets.all(25.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text('No device found',style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 30,color: Color(0xff26204a),fontWeight: FontWeight.w600),),
            SizedBox(height: 20,),
            Text('Please make sure your lilly device is turned on and connected to this mobile phone via Bluetooth',style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 20,color: Color(0xff26204a),fontWeight: FontWeight.w400),textAlign: TextAlign.center,),
            SizedBox(height: 20,),
            GestureDetector(
              onTap: ()async{
           final res= await context.router.push( TroubleshootingRoute());
             if(res==true){
               context.read<BluetoothServiceBloc>().add(const StartSearch());             }
              },
                child: Text('get troubleshooting tips',style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 20,color: AppTheme.primaryColor,fontWeight: FontWeight.w400,decoration: TextDecoration.underline),)),
            SizedBox(height: 20,),
            SvgPicture.asset('assets/remote/alert-icon.svg',height: .3.sw,width: .3.sw),
            SizedBox(height: 40,),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal:  40.0),
              child: GestureDetector(
                onTap: (){
                  context.read<BluetoothServiceBloc>().add(const StartSearch());
                },
                child: Container(
                  width: 1.sw,
                  height: .12.sw,
                  decoration: BoxDecoration(
                    border: Border.all(color: Color(0xffBEBADE),width: 1),
                    borderRadius : BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                      bottomLeft: Radius.circular(25),
                      bottomRight: Radius.circular(25),
                    ),
                    color : AppTheme.primaryColor,
                  ),
                  child: Center(child: Text("Scan for Device",style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 20,color: Colors.white),)),
                ),
              ),
            ),
          ],
        ),
      )
    );
  }
}
