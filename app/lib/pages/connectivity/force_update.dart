// import 'package:design_system/design_system.dart';
// import 'package:firebase_remote_config/firebase_remote_config.dart';
// import 'package:package_info_plus/package_info_plus.dart';
// import 'package:flutter/material.dart';
// import 'package:url_launcher/url_launcher.dart'; // To open app store or play store
//
// class AppUpdateCheck {
//   final FirebaseRemoteConfig remoteConfig;
//
//   AppUpdateCheck(this.remoteConfig);
//
//   Future<void> checkForUpdate(BuildContext context) async {
//     try {
//       await remoteConfig.setConfigSettings(RemoteConfigSettings(
//         fetchTimeout: Duration(seconds: 10),
//         minimumFetchInterval: Duration(seconds: 0), // No wait for fetching updates
//       ));
//       bool updated= await remoteConfig.fetchAndActivate();
//       if (updated) {
//         print("Remote Config updated with latest values.");
//       } else {
//         print("Using cached Remote Config values.");
//       }
//
//       // Get the latest version and mandatory update flag from Remote Config
//       final latestVersion = remoteConfig.getString('latest_version');
//       final isMandatoryUpdate = remoteConfig.getBool('is_mandatory_update');
//
//       // Get current app version
//       PackageInfo packageInfo = await PackageInfo.fromPlatform();
//       String currentVersion = packageInfo.version;
//       print('Current version: $currentVersion, Latest version: $latestVersion');
//
//       // Compare versions
//       if (_isVersionOutdated(currentVersion, latestVersion)) {
//         _showUpdateDialog(context, isMandatoryUpdate);
//       }
//     } catch (e) {
//       print("Error checking for updates: $e");
//       // Optionally, handle fallback behavior here (e.g., allow app use without update)
//     }
//   }
//
// // Helper function to compare version strings
//   bool _isVersionOutdated(String currentVersion, String latestVersion) {
//     List<int> current = currentVersion.split('.').map(int.parse).toList();
//     List<int> latest = latestVersion.split('.').map(int.parse).toList();
//
//     for (int i = 0; i < current.length; i++) {
//       if (i >= latest.length || current[i] < latest[i]) return true;
//       if (current[i] > latest[i]) return false;
//     }
//     return current.length < latest.length;
//   }
//
//
//   void _showUpdateDialog(BuildContext context, bool isMandatoryUpdate) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//
//           backgroundColor: AppTheme.loginAppBarColor,
//           title: Text(isMandatoryUpdate ? 'Update Required' : 'New Update Available',style: Theme.of(context).textTheme.headlineMedium!.copyWith(color: AppTheme.primaryColor),),
//           content: Text(isMandatoryUpdate
//               ? 'A new version of the app is required. Please update.'
//               : 'A new version of the app is available. Would you like to update?',style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: AppTheme.primaryColor),),
//           actions: <Widget>[
//             if (isMandatoryUpdate)
//               TextButton(
//                 onPressed: () {
//                   // Redirect user to app store
//                   _openAppStore();
//                 },
//                 child: Text('Update'),
//               ),
//             if (!isMandatoryUpdate)
//               TextButton(
//                 onPressed: () {
//                   // Optionally dismiss
//                   Navigator.of(context).pop();
//                 },
//                 child: Text('Later'),
//               ),
//             TextButton(
//               onPressed: () {
//                 // Force user to update
//                 _openAppStore();
//               },
//               child: Text('Update Now'),
//             ),
//           ],
//         );
//       },
//     );
//   }
//
//   void _openAppStore() async {
//     const url = 'https://play.google.com/store/apps/details?id=com.yourappname'; // Google Play Store URL
//
//     if (await canLaunch(url)) {
//       await launch(url);
//     } else {
//       throw 'Could not launch $url';
//     }
//   }
// }
