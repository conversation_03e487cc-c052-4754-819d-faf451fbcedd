
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class CalendarWidget extends StatefulWidget {
  const CalendarWidget({super.key});

  @override
  State<CalendarWidget> createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends State<CalendarWidget> {
  @override
  Widget build(BuildContext context) {
    DateTime _now = new DateTime.now().subtract(Duration(days: 20));
    double _day = _now.day.toDouble();

    return
      Container(
        width: 280,
        height: 280,
        child: Stack(
          children: [
            // Positioned(
            //   top: 0,
            //   bottom: 0,
            //   left: 0,
            //   right: 0,
            //   child: Container(
            //     margin: EdgeInsets.all(30.00),
            //     width: 10,
            //     height: 10,
            //     decoration: BoxDecoration(
            //       color: Colors.white,
            //       boxShadow: [
            //         BoxShadow(
            //           color: Color.fromRGBO(255, 255, 255, 1.0),
            //           blurRadius: 2.0,
            //           offset: Offset(0, 1),
            //         ),
            //       ],
            //       shape: BoxShape.circle,
            //     ),
            //   ),
            // ),

            Container(
                decoration:
                BoxDecoration(
                    color: Colors.transparent,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.transparent,
                        blurRadius: 2,
                        offset: Offset(0, 1),

                      ),
                    ]
                ),
                child: SfRadialGauge(
                  axes: [
                    RadialAxis(
                      axisLineStyle: AxisLineStyle(color: Color.fromRGBO(211, 207, 198, 1.0), cornerStyle: CornerStyle.startCurve, thickness: 20.0),
                      minimum: 0,
                      maximum: 30,
                      radiusFactor: 0.83,
                      labelOffset: -15.0,
                      startAngle: 280,
                      endAngle: 280,
                      showTicks: false,
                      showLabels: false,

                    )
                  ],

                )
            ),
            // Container(
            //   child: SfRadialGauge(
            //     axes: [
            //       RadialAxis(
            //         axisLineStyle: AxisLineStyle(color: Colors.transparent,cornerStyle: CornerStyle.startCurve, thickness: 20.0),
            //         pointers: <GaugePointer>[
            //           RangePointer(value: 7, cornerStyle: CornerStyle.bothCurve, width: 20.0, color: Color.fromRGBO(243, 161, 14, 1.0),)
            //         ],
            //         labelOffset: -15.0,
            //         radiusFactor: 0.83,
            //         minimum: 0,
            //         maximum: 30,
            //         startAngle: 90,
            //         endAngle: 90,
            //         showTicks: false,
            //         showLabels: false,
            //         labelsPosition: ElementsPosition.inside,
            //
            //
            //       )
            //     ],
            //
            //   ),
            //
            // ),

            Container(
              child: SfRadialGauge(
                axes: [
                  RadialAxis(
                    axisLineStyle: AxisLineStyle(color: Colors.transparent,cornerStyle: CornerStyle.startCurve, thickness: 20.0),
                    pointers: <GaugePointer>[
                      RangePointer(value: 5, cornerStyle: CornerStyle.bothCurve, width: 20.0, color:Color(0xff554284)),
                      MarkerPointer(value: _day, markerType: MarkerType.triangle, markerOffset: 22, color: Color(0xff554284), markerHeight: 20, markerWidth: 25, ),
                    ],
                    minimum: 0,
                    maximum: 30,
                    radiusFactor: 0.83,
                    labelOffset: -15.0,
                    startAngle: 265,
                    endAngle: 265,
                    showTicks: false,
                    showLabels: false,
                  )
                ],
              ),
            ),
            Container(
              child: SfRadialGauge(
                axes: [
                  RadialAxis(
                    axisLineStyle: AxisLineStyle(color:Color(0xff554284),cornerStyle: CornerStyle.bothCurve, thickness: 20.0),
                    pointers: <GaugePointer>[
                      RangePointer(value: 5, cornerStyle: CornerStyle.bothCurve, width: 20.0, color: Colors.transparent,)
                    ],
                    labelOffset: -12.0,
                    radiusFactor: 0.83,
                    minimum: 1,
                    maximum: 5,
                    startAngle: 270,
                    endAngle: 325,
                    showTicks: false,
                    showLabels: true,
                    showLastLabel: false,
                    maximumLabels: 10,

                    axisLabelStyle: GaugeTextStyle(color: Colors.white),
                    labelsPosition: ElementsPosition.inside,

                  )
                ],

              ),

            ),
            Positioned(
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                margin: EdgeInsets.all(55.00),
                width: 10,
                height: 10,
                // child: Align(
                //   alignment: Alignment(-0.1,-0.25),
                //   child: Text('Day '+_day.toInt().toString()+'\n of your cycle',
                //       textAlign: TextAlign.center,
                //       style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20.00,height: 1.1)),
                // ),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(61 , 47, 102, 1.0),
                  shape: BoxShape.circle,
                ),
              ),
            ),

          ],


        ),
      );
  }
}
