import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:flutter/material.dart';
import 'package:auto_route/annotations.dart';
import '../../custom_widgets/curved_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:design_system/design/theme.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

@RoutePage()
class ExtendedCalenderPage extends StatefulWidget {
  const ExtendedCalenderPage({super.key});

  @override
  State<ExtendedCalenderPage> createState() => _ExtendedCalenderPageState();
}

class _ExtendedCalenderPageState extends State<ExtendedCalenderPage> {
  double _currentSliderValue = 1;
  List<String> emoji = [' 😁',' 🙂',' 😐',' 😕',' 🙁',' 😞',' 😓',' 😣',' 😖',' 😵‍💫'];
  List<String> pain_description = ['No Pain','Discomfort','Very Mild','Mild','Moderate','Significant','High','Very High','Intense','Worst Pain'];
  String selected_button = '';
  bool isPressedHeadache = false;
  bool isPressedFatigue = false;
  bool isPressedBloating = false;
  bool isPressedBackPain = false;
  bool isPressedCramps = false;
  bool isPressedBreakouts = false;
  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now().add(Duration(days: 6));
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        height: .35.sw,
        topLeftIcon: GestureDetector(
          onTap: (){
            Navigator.pop(context);
          },
          child: Container(
            height: 40,
            width: 40,
            child: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Icon(Icons.arrow_back,color:Colors.white,),
            ),
          ),
        ),

      ),
      body: SingleChildScrollView(
        child: Container(
          child: Column(
            children: [
              SizedBox(height: 0.4.sw,),




              Container(
                margin: EdgeInsets.zero,
                height: 0.70.sw,
                width: 0.90.sw,
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                      blurRadius: 4.0, // the blur radius
                      offset: Offset(0, 1), // the x,y offset of the shadow
                    ),
                  ],
                  borderRadius: BorderRadius.circular(32),
                  color:Color.fromRGBO(250, 242, 223, 1),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: 30,),
                      Text("What is your pain level?", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 22),),
                      SizedBox(height: 30,),
                      Text(_currentSliderValue.toInt().toString(), style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 31),),
                      Container(
                        height: 77,
                        margin: EdgeInsets.zero,
                        child: Align(
                          child:SfLinearGauge(
                            minimum: 1,
                            maximum: 10,
                            markerPointers: [
                              LinearWidgetPointer(
                                dragBehavior: LinearMarkerDragBehavior.free,
                                value: _currentSliderValue,
                                child: Container(
                                  margin: EdgeInsets.zero,
                                  height: 100,
                                  width: 100,
                                  child: Column(
                                    children: [
                                      Stack(
                                        alignment: AlignmentDirectional.bottomCenter,
                                        children: [
                                          Container(
                                            margin: EdgeInsets.zero,
                                            height: 30,
                                            width: 100,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(32),
                                              color: Colors.transparent,
                                            ),
                                          ),
                                          Container(
                                            alignment: Alignment.center,
                                            margin: EdgeInsets.only(left: 10),
                                            height: 30,
                                            width: 100,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(32),
                                              color: Color.fromRGBO(207, 204, 235, 1.0),
                                            ),
                                            child: Text(
                                              pain_description[_currentSliderValue.toInt() - 1],
                                              style: TextStyle(
                                                fontSize: 15,
                                                fontWeight: FontWeight.w500,
                                                color: Color.fromRGBO(48, 40, 93, 1),
                                              ),
                                            ),
                                          ),
                                          Container(
                                            margin: EdgeInsets.only(right: 15),
                                            height: 20,
                                            width: 20,
                                            child: Icon(
                                              size: 40,
                                              Icons.arrow_drop_down_outlined,
                                              color: Color.fromRGBO(207, 204, 235, 1.0),
                                            ),
                                          )
                                        ],
                                      ),
                                      Text(
                                        emoji[_currentSliderValue.toInt() - 1],
                                        style: TextStyle(fontSize: 30),
                                      ),
                                    ],
                                  ),
                                ),
                                onChanged: (double newValue) {
                                  setState(() {
                                    _currentSliderValue = newValue;
                                  });
                                },
                              ),
                            ],
                            axisTrackExtent: 30,
                            axisTrackStyle: LinearAxisTrackStyle(
                              color: Color.fromRGBO(173, 138, 166, 1.0),
                              edgeStyle: LinearEdgeStyle.bothCurve,
                              thickness: 2.0,
                            ),
                            showTicks: false,
                            showLabels: false,
                              // Add an offset by increasing the left padding
                          ),

                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('No Pain', style: TextStyle(color: Colors.black),),
                            Text('Worst Pain', style: TextStyle(color: Colors.black),),

                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.all(22),
                height: 217,
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                      blurRadius: 4.0, // the blur radius
                      offset: Offset(0, 1), // the x,y offset of the shadow
                    ),
                  ],
                  borderRadius: BorderRadius.circular(32),
                  color:Color.fromRGBO(250, 242, 223, 1),
                ),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("Symptoms", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 22),),
                      SizedBox(height: 20,),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 38,
                                width: 67,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  color:(isPressedHeadache)? Color.fromRGBO(247, 166, 0, 1): Colors.white ,
                                ),
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  icon: SvgPicture.asset('assets/home/<USER>', color: (isPressedHeadache)?  Colors.white : Color.fromRGBO(88, 66, 148, 1), fit: BoxFit.cover,),
                                  onPressed: () {
                                    setState(() {
                                      isPressedHeadache =! isPressedHeadache;
                                    });

                                  },
                                )
                              ),
                              Text("Headache", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 15),),
                            ],
                          ),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 38,
                                width: 67,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  color:(isPressedFatigue)? Color.fromRGBO(247, 166, 0, 1): Colors.white ,

                                ),
                                  child: IconButton(
                                    padding: EdgeInsets.zero,
                                    icon: SvgPicture.asset('assets/home/<USER>', color: (isPressedFatigue)?  Colors.white : Color.fromRGBO(88, 66, 148, 1), fit: BoxFit.cover,),
                                    onPressed: () {
                                      setState(() {
                                        isPressedFatigue =! isPressedFatigue;
                                      });

                                    },
                                  )                              ),
                              Text("Fatigue", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 15),),
                            ],
                          ),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 38,
                                width: 67,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  color:(isPressedBloating)? Color.fromRGBO(247, 166, 0, 1): Colors.white ,

                                ),
                                  child: IconButton(
                                    padding: EdgeInsets.zero,
                                    icon: SvgPicture.asset('assets/home/<USER>', color: (isPressedBloating)?  Colors.white : Color.fromRGBO(88, 66, 148, 1), fit: BoxFit.cover,),
                                    onPressed: () {
                                      setState(() {
                                        isPressedBloating =! isPressedBloating;
                                      });

                                    },
                                  )
                              ),
                              Text("Bloating", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 15),),
                            ],
                          )

                        ],
                      ),
                      SizedBox(height: 20,),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 38,
                                width: 67,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  color:(isPressedBackPain)? Color.fromRGBO(247, 166, 0, 1): Colors.white ,
                                ),
                                  child: IconButton(
                                    padding: EdgeInsets.zero,
                                    icon: SvgPicture.asset('assets/home/<USER>', color: (isPressedBackPain)?  Colors.white : Color.fromRGBO(88, 66, 148, 1), fit: BoxFit.cover, ),
                                    onPressed: () {
                                      setState(() {
                                        isPressedBackPain =! isPressedBackPain;
                                      });

                                    },
                                  )
                              ),
                              Text("Back Pain", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 15),),
                            ],
                          ),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 38,
                                width: 67,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  color:(isPressedCramps)? Color.fromRGBO(247, 166, 0, 1): Colors.white ,

                                ),
                                  child: IconButton(
                                    padding: EdgeInsets.zero,
                                    icon: SvgPicture.asset('assets/home/<USER>', color: (isPressedCramps)?  Colors.white : Color.fromRGBO(88, 66, 148, 1) , fit: BoxFit.cover,),
                                    onPressed: () {
                                      setState(() {
                                        isPressedCramps =! isPressedCramps;
                                      });

                                    },
                                  )                              ),
                              Text("Cramps", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 15),),
                            ],
                          ),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 38,
                                width: 67,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  color:(isPressedBreakouts)? Color.fromRGBO(247, 166, 0, 1): Colors.white ,
                                ),
                                  child: IconButton(
                                    padding: EdgeInsets.zero,
                                    icon: SvgPicture.asset('assets/home/<USER>', color: (isPressedBreakouts)?  Colors.white : Color.fromRGBO(88, 66, 148, 1), fit: BoxFit.cover, ),
                                    onPressed: () {
                                      setState(() {
                                        isPressedBreakouts =! isPressedBreakouts;
                                      });

                                    },
                                  )                                ),
                              Text("Breakouts", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 15),),
                            ],
                          ),

                        ],
                      )
                    ]
                ),
              ),
              Container(
                margin: EdgeInsets.fromLTRB(22, 0 , 22, 22),
                height: 120,
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                      blurRadius: 4.0, // the blur radius
                      offset: Offset(0, 1), // the x,y offset of the shadow
                    ),
                  ],
                  borderRadius: BorderRadius.circular(32),
                  color:Color.fromRGBO(250, 242, 223, 1),
                ),
                child: Column(
                  children: [
                    SizedBox(height: 10,),
                    Text("Flow", style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0), fontWeight: FontWeight.w700, fontSize: 22),),
                    SizedBox(height: 5,),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Container(
                          height: 45,
                          width: 90,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(32),
                            color:Color.fromRGBO(250, 242, 223, 1),
                          ),
                          child: TextButton(
                            style: ButtonStyle(
                              padding: WidgetStatePropertyAll(EdgeInsets.zero),
                              backgroundColor: (selected_button == 'button1')? WidgetStatePropertyAll(Color.fromRGBO(247, 166, 0, 1)) : WidgetStatePropertyAll(Color.fromRGBO(250, 242, 223, 1)),
                              foregroundColor: (selected_button == 'button1')? WidgetStatePropertyAll(Colors.white) : WidgetStatePropertyAll(Colors.purple),
                            ),
                            onPressed: (){
                              setState(() {
                                selected_button = 'button1';
                              });
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button1')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button1')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button1')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                              ],
                            ),
                          ),
                        ),
                        Container(
                          height: 45,
                          width: 90,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(32),
                            color:Color.fromRGBO(250, 242, 223, 1),
                          ),
                          child: TextButton(
                            style: ButtonStyle(
                              padding: WidgetStatePropertyAll(EdgeInsets.zero),
                              backgroundColor: (selected_button == 'button2')? WidgetStatePropertyAll(Color.fromRGBO(247, 166, 0, 1)) : WidgetStatePropertyAll(Color.fromRGBO(250, 242, 223, 1)),
                              foregroundColor: (selected_button == 'button2')? WidgetStatePropertyAll(Colors.white) : WidgetStatePropertyAll(Colors.purple),
                            ),
                            onPressed: (){
                              setState(() {
                                selected_button = 'button2';
                              });
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button2')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button2')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button2')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                              ],
                            ),
                          ),
                        ),
                        Container(
                          height: 45,
                          width: 90,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(32),
                            color:Color.fromRGBO(250, 242, 223, 1),
                          ),
                          child: TextButton(
                            style: ButtonStyle(
                              padding: WidgetStatePropertyAll(EdgeInsets.zero),
                              backgroundColor: (selected_button == 'button3')? WidgetStatePropertyAll(Color.fromRGBO(247, 166, 0, 1)) : WidgetStatePropertyAll(Color.fromRGBO(250, 242, 223, 1)),
                              foregroundColor: (selected_button == 'button3')? WidgetStatePropertyAll(Colors.white) : WidgetStatePropertyAll(Colors.purple),
                            ),
                            onPressed: (){
                              setState(() {
                                selected_button = 'button3';
                              });
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button3')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button3')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                                SvgPicture.asset('assets/home/<USER>', color: (selected_button == 'button3')?  Colors.white : Color.fromRGBO(88, 66, 148, 1)),

                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 200,
                child:
                ElevatedButton(
                    onPressed: (){},
                    style: ButtonStyle(backgroundColor: WidgetStatePropertyAll(Color.fromRGBO(58, 38, 101, 1.0))),
                    child: Text('Save', style: TextStyle(color: Colors.white),)),
              ),
              SizedBox(
                height: 5,
              ),
              SizedBox(
                width: 200,
                child:
                ElevatedButton(
                    onPressed: (){},
                    style: ButtonStyle(backgroundColor: WidgetStatePropertyAll(Colors.white)),
                    child: Text('Back', style: TextStyle(color: Color.fromRGBO(58, 38, 101, 1.0)),)),
              ),
              SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
      ),
    );
  }
}


List<DateTime> calculateOvulationDates(DateTime firstDayOfPeriod, int cycleLength) {
  // Ovulation typically occurs around 14 days before the next period
  int ovulationDay = cycleLength - 14;

  // Calculate ovulation date
  DateTime ovulationDate = firstDayOfPeriod.add(Duration(days: ovulationDay));

  // Block ovulation date and surrounding days (e.g., fertile window: 2 days before and after ovulation)
  List<DateTime> blockedDates = [];
  for (int i = -2; i <= 2; i++) {
    blockedDates.add(ovulationDate.add(Duration(days: i)));
  }

  return blockedDates;
}