import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:account_management/domain/model/period_tracking_model.dart';
import 'package:auto_route/annotations.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/dashboard/symptom_tracking_page.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../custom_widgets/dotted_border_widget.dart';
import 'package:account_management/account_management.dart';
import '../../helpers.dart';

@RoutePage()
class PeriodTrackingCalendarPage extends StatelessWidget {
  const PeriodTrackingCalendarPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ManagePeriodTrackingBloc>(
            create: (context) => getIt<ManagePeriodTrackingBloc>()),
        BlocProvider<PeriodTrackingWatcherBloc>(
          create: (context) => getIt<PeriodTrackingWatcherBloc>()
            ..add(const PeriodTrackingWatcherEvent.watchAllStarted()),
        ),
      ],
      child: PeriodTrackingCalendarScaffold(),
    );
  }
}

class PeriodTrackingCalendarScaffold extends StatefulWidget {
  const PeriodTrackingCalendarScaffold({super.key});

  @override
  State<PeriodTrackingCalendarScaffold> createState() =>
      _PeriodTrackingCalendarScaffoldState();
}

class _PeriodTrackingCalendarScaffoldState
    extends State<PeriodTrackingCalendarScaffold> {
  CalendarFormat _calendarFormat = CalendarFormat.week;
  bool _currentDaySelected = false;
  DateTime _focusedDay = DateTime.now();

  Widget _buildBottomSheet(BuildContext context) {
    return Container(
      height: .85.sh,
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(32),
          topRight: Radius.circular(32),
        ),
      ),
      clipBehavior: Clip.hardEdge,
      child: MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<PeriodTrackingWatcherBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<ManagePeriodTrackingBloc>(context),
          ),
        ],
        child: SymptomTrackingPage(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
        listener: (context, state) {
      state.maybeWhen(
        periodTrackingAdded: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Period Tracking Added Successfully')),
          );
          Navigator.pop(context);
        },
        periodTrackingUpdated: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Period Tracking Updated Successfully')),
          );
          Navigator.pop(context);
        },
        periodTrackingDeleted: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Period Tracking Deleted Successfully')),
          );
          Navigator.pop(context);
        },
        dataLoaded: () {
          // Period dates saved successfully - don't navigate away, just stay on current page
        },
        periodTrackingFailure: (failure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Action Failed: ${failure}')),
          );
          // Don't navigate away - let user stay on the page to retry
        },
        orElse: () {},
      );
    }, child:
            BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
                builder: (context, state) {
      return state.map(
        loading: (_) => Scaffold(
          appBar: CurvedAppBar(
            appBarColor: AppTheme.primaryColor,
            logoColor: Color(0xffFAF2DF),
            height: .35.sw,
            topLeftIcon: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                height: 40,
                width: 40,
                child: Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            topRightIcon: Container(
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                color: Color(0xffFAF2DF),
                shape: BoxShape.circle,
              ),
              child: Padding(
                padding: const EdgeInsets.all(6.0),
                child: Icon(
                  Icons.notifications_rounded,
                  color: Color(0xff30285D),
                ),
              ),
            ), // The height of your curved app bar
          ),
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        data: (state) {
          // Print all calculated dates for debugging
          // print('📅 CALENDAR DEBUG - All calculated dates:');
          // print('📅 Selected period dates (${state.selectedDays.length}):');
          List<DateTime> sortedSelectedDays = state.selectedDays.toList()
            ..sort();
          for (int i = 0; i < sortedSelectedDays.length; i++) {
            // print('📅   ${i + 1}. ${sortedSelectedDays[i]}');
          }

          // print('📅 Ovulation dates (${state.ovulationDays.length}):');
          List<DateTime> sortedOvulationDays = state.ovulationDays.toList()
            ..sort();
          for (int i = 0; i < sortedOvulationDays.length; i++) {
           // print('📅   ${i + 1}. ${sortedOvulationDays[i]}');
          }

          // print('📅 Focused day: ${state.focusedDay}');
          // print(
          //     '📅 Selected period tracking day: ${state.selectedPeriodTrackingDay.date}');

          return Scaffold(
            appBar: CurvedAppBar(
              appBarColor: AppTheme.primaryColor,
              logoColor: Color(0xffFAF2DF),
              height: .35.sw,
              topLeftIcon: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  height: 40,
                  width: 40,
                  child: Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              topRightIcon: Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(
                  color: Color(0xffFAF2DF),
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: Icon(
                    Icons.notifications_rounded,
                    color: Color(0xff30285D),
                  ),
                ),
              ), // The height of your curved app bar
            ),
            body: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(15.0),
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                          color: Color(0xffFAF2DF),
                          boxShadow: [
                            BoxShadow(
                              color: Color(
                                  0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                              blurRadius: 4.0, // the blur radius
                              offset:
                                  Offset(0, 1), // the x,y offset of the shadow
                            ),
                          ],
                          borderRadius: BorderRadius.all(Radius.circular(32))),
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: 10.0, right: 10.0, bottom: 10.0),
                        child: TableCalendar<PeriodTrackingModel>(
                          firstDay: DateTime(1960, 1,
                              1), // Allow selecting from a very early date
                          lastDay: DateTime.now().add(const Duration(
                              days: 365 *
                                  2)), // Allow future dates for predictions
                          focusedDay: _focusedDay,
                          calendarFormat: _calendarFormat,
                          headerStyle: HeaderStyle(
                            formatButtonVisible: false,
                            titleCentered: true,
                            titleTextStyle: GoogleFonts.roboto(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                            ),
                            leftChevronIcon: Icon(
                              Icons.chevron_left,
                              color: AppTheme.primaryColor,
                            ),
                            rightChevronIcon: Icon(
                              Icons.chevron_right,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                          startingDayOfWeek: StartingDayOfWeek.monday,

                          selectedDayPredicate: (day) {
                            return state.selectedDays.any((selectedDay) =>
                                selectedDay.year == day.year &&
                                selectedDay.month == day.month &&
                                selectedDay.day == day.day);
                          },
                          onDaySelected: (selectedDay, focusedDay) {
                            print(
                                'Calendar format: ${_calendarFormat.name}, Selected day: $selectedDay');

                            if (_calendarFormat == CalendarFormat.week) {
                              // Week view - only allow symptom tracking for today and past dates
                              final today = DateTime.now();
                              final selectedDateOnly = DateTime(
                                  selectedDay.year,
                                  selectedDay.month,
                                  selectedDay.day);
                              final todayOnly =
                                  DateTime(today.year, today.month, today.day);

                              if (selectedDateOnly.isAfter(todayOnly)) {
                                // Future date - show message and don't allow selection
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                        'Cannot add symptoms for future dates'),
                                    backgroundColor: Colors.orange,
                                  ),
                                );
                                return;
                              }

                              // Valid date for symptom tracking - just update focused day
                              context.read<ManagePeriodTrackingBloc>().add(
                                  ManagePeriodTrackingEvent.selectDay(
                                      selectedDay, false));
                            } else {
                              // Month view - period date editing mode
                              context.read<ManagePeriodTrackingBloc>().add(
                                  ManagePeriodTrackingEvent.selectDay(
                                      selectedDay, true));
                            }
                          },
                          onPageChanged: (focusedDay) {
                            // Update focused day when user swipes to different month
                            setState(() {
                              _focusedDay = focusedDay;
                            });
                            context.read<ManagePeriodTrackingBloc>().add(
                                ManagePeriodTrackingEvent.selectDay(focusedDay,
                                    false)); // false means just update focus, don't toggle selection
                          },
                          daysOfWeekHeight: 40,
                          calendarStyle: CalendarStyle(
                            todayTextStyle: const TextStyle(
                              color: Color(0xff71456F),
                              fontWeight: FontWeight.w400,
                              fontSize: 13,
                            ),
                            outsideDaysVisible: true,
                            todayDecoration: BoxDecoration(
                              border: Border.all(color: AppTheme.primaryColor),
                              shape: BoxShape.circle,
                            ),
                            defaultTextStyle: const TextStyle(
                              color: Color(0xff71456F),
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                            ),
                            selectedDecoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(color: AppTheme.primaryColor),
                            ),
                            disabledTextStyle:
                                const TextStyle(color: Colors.transparent),
                            weekendTextStyle: const TextStyle(
                              color: Color(0xff71456F),
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                            ),
                            selectedTextStyle: GoogleFonts.roboto(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                            ),
                          ),
                          calendarBuilders: CalendarBuilders(
                            defaultBuilder: (context, date, focusedDay) {
                              // Check if this is the focused day
                              bool isFocusedDay =
                                  date.year == _focusedDay.year &&
                                      date.month == _focusedDay.month &&
                                      date.day == _focusedDay.day;

                              // Check if this is today
                              final today = DateTime.now();
                              bool isToday = date.year == today.year &&
                                  date.month == today.month &&
                                  date.day == today.day;

                              // Check if this is a selected period date
                              bool isSelectedPeriodDate = state.selectedDays
                                  .any((selectedDay) =>
                                      selectedDay.year == date.year &&
                                      selectedDay.month == date.month &&
                                      selectedDay.day == date.day);

                              // Check if this is an ovulation date
                              bool isOvulationDate = state.ovulationDays.any(
                                  (ovulationDay) =>
                                      ovulationDay.year == date.year &&
                                      ovulationDay.month == date.month &&
                                      ovulationDay.day == date.day);

                              // Check if this is a future date
                              bool isFutureDate = date.isAfter(
                                  DateTime(today.year, today.month, today.day));

                              // Priority: Focused day > Today > Selected period > Ovulation > Default
                              if (isFocusedDay) {
                                return Container(
                                  margin: const EdgeInsets.all(4.0),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      '${date.day}',
                                      style: GoogleFonts.roboto(
                                        color: const Color(0xffFBF0D5),
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                );
                              } else if (isSelectedPeriodDate) {
                                // Period dates - solid border for past, dotted for future
                                if (isFutureDate) {
                                  return Container(
                                    margin: const EdgeInsets.all(4.0),
                                    child: DottedBorder(
                                      color: AppTheme.primaryColor,
                                      strokeWidth: 2,
                                      child: Container(
                                        width: 32,
                                        height: 32,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: AppTheme.primaryColor
                                              .withOpacity(0.1),
                                        ),
                                        child: Center(
                                          child: Text(
                                            '${date.day}',
                                            style: GoogleFonts.roboto(
                                              color: AppTheme.primaryColor,
                                              fontWeight: FontWeight.w500,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                } else {
                                  return Container(
                                    margin: const EdgeInsets.all(4.0),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: AppTheme.primaryColor,
                                        width: 2,
                                      ),
                                      color: AppTheme.primaryColor
                                          .withOpacity(0.2),
                                    ),
                                    child: Center(
                                      child: Text(
                                        '${date.day}',
                                        style: GoogleFonts.roboto(
                                          color: AppTheme.primaryColor,
                                          fontWeight: FontWeight.w500,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                  );
                                }
                              } else if (isOvulationDate) {
                                return Container(
                                  margin: const EdgeInsets.all(4.0),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Color(0xffECA83D).withOpacity(0.3),
                                    border: Border.all(
                                      color: Color(0xffECA83D),
                                      width: 1,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      '${date.day}',
                                      style: GoogleFonts.roboto(
                                        color: Color(0xffECA83D),
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                );
                              } else if (isToday) {
                                return Container(
                                  margin: const EdgeInsets.all(4.0),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppTheme.primaryColor,
                                      width: 2,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      '${date.day}',
                                      style: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                );
                              } else {
                                return Center(
                                  child: Text(
                                    '${date.day}',
                                    style: GoogleFonts.roboto(
                                      color: AppTheme.primaryColor,
                                      fontWeight: FontWeight.w400,
                                      fontSize: 16,
                                    ),
                                  ),
                                );
                              }
                            },
                            selectedBuilder: (context, date, focusedDay) {
                              bool isSameDay =
                                  date.year == state.focusedDay.year &&
                                      date.month == state.focusedDay.month &&
                                      date.day == state.focusedDay.day;

                              // Check if this is a future period date
                              bool isSelectedDay = state.selectedDays.any(
                                  (selectedDay) =>
                                      selectedDay.year == date.year &&
                                      selectedDay.month == date.month &&
                                      selectedDay.day == date.day);

                              bool isFutureDate = date.isAfter(DateTime.now());

                              return isSameDay
                                  ? Container(
                                      margin: const EdgeInsets.all(4.0),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: AppTheme.primaryColor,
                                      ),
                                      child: Center(
                                        child: Text(
                                          '${date.day}',
                                          style: GoogleFonts.roboto(
                                            color: const Color(0xffFBF0D5),
                                            fontWeight: FontWeight.w400,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ),
                                    )
                                  : isSelectedDay
                                      ? Container(
                                          margin: const EdgeInsets.all(4.0),
                                          child: isFutureDate
                                              ? DottedBorder(
                                                  color: AppTheme.primaryColor,
                                                  strokeWidth: 2,
                                                  child: Container(
                                                    width: 32,
                                                    height: 32,
                                                    child: Center(
                                                      child: Text(
                                                        '${date.day}',
                                                        style:
                                                            GoogleFonts.roboto(
                                                          color: AppTheme
                                                              .primaryColor,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          fontSize: 16,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              : Container(
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color:
                                                          AppTheme.primaryColor,
                                                      width: 2,
                                                    ),
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      '${date.day}',
                                                      style: GoogleFonts.roboto(
                                                        color: AppTheme
                                                            .primaryColor,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                        )
                                      : Container(
                                          margin: const EdgeInsets.all(4.0),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                                color: AppTheme.primaryColor),
                                          ),
                                          child: Center(
                                            child: Text(
                                              '${date.day}',
                                              style: GoogleFonts.roboto(
                                                color: AppTheme.primaryColor,
                                                fontWeight: FontWeight.w400,
                                                fontSize: 16,
                                              ),
                                            ),
                                          ),
                                        );
                            },
                            todayBuilder: (context, date, focusedDay) {
                              bool isSameDay =
                                  date.year == state.focusedDay.year &&
                                      date.month == state.focusedDay.month &&
                                      date.day == state.focusedDay.day;
                              return isSameDay
                                  ? Container(
                                      margin: const EdgeInsets.all(4.0),
                                      decoration: BoxDecoration(
                                        color: AppTheme.primaryColor,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: Text(
                                          '${date.day}',
                                          style: GoogleFonts.roboto(
                                            color: const Color(0xffFBF0D5),
                                            fontWeight: FontWeight.w400,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ),
                                    )
                                  : state.ovulationDays.any((ovulationDay) =>
                                          ovulationDay.year == date.year &&
                                          ovulationDay.month == date.month &&
                                          ovulationDay.day == date.day)
                                      ? Container(
                                          margin: const EdgeInsets.all(4.0),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Color(0xff71456f)
                                                .withOpacity(0.2),
                                          ),
                                          child: Center(
                                            child: Text(
                                              '${date.day}',
                                              style: GoogleFonts.roboto(
                                                color: const Color(0xff71456f),
                                                fontWeight: FontWeight.w400,
                                                fontSize: 16,
                                              ),
                                            ),
                                          ),
                                        )
                                      : Container(
                                          margin: const EdgeInsets.all(4.0),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                          ),
                                          child: Center(
                                            child: Text(
                                              '${date.day}',
                                              style: GoogleFonts.roboto(
                                                color: AppTheme.primaryColor,
                                                fontWeight: FontWeight.w400,
                                                fontSize: 16,
                                              ),
                                            ),
                                          ),
                                        );
                            },
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 20),
                    GestureDetector(
                      onTap: () {
                        if (_calendarFormat == CalendarFormat.week) {
                          // Switch to month view for editing period dates
                          setState(() {
                            _calendarFormat = CalendarFormat.month;
                          });

                          // Show instruction message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'Select/deselect your period dates. Only past and present dates can be edited.'),
                              backgroundColor: AppTheme.primaryColor,
                              duration: Duration(seconds: 3),
                            ),
                          );
                        } else {
                          // Save and recalculate when Done is clicked
                          context.read<ManagePeriodTrackingBloc>().add(
                                const ManagePeriodTrackingEvent
                                    .savePeriodDatesAndRecalculate(),
                              );

                          // Reset calendar to current day and switch to week view
                          setState(() {
                            _calendarFormat = CalendarFormat.week;
                            _focusedDay =
                                DateTime.now(); // Reset to current day
                          });

                          // Focus on current day in the bloc as well
                          context.read<ManagePeriodTrackingBloc>().add(
                              ManagePeriodTrackingEvent.selectDay(
                                  DateTime.now(), false));

                          // Show success message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'Period dates saved and future dates calculated!'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                      child: Container(
                        height: 50,
                        width: 200,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Center(
                          child: Text(
                            _calendarFormat == CalendarFormat.week
                                ? 'Change period dates'
                                : 'Done',
                            style: GoogleFonts.roboto(
                              color: Color(0xffFAF2DF),
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 20),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        '${DateFormat('EEEE, d MMMM').format(state.focusedDay)}',
                        textAlign: TextAlign.left,
                        style: GoogleFonts.roboto(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 18,
                        ),
                      ),
                    ),
                    SizedBox(height: 20),
                    Container(
                      width: 1.sw,
                      child: GridView.builder(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3, // Number of columns in the grid
                          crossAxisSpacing:
                              16.0, // Horizontal spacing between items
                          mainAxisSpacing:
                              16.0, // Vertical spacing between items
                          childAspectRatio: .9, // Aspect ratio for each item
                        ),
                        itemCount:
                            state.selectedPeriodTrackingDay.symptoms!.length +
                                3,
                        itemBuilder: (girdContext, index) {
                          if (index == 0) {
                            // First item - "Add Symptoms"
                            return GestureDetector(
                              key: Key('add_symptoms_button'),
                              onTap: () {
                                showModalBottomSheet<void>(
                                  context: context,
                                  isScrollControlled: true,
                                  builder: (buildContext) =>
                                      _buildBottomSheet(context),
                                );
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Color(0xffFAF2DF),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      blurRadius: 4.0,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(20)),
                                ),
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                          padding: EdgeInsets.only(top: 3),
                                          child: Icon(Icons.add,
                                              size: 45,
                                              color: AppTheme.primaryColor)),
                                      Container(
                                        padding: EdgeInsets.only(top: 5),
                                        child: Text(
                                          'Add\nSymptoms',
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.roboto(
                                            fontSize: 15,
                                            fontWeight: FontWeight.w400,
                                            color: AppTheme.primaryColor,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          } else if (index == 1) {
                            // First item - "Add Symptoms"
                            return GestureDetector(
                              onTap: () {
                                showModalBottomSheet<void>(
                                  context: context,
                                  isScrollControlled: true,
                                  builder: (buildContext) =>
                                      _buildBottomSheet(context),
                                );
                              },
                              child: Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: Color(0xffFAF2DF),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      blurRadius: 4.0,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(20)),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      state.selectedPeriodTrackingDay.painLevel
                                          .toString(),
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.roboto(
                                        fontSize: 36,
                                        fontWeight: FontWeight.bold,
                                        color: AppTheme.primaryColor,
                                      ),
                                    ),
                                    SizedBox(height: 5),
                                    Text(
                                      'Pain',
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.roboto(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w400,
                                        color: AppTheme.primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          } else if (index == 2) {
                            // First item - "Add Symptoms"
                            return GestureDetector(
                              onTap: () {
                                showModalBottomSheet<void>(
                                  context: context,
                                  isScrollControlled: true,
                                  builder: (buildContext) =>
                                      _buildBottomSheet(context),
                                );
                              },
                              child: Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: Color(0xffFAF2DF),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      blurRadius: 4.0,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(20)),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    SizedBox(height: 0),
                                    state.selectedPeriodTrackingDay.flowLevel ==
                                            1
                                        ? SvgPicture.asset(
                                            'assets/home/<USER>',
                                            color: AppTheme.primaryColor,
                                            height: 30,
                                            width: 30)
                                        : state.selectedPeriodTrackingDay
                                                    .flowLevel ==
                                                2
                                            ? SvgPicture.asset(
                                                'assets/home/<USER>',
                                                color: AppTheme.primaryColor,
                                                height: 30,
                                                width: 30)
                                            : SvgPicture.asset(
                                                'assets/home/<USER>',
                                                color: AppTheme.primaryColor,
                                                height: 30,
                                                width: 30),
                                    SizedBox(height: 2),
                                    Text(
                                      'Flow',
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.roboto(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w400,
                                        color: AppTheme.primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          } else {
                            // Symptoms items
                            final symptom = state
                                .selectedPeriodTrackingDay.symptoms![index - 3];
                            return Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Color(0xffFAF2DF),
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(0x40000000),
                                    blurRadius: 4.0,
                                    offset: Offset(0, 1),
                                  ),
                                ],
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20)),
                              ),
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(height: 0),
                                  SvgPicture.asset(
                                    'assets/home/<USER>' ', '_')}.svg',
                                    color: AppTheme.primaryColor,
                                    height: 40,
                                    width: 40,
                                  ),
                                  FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      symptom.name,
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.roboto(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w400,
                                        color: AppTheme.primaryColor,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            );
                          }
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        },
      );
    }));
  }
}
