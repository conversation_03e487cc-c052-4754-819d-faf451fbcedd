import 'package:auto_route/annotations.dart';
import 'package:design_system/design/theme.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:juno_plus/custom_widgets/curved_app_bar.dart';

@RoutePage()
class BarChartPage extends StatefulWidget {
  @override
  _BarChartPageState createState() => _BarChartPageState();
}

class _BarChartPageState extends State<BarChartPage> {
  final Color leftBarColor = const Color(0xffD46464);
  final Color middleBarColor = const Color(0xffF4BA4A);
  final Color rightBarColor = const Color(0xff3B7EFF);

  final double width = 8;
  final double groupSpace = 10; // Increase space between groups

  late List<BarChartGroupData> showingBarGroups;

  @override
  void initState() {
    super.initState();
    showingBarGroups = [
      makeGroupData(0, 4, 4, 5),
      makeGroupData(1, 5, 4, 5),
      makeGroupData(2, 6, 4, 5),
      makeGroupData(3, 7, 4, 5),
      makeGroupData(4, 8, 4, 5),
      makeGroupData(5, 9, 4, 5),
      makeGroupData(6, 10, 4, 5),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        height: .35.sw,
        topLeftIcon: IconButton(
          icon: Icon(Icons.arrow_back_rounded, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            RichText(text: TextSpan(
              text: 'The graph shows the ',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                color: Color(0xff30285D),
              ),
              children: [
                TextSpan(
                  text: 'TENS',
                  style: TextStyle(
                    color: leftBarColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(
                  text: ', ',
                ),
                TextSpan(
                  text: 'Heat',
                  style: TextStyle(
                    color: middleBarColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(
                  text: ', and calculated ',
                ),
                TextSpan(
                  text: 'Pain',
                  style: TextStyle(
                    color: rightBarColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(
                  text: ' levels based on the device usage history.',
                ),
              ],
            )),
            SizedBox(height: 20),
            Container(
              decoration: BoxDecoration(
                color: Color(0xffFAF2DF),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x40000000),
                    blurRadius: 4.0,
                    offset: Offset(0, 1),
                  ),
                ],
                borderRadius: BorderRadius.all(Radius.circular(32)),
              ),
              width: 1.sw,
              height: .75.sw,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 1.sw,
                      height: .5.sw,
                      child: BarChart(
                        BarChartData(
                          alignment: BarChartAlignment.spaceAround,
                          groupsSpace: groupSpace,
                          titlesData: FlTitlesData(
                            show: true,
                            bottomTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: bottomTitles,
                                reservedSize: 30,
                              ),
                            ),
                            leftTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: leftTitles,
                                reservedSize: 50,
                                interval: 2,
                              ),
                            ),
                            rightTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: false,
                                reservedSize: 0,
                              ),
                            ),
                            topTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: false,
                              ),
                            ),
                          ),
                          borderData: FlBorderData(show: false),
                          barGroups: showingBarGroups,
                          gridData: FlGridData(show: false),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),
                    Wrap(
                      spacing: 20,
                      children: [
                        LegendItem(color: leftBarColor, text: 'TENS'),
                        LegendItem(color: middleBarColor, text: 'Heat'),
                        LegendItem(color: rightBarColor, text: 'Pain'),
                      ],
                    ),
                  ],
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }

  Widget leftTitles(double value, TitleMeta meta) {
    const style = TextStyle(
      color: Color(0xff7589a2),
      fontWeight: FontWeight.bold,
      fontSize: 14,
    );
    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 16,
      child: Text(value.toString(), style: style),
    );
  }

  Widget bottomTitles(double value, TitleMeta meta) {
    const days = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
    final day = days[value.toInt() % days.length];
    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        day,
        style: const TextStyle(
          color: Color(0xff7589a2),
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  BarChartGroupData makeGroupData(int x, double y1, double y2, double y3) {
    return BarChartGroupData(
      barsSpace: 2,
      x: x,
      barRods: [
        BarChartRodData(
          toY: y1,
          color: leftBarColor,
          width: width,
        ),
        BarChartRodData(
          toY: y2,
          color: middleBarColor,
          width: width,
        ),
        BarChartRodData(
          toY: y3,
          color: rightBarColor,
          width: width,
        ),
      ],
    );
  }
}


class LegendItem extends StatelessWidget {
  final Color color;
  final String text;

  const LegendItem({required this.color, required this.text, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 4),
        Text(text, style: TextStyle(color: Color(0xff26204A), fontSize: 14)),
      ],
    );
  }
}

class BarChartMiniWidget extends StatefulWidget {
  const BarChartMiniWidget({super.key});

  @override
  State<BarChartMiniWidget> createState() => _BarChartMiniWidgetState();
}

class _BarChartMiniWidgetState extends State<BarChartMiniWidget> {
  final Color leftBarColor = const Color(0xffD46464);

  final Color middleBarColor = const Color(0xffF4BA4A);

  final Color rightBarColor = const Color(0xff3B7EFF);

  final double width = 7;

  late List<BarChartGroupData> showingBarGroups;

  @override
  void initState() {
    super.initState();
    final barGroup1 = makeGroupData(4, 4, 5, 3);
    final barGroup2 = makeGroupData(5, 4, 5, 3);
    final barGroup3 = makeGroupData(6, 4, 5, 3);
    final barGroup4 = makeGroupData(7, 4, 5, 3);
    final barGroup5 = makeGroupData(8, 4, 5, 3);

    // Continue for the rest of your data...

    showingBarGroups = [
      barGroup1,
      barGroup2,
      barGroup3,
      barGroup4,

      // Add the rest of your groups here...
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: .5.sw,
      child: Column(
        children: [
          Container(
            width: 1.sw,
            height: .2.sw,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.start,
                groupsSpace: 3,
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: false,
                      getTitlesWidget: bottomTitles,
                      reservedSize: 0,
                    ),
                  ),
                  rightTitles: AxisTitles(
                      sideTitles: SideTitles(
                    showTitles: false,
                    reservedSize: 0,
                  )),
                  topTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: false,
                      reservedSize: 10,
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: leftTitles,
                      reservedSize: 20,
                      interval: 10,
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: false,
                ),
                barGroups: showingBarGroups,
                gridData: FlGridData(show: false),
              ),
            ),
          ),
          SizedBox(
              height:
                  16), // Provide some space between the chart and the legend
          Wrap(
            spacing: 20, // Space between legend items
            children: [
              LegendItem(color: leftBarColor, text: 'TENS'),
              LegendItem(color: middleBarColor, text: 'Heat'),
              LegendItem(color: rightBarColor, text: 'Pain'),
            ],
          ),
        ],
      ),
    );
  }

  BarChartGroupData makeGroupData(int x, double y1, double y2, double y3) {
    return BarChartGroupData(
      barsSpace: 2,
      x: x,
      barRods: [
        BarChartRodData(
          toY: y1,
          color: leftBarColor,
          width: width,
        ),
        BarChartRodData(
          toY: y2,
          color: middleBarColor,
          width: width,
        ),
        BarChartRodData(
          toY: y3,
          color: rightBarColor,
          width: width,
        ),
      ],
    );
  }

  Widget leftTitles(double value, TitleMeta meta) {
    const style = TextStyle(
      color: Color(0xff7589a2),
      fontWeight: FontWeight.bold,
      fontSize: 14,
    );
    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 0,
      child: Text(value.toString(), style: style),
    );
  }

  Widget bottomTitles(double value, TitleMeta meta) {
    final titles = ['M', 'T', 'W', 'T', 'F'];

    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Text(
        titles[value.toInt()],
        style: const TextStyle(
          color: Color(0xff7589a2),
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }
}
