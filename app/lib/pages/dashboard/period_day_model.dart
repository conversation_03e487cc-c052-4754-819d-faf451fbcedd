class PeriodDay {
  final DateTime? date;
  final List<Symptom>? symptoms;
  final int? painLevel;
  final int? flowLevel;
  final bool? isSelected;

  PeriodDay(this.date, this.symptoms, this.painLevel, this.flowLevel,
      [this.isSelected = true]);

  PeriodDay copyWith({
    DateTime? date,
    List<Symptom>? symptoms,
    int? painLevel,
    int? flowLevel,
    bool? isSelected,
  }) {
    return PeriodDay(
      date ?? this.date,
      symptoms ?? this.symptoms,
      painLevel ?? this.painLevel,
      flowLevel ?? this.flowLevel,
      isSelected ?? this.isSelected,
    );
  }
}

class Symptom {
  String name;
  Symptom(this.name);
}
