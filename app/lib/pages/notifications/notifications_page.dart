import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:notifications/application/manage_notifications_bloc/manage_notifications_bloc.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'package:timeago/timeago.dart' as TimeAgo;

@RoutePage()
class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ManageNotificationsBloc>()..add(const ManageNotificationsEvent.loadNotifications()),
      child: BlocConsumer<ManageNotificationsBloc, ManageNotificationsState>(
        listener: (context, state) {
          if (state is DeleteSuccess) {
            Fluttertoast.showToast(msg: 'Notification cleared successfully');
            context.read<ManageNotificationsBloc>().add(const ManageNotificationsEvent.loadNotifications());
          }
        },
        builder: (context, state) {
          return state.map(
              deleteSuccess: (_) => const Center(child: CircularProgressIndicator()), // Show loading while reloading
              failure: (failure) => Center(child: Text(failure.failure.failureMessage)),
              initial: (_) => const Center(child: CircularProgressIndicator()),
          loading: (_) => const Center(child: CircularProgressIndicator()),
          success: (notifications) =>Scaffold(
        appBar: CurvedAppBar(
          topLeftIcon: IconButton(
            icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
            onPressed: () {
              context.router.back();

            },
          ),
          topRightIcon: GestureDetector(
            onTap: () {
              context.read<ManageNotificationsBloc>().add(const ManageNotificationsEvent.deleteAllNotifications());
            },
            child: Container(
              height: 40,
              width: 40,
              child: const Padding(
                padding: EdgeInsets.all(6.0),
                child: Icon(Icons.clear_all_outlined, color: Colors.white),
              ),
            ),
          ),
          appBarColor: AppTheme.primaryColor,
          logoColor: Color(0xffFAF2DF),
          height: .35.sw,
         // The height of your curved app bar
        ),
        body:  notifications.notifications.isEmpty?
              Center(child: Text('You have no notifications yet!',style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.grey),)):
                  ListView.builder(
                itemCount: notifications.notifications.length,
                itemBuilder: (context, index) {
                  final notification = notifications.notifications[index];
                  return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Card(
                      elevation: 4.0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15.0),
                      ),
                      child: ListTile(
                        title: Text(notification.title!,style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.bold,color: Colors.black,fontSize: 18)),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(notification.body!,maxLines: 2,overflow: TextOverflow.ellipsis,style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.grey),),
                            Text('received at: ${TimeAgo.format(notification.receivedAt!)}'),
                          ],
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () {
                            context.read<ManageNotificationsBloc>().add(
                              ManageNotificationsEvent.deleteNotification(notification.id!),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
            ));
          },
        ),

    );
  }
}
