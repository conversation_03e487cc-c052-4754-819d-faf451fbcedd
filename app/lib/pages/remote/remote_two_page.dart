import 'dart:async';
import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/bluetooth.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:juno_plus/pages/remote/battery_indicator_widget.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:juno_plus/pages/remote/tens_mode.dart';
import 'package:juno_plus/pages/remote/tens_widget.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:remote/application/device_control_heat_bloc/device_control_heat_bloc.dart';
import 'package:remote/application/device_control_heat_watcher_bloc/device_control_heat_watcher_bloc.dart';
import 'package:remote/application/device_control_tens_bloc/device_control_tens_bloc.dart';
import 'package:remote/application/device_control_tens_watcher_bloc/device_control_tens_watcher_bloc.dart';
import 'package:remote/application/device_status_watcher_bloc/device_status_watcher_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:showcaseview/showcaseview.dart';
import '../../helpers.dart';
import 'heat_widget.dart';

//global variable to be deleted
bool _showCase = false;

@RoutePage()
class RemoteTwoPage extends StatefulWidget {
  @override
  State<RemoteTwoPage> createState() => _RemoteTwoPageState();
}

class _RemoteTwoPageState extends State<RemoteTwoPage> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<DeviceControlHeatBloc>(
          create: (context) => getIt<DeviceControlHeatBloc>(),
        ),
        BlocProvider<DeviceControlHeatWatcherBloc>(
          create: (context) => getIt<DeviceControlHeatWatcherBloc>()
            ..add(const DeviceControlHeatWatcherEvent.watchAllStarted()),
        ),
        BlocProvider<DeviceControlTensBloc>(
          create: (context) => getIt<DeviceControlTensBloc>(),
        ),
        BlocProvider<DeviceControlTensWatcherBloc>(
          create: (context) => getIt<DeviceControlTensWatcherBloc>()
            ..add(const DeviceControlTensWatcherEvent.watchAllStarted()),
        ),
        BlocProvider<DeviceStatusWatcherBloc>(
            create: (context) => getIt<DeviceStatusWatcherBloc>()
              ..add(const DeviceStatusWatcherEvent.watchAllStarted())),
      ],
      child: ShowCaseWidget(
        enableShowcase: false,
        builder: (context) {
          return RemoteScaffold();
        },
      ),
    );
  }
}

class RemoteScaffold extends StatefulWidget {
  const RemoteScaffold({super.key});

  @override
  State<RemoteScaffold> createState() => _RemoteScaffoldState();
}

class _RemoteScaffoldState extends State<RemoteScaffold> {
  final GlobalKey _tensExplainerKey = GlobalKey();
  final GlobalKey _heatExplainerKey = GlobalKey();
  final GlobalKey _syncButtonKey = GlobalKey();
  final GlobalKey _helpButtonKey = GlobalKey();
  final GlobalKey _settingsButtonKey = GlobalKey();
  final GlobalKey _modesExplainerKey = GlobalKey();
  final GlobalKey _playPauseExplainerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // _checkBinding();
  }

  Future<void> _checkBinding() async {
    if (!_showCase) {
      Future.delayed(Duration(milliseconds: 500), () {
        ShowCaseWidget.of(context).startShowCase([
          _tensExplainerKey,
          _modesExplainerKey,
          _heatExplainerKey,
          _syncButtonKey,
          _playPauseExplainerKey,
          _settingsButtonKey,
          _helpButtonKey
        ]);
      });

      _showCase = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.white,
        child: SafeArea(
          bottom: false,
          child: Scaffold(
            appBar: PreferredSize(
                preferredSize: Size.fromHeight(250.dg),
                child: Container(
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: 25.0, right: 25.0, top: 10, bottom: 3),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            context.router.replace(HomeRoute());
                          },
                          key: Key('back_button'),
                          child: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              color: Color(0xffFAF2DF),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.6),
                                  offset: Offset(4, 4),
                                  blurRadius: 10,
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(6.0),
                              child: Icon(
                                Icons.arrow_back_rounded,
                                color: Color(0xff30285D),
                                size: 35,
                              ),
                            ),
                          ),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Container(
                                    width: 60,
                                    child: SvgPicture.asset(
                                      'assets/logo/juno_logo_violet.svg',
                                      height: 35,
                                      color: AppTheme.primaryColor,
                                      fit: BoxFit.fitWidth,
                                    )),
                                SizedBox(
                                  width: 5,
                                ),
                                Text(
                                  '+',
                                  style: GoogleFonts.poppins(
                                    color: AppTheme.primaryColor,
                                    fontSize: 30,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            Text(
                              'remote',
                              style: GoogleFonts.poppins(
                                color: AppTheme.primaryColor,
                                fontSize: 14,
                                fontStyle: FontStyle.italic,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        GestureDetector(
                          onTap: () {
                            context.router.push(HelpCenterHomeRoute());
                          },
                          child: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              color: Color(0xffFAF2DF),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.6),
                                  offset: Offset(4, 4),
                                  blurRadius: 10,
                                ),
                              ],
                            ),
                            child: Showcase(
                              key: _helpButtonKey,
                              title: 'Help',
                              description:
                                  'Press the help button to access the help center.',
                              descTextStyle: GoogleFonts.roboto(
                                color: AppTheme.primaryColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                              tooltipBorderRadius: BorderRadius.circular(20),
                              tooltipBackgroundColor: Color(0xffFAF2DF),
                              tooltipPadding: EdgeInsets.all(20),
                              titleTextStyle: GoogleFonts.roboto(
                                color: AppTheme.primaryColor,
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                              ),
                              targetShapeBorder: CircleBorder(),
                              child: Padding(
                                padding: const EdgeInsets.all(6.0),
                                child: Icon(
                                  Icons.help_outline_sharp,
                                  color: Color(0xff30285D),
                                  size: 35,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )),
            body: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20.0, vertical: 10),
                    child: Container(
                        width: .9.sw,
                        decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                color: Color(0x40000000),
                                // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                blurRadius: 4.0,
                                // the blur radius
                                offset: Offset(
                                    0, 1), // the x,y offset of the shadow
                              ),
                            ],
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(32)),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  Container(
                                    height: .075.sh,
                                    width: .26.sw,
                                    decoration: BoxDecoration(
                                      color: Color(0xffFAF2DF),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(26),
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(6.0),
                                      child: Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color:
                                                  Colors.grey.withOpacity(0.6),
                                              offset: Offset(4, 4),
                                              blurRadius: 10,
                                            ),
                                            // BoxShadow(
                                            //   color: Colors.white,
                                            //   offset: Offset(-4, -4),
                                            //   blurRadius: 10,
                                            // ),
                                          ],
                                        ),
                                        child: Center(
                                          child: Showcase(
                                            key: _settingsButtonKey,
                                            title: 'Settings',
                                            description:
                                                'Press the settings button to access the device settings.',
                                            descTextStyle: GoogleFonts.roboto(
                                              color: AppTheme.primaryColor,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                            ),
                                            tooltipBorderRadius:
                                                BorderRadius.circular(20),
                                            tooltipBackgroundColor:
                                                Color(0xffFAF2DF),
                                            tooltipPadding: EdgeInsets.all(20),
                                            titleTextStyle: GoogleFonts.roboto(
                                              color: AppTheme.primaryColor,
                                              fontSize: 20,
                                              fontWeight: FontWeight.w700,
                                            ),
                                            targetShapeBorder: CircleBorder(),
                                            child: GestureDetector(
                                              child: Icon(
                                                Icons.settings,
                                                size: 40,
                                              ),
                                              onTap: () {
                                                _connectionCheck();
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      _connectToDevice();
                                    },
                                    child: Container(
                                      height: .075.sh,
                                      width: .28.sw,
                                      decoration: BoxDecoration(
                                        color: Color(0xffFAF2DF),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Center(
                                            child: BatteryIndicatorWidget()),
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Container(
                                    height: .075.sh,
                                    width: .26.sw,
                                    decoration: BoxDecoration(
                                      color: Color(0xffFAF2DF),
                                      borderRadius: BorderRadius.only(
                                        topRight: Radius.circular(26),
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(6.0),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color:
                                                  Colors.grey.withOpacity(0.6),
                                              offset: Offset(4, 4),
                                              blurRadius: 10,
                                            ),
                                            // BoxShadow(
                                            //   color: Colors.white,
                                            //   offset: Offset(-4, -4),
                                            //   blurRadius: 10,
                                            // ),
                                          ],
                                        ),
                                        child: Center(
                                          child: GestureDetector(
                                            child: Icon(
                                              Icons.power_settings_new_rounded,
                                              size: 40,
                                            ),
                                            onTap: () {
                                              _startScan();
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              SizedBox(
                                height: .01.sh,
                              ),
                              Stack(
                                alignment: Alignment.center,
                                children: [
                                  Positioned(
                                    top: 0,
                                    left: 0,
                                    child: Showcase(
                                      key: _tensExplainerKey,
                                      targetBorderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(32),
                                      ),
                                      title: 'TENS',
                                      description:
                                          'TENS (Transcutaneous Electrical Nerve Stimulation) uses gentle electrical pulses to help relieve pain and reduce muscle spasms. It has 10 intensity levels that can be adjusted using the + and - buttons. Please set the modes first before adjusting the intensity.',
                                      descTextStyle: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      tooltipBorderRadius:
                                          BorderRadius.circular(20),
                                      tooltipBackgroundColor: Color(0xffFAF2DF),
                                      tooltipPadding: EdgeInsets.all(20),
                                      titleTextStyle: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700,
                                      ),
                                      child: Container(
                                        width: .5.sw - 13,
                                        height: .685.sh,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.only(
                                            bottomLeft: Radius.circular(32),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Container(
                                            width: .5.sw - 13,
                                            height: .55.sh,
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: .48.sw,
                                                  height: .55.sh,
                                                  decoration: BoxDecoration(
                                                    color: Color(0xffFAF2DF),
                                                  ),
                                                  child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        SizedBox(
                                                          height: 15,
                                                        ),
                                                        Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: [
                                                            SizedBox(
                                                              width: .11.sw,
                                                            ),
                                                            SvgPicture.asset(
                                                              'assets/remote/remote_charge.svg',
                                                              width: 23,
                                                              height: 23,
                                                            ),
                                                            SizedBox(
                                                              width: 10,
                                                            ),
                                                            GestureDetector(
                                                              onTap: () {
                                                                showDialog(
                                                                  context:
                                                                      context,
                                                                  builder:
                                                                      (BuildContext
                                                                          context) {
                                                                    return AlertDialog(
                                                                      backgroundColor:
                                                                          Color(
                                                                              0xffFAF2DF),
                                                                      title:
                                                                          Text(
                                                                        'TENS',
                                                                        style: TextStyle(
                                                                            color: Color(
                                                                                0xff26204A),
                                                                            fontSize:
                                                                                20,
                                                                            fontWeight:
                                                                                FontWeight.w500),
                                                                      ),
                                                                      content:
                                                                          Text(
                                                                        'TENS (Transcutaneous Electrical Nerve Stimulation) is a type of therapy that uses gentle electrical pulses to help relieve pain and reduce muscle spasms. With 10 adjustable intensity levels, TENS can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                                        style: TextStyle(
                                                                            color:
                                                                                Color(0xff26204A),
                                                                            fontSize: 16),
                                                                      ),
                                                                      actions: [
                                                                        TextButton(
                                                                          onPressed:
                                                                              () {
                                                                            Navigator.of(context).pop();
                                                                          },
                                                                          child:
                                                                              Text('OK'),
                                                                        ),
                                                                      ],
                                                                    );
                                                                  },
                                                                );
                                                              },
                                                              child: Icon(
                                                                Icons
                                                                    .info_outline,
                                                                size: 23,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        RichText(
                                                            text: TextSpan(
                                                                text: 'TENS ',
                                                                style: Theme.of(
                                                                        context)
                                                                    .textTheme
                                                                    .bodyMedium!
                                                                    .copyWith(
                                                                        color: Color(
                                                                            0xff26204A),
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w400,
                                                                        fontSize:
                                                                            20),
                                                                children: [
                                                              TextSpan(
                                                                  text:
                                                                      'Intensity',
                                                                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                                                      fontStyle:
                                                                          FontStyle
                                                                              .italic,
                                                                      color: Color(
                                                                          0xff26204A),
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w400,
                                                                      fontSize:
                                                                          16))
                                                            ])),
                                                        SizedBox(
                                                          height: 10,
                                                        ),
                                                        Container(
                                                          child: Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              // Increase Button
                                                              Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              30),
                                                                  boxShadow: [
                                                                    BoxShadow(
                                                                      color: Colors
                                                                          .grey
                                                                          .withOpacity(
                                                                              0.6),
                                                                      offset:
                                                                          Offset(
                                                                              4,
                                                                              4),
                                                                      blurRadius:
                                                                          10,
                                                                    ),
                                                                  ],
                                                                ),
                                                                child:
                                                                    IconButton(
                                                                  key: const Key(
                                                                      'tens_increase_button'),
                                                                  icon: Icon(
                                                                    Icons.add,
                                                                    size: 28,
                                                                  ),
                                                                  onPressed:
                                                                      () async {
                                                                    //write +1 to this characteristic a1a2a3a4-b1b2-c1c2-d1d2-e1e2e3e4e5e7 service a1a2a3a4-b1b2-c1c2-d1d2-e1e2e3e4e5e6

                                                                    actualTensLevel =
                                                                        actualTensLevel +
                                                                            1;
                                                                    List<BluetoothService>
                                                                        services =
                                                                        await devices
                                                                            .first
                                                                            .discoverServices();

                                                                    services.forEach(
                                                                        (service) {
                                                                      print(service
                                                                          .uuid);
                                                                      if (service
                                                                              .uuid ==
                                                                          Guid(
                                                                              '23289aa0-670c-4635-8b38-e1ab58c0e9c4')) {
                                                                        print(
                                                                            'Service found');
                                                                        service
                                                                            .characteristics
                                                                            .forEach((characteristic) {
                                                                          print(
                                                                              characteristic.uuid);
                                                                          if (characteristic.uuid ==
                                                                              Guid('23289aa3-670c-44f3-8b38-e1ab58c0e9c4')) {
                                                                            print('Characteristic found');
                                                                            characteristic.write([
                                                                              3
                                                                            ]);
                                                                          }
                                                                        });
                                                                      }
                                                                    });
                                                                    _pollTensLevel();
                                                                    setState(
                                                                        () {});
                                                                  },
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                  height: 8),
                                                              // Tens Level Indicator
                                                              Container(
                                                                width: 35,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              30),
                                                                  boxShadow: [
                                                                    BoxShadow(
                                                                      color: Colors
                                                                          .grey
                                                                          .withOpacity(
                                                                              0.6),
                                                                      offset:
                                                                          Offset(
                                                                              4,
                                                                              4),
                                                                      blurRadius:
                                                                          10,
                                                                    ),
                                                                    BoxShadow(
                                                                      color: Colors
                                                                          .white,
                                                                      offset:
                                                                          Offset(
                                                                              -4,
                                                                              -4),
                                                                      blurRadius:
                                                                          10,
                                                                    ),
                                                                  ],
                                                                ),
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .all(
                                                                          5.0),
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .end,
                                                                    children: List
                                                                        .generate(
                                                                            10,
                                                                            (index) {
                                                                      int levelIndex =
                                                                          9 - index;

                                                                      // Determine color based on the actual and selected Tens levels
                                                                      final color = levelIndex <
                                                                              actualTensLevel
                                                                          ? AppTheme
                                                                              .primaryColor // Displayed as the actual level
                                                                          : levelIndex < selectedTensLevel
                                                                              ? Colors.white // Displayed as the selected level above actual
                                                                              : Colors.grey[300]; // Gray for levels above selected

                                                                      return Container(
                                                                        height:
                                                                            64.dg,
                                                                        margin: EdgeInsets.symmetric(
                                                                            vertical:
                                                                                1),
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          color:
                                                                              color,
                                                                          borderRadius:
                                                                              BorderRadius.only(
                                                                            topLeft: levelIndex == 9
                                                                                ? Radius.circular(30)
                                                                                : Radius.zero,
                                                                            topRight: levelIndex == 9
                                                                                ? Radius.circular(30)
                                                                                : Radius.zero,
                                                                            bottomLeft: levelIndex == 0
                                                                                ? Radius.circular(30)
                                                                                : Radius.zero,
                                                                            bottomRight: levelIndex == 0
                                                                                ? Radius.circular(30)
                                                                                : Radius.zero,
                                                                          ),
                                                                        ),
                                                                      );
                                                                    }),
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                  height: 8),
                                                              // Decrease Button
                                                              Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              30),
                                                                  boxShadow: [
                                                                    BoxShadow(
                                                                      color: Colors
                                                                          .grey
                                                                          .withOpacity(
                                                                              0.6),
                                                                      offset:
                                                                          Offset(
                                                                              4,
                                                                              4),
                                                                      blurRadius:
                                                                          10,
                                                                    ),
                                                                  ],
                                                                ),
                                                                child:
                                                                    IconButton(
                                                                  key: const Key(
                                                                      'tens_decrease_button'),
                                                                  icon: Icon(
                                                                    Icons
                                                                        .remove,
                                                                    size: 28,
                                                                  ),
                                                                  onPressed:
                                                                      () async {
                                                                    actualTensLevel =
                                                                        actualTensLevel -
                                                                            1;
                                                                    List<BluetoothService>
                                                                    services =
                                                                    await devices
                                                                        .first
                                                                        .discoverServices();

                                                                    services.forEach(
                                                                            (service) {
                                                                          print(service
                                                                              .uuid);
                                                                          if (service
                                                                              .uuid ==
                                                                              Guid(
                                                                                  '23289aa0-670c-4635-8b38-e1ab58c0e9c4')) {
                                                                            print(
                                                                                'Service found');
                                                                            service
                                                                                .characteristics
                                                                                .forEach((characteristic) {
                                                                              print(
                                                                                  characteristic.uuid);
                                                                              if (characteristic.uuid ==
                                                                                  Guid('23289aa4-670c-44f3-8b38-e1ab58c0e9c4')) {
                                                                                print('Characteristic found');
                                                                                characteristic.write([
                                                                                  -1
                                                                                ]);
                                                                              }
                                                                            });
                                                                          }
                                                                        });
                                                                    _pollTensLevel();
                                                                    setState(
                                                                        () {});
                                                                  },
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          height: 15,
                                                        ),
                                                      ]),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Container(
                                            width: .4.sw - 13,
                                            height: .55.sh,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                            ),
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Showcase(
                                                  key: _heatExplainerKey,
                                                  title: 'Heat',
                                                  description:
                                                      'Heat therapy is a type of treatment that uses heat to relieve pain and stiffness. With 3 adjustable levels, heat therapy can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                  descTextStyle:
                                                      GoogleFonts.roboto(
                                                    color:
                                                        AppTheme.primaryColor,
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                  tooltipBorderRadius:
                                                      BorderRadius.circular(20),
                                                  tooltipBackgroundColor:
                                                      Color(0xffFAF2DF),
                                                  tooltipPadding:
                                                      EdgeInsets.all(20),
                                                  titleTextStyle:
                                                      GoogleFonts.roboto(
                                                    color:
                                                        AppTheme.primaryColor,
                                                    fontSize: 20,
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                  child: Container(
                                                    width: .4.sw - 13,
                                                    height: .415.sh,
                                                    decoration: BoxDecoration(
                                                      color: Color(0xffFAF2DF)
                                                          .withOpacity(.4),
                                                    ),
                                                    child: Column(
                                                      children: [
                                                        SizedBox(
                                                          height: 15,
                                                        ),
                                                        Row(
                                                          children: [
                                                            SizedBox(
                                                              width: .16.sw,
                                                            ),
                                                            SvgPicture.asset(
                                                              'assets/remote/remote_heat.svg',
                                                              width: 23,
                                                              height: 23,
                                                            ),
                                                            SizedBox(
                                                              width: 10,
                                                            ),
                                                            GestureDetector(
                                                              onTap: () {
                                                                showDialog(
                                                                  context:
                                                                      context,
                                                                  builder:
                                                                      (BuildContext
                                                                          context) {
                                                                    return AlertDialog(
                                                                      backgroundColor:
                                                                          Color(
                                                                              0xffFAF2DF),
                                                                      title:
                                                                          Text(
                                                                        'Heat',
                                                                        style: TextStyle(
                                                                            color: Color(
                                                                                0xff26204A),
                                                                            fontSize:
                                                                                20,
                                                                            fontWeight:
                                                                                FontWeight.w500),
                                                                      ),
                                                                      content:
                                                                          Text(
                                                                        'Heat therapy is a type of treatment that uses heat to relieve pain and stiffness. With 3 adjustable levels, heat therapy can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                                        style: TextStyle(
                                                                            color:
                                                                                Color(0xff26204A),
                                                                            fontSize: 16),
                                                                      ),
                                                                      actions: [
                                                                        TextButton(
                                                                          onPressed:
                                                                              () {
                                                                            Navigator.of(context).pop();
                                                                          },
                                                                          child:
                                                                              Text('OK'),
                                                                        ),
                                                                      ],
                                                                    );
                                                                  },
                                                                );
                                                              },
                                                              child: Icon(
                                                                Icons
                                                                    .info_outline,
                                                                size: 23,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        SizedBox(
                                                          height: 10,
                                                        ),
                                                        Text(
                                                          'Heat',
                                                          style: Theme
                                                                  .of(context)
                                                              .textTheme
                                                              .bodyMedium!
                                                              .copyWith(
                                                                  color: Color(
                                                                      0xff26204A),
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                  fontSize: 20),
                                                        ),
                                                        SizedBox(
                                                          height: 10,
                                                        ),
                                                        Container(
                                                            child:
                                                            Container(
                                                              child: Column(
                                                                mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                                children: [
                                                                  // Increase Button
                                                                  Container(
                                                                    decoration:
                                                                    BoxDecoration(
                                                                      color: Colors
                                                                          .white,
                                                                      borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                          30),
                                                                      boxShadow: [
                                                                        BoxShadow(
                                                                          color: Colors
                                                                              .grey
                                                                              .withOpacity(
                                                                              0.6),
                                                                          offset:
                                                                          Offset(
                                                                              4,
                                                                              4),
                                                                          blurRadius:
                                                                          10,
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    child:
                                                                    IconButton(
                                                                      key: const Key(
                                                                          'heat_increase_button'),
                                                                      icon: Icon(
                                                                        Icons.add,
                                                                        size: 28,
                                                                      ),
                                                                      onPressed:
                                                                          () async {
                                                                        //write +1 to this characteristic a1a2a3a4-b1b2-c1c2-d1d2-e1e2e3e4e5e7 service a1a2a3a4-b1b2-c1c2-d1d2-e1e2e3e4e5e6

                                                                        actualHeatLevel =
                                                                            actualHeatLevel +
                                                                                1;
                                                                        List<BluetoothService>
                                                                        services =
                                                                        await devices
                                                                            .first
                                                                            .discoverServices();

                                                                        services.forEach(
                                                                                (service) {
                                                                              print(service
                                                                                  .uuid);
                                                                              if (service
                                                                                  .uuid ==
                                                                                  Guid(
                                                                                      'd0b12770-6b7d-4635-ab5a-e3abdd621537')) {
                                                                                print(
                                                                                    'Service found');
                                                                                service
                                                                                    .characteristics
                                                                                    .forEach((characteristic) {
                                                                                  print(
                                                                                      characteristic.uuid);
                                                                                  if (characteristic.uuid ==
                                                                                      Guid('d0b12771-6b7d-4635-ab5a-e3abdd621537')) {
                                                                                    print('Characteristic found');
                                                                                    characteristic.write([
                                                                                      2
                                                                                    ]);
                                                                                  }
                                                                                });
                                                                              }
                                                                            });
                                                                        _pollTensLevel();
                                                                        setState(
                                                                                () {});
                                                                      },
                                                                    ),
                                                                  ),
                                                                  SizedBox(
                                                                      height: 8),
                                                                  // Tens Level Indicator
                                                                  Container(
                                                                    width: 35,
                                                                    decoration:
                                                                    BoxDecoration(
                                                                      color: Colors
                                                                          .white,
                                                                      borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                          30),
                                                                      boxShadow: [
                                                                        BoxShadow(
                                                                          color: Colors
                                                                              .grey
                                                                              .withOpacity(
                                                                              0.6),
                                                                          offset:
                                                                          Offset(
                                                                              4,
                                                                              4),
                                                                          blurRadius:
                                                                          10,
                                                                        ),
                                                                        BoxShadow(
                                                                          color: Colors
                                                                              .white,
                                                                          offset:
                                                                          Offset(
                                                                              -4,
                                                                              -4),
                                                                          blurRadius:
                                                                          10,
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    child: Padding(
                                                                      padding:
                                                                      const EdgeInsets
                                                                          .all(
                                                                          5.0),
                                                                      child: Column(
                                                                        mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .end,
                                                                        children: List
                                                                            .generate(
                                                                            3,
                                                                                (index) {
                                                                              int levelIndex =
                                                                                  2 - index;

                                                                              // Determine color based on the actual and selected Tens levels
                                                                              final color = levelIndex <
                                                                                  actualHeatLevel
                                                                                  ? Colors.orange // Displayed as the actual level
                                                                                  : levelIndex < selectedTensLevel
                                                                                  ? Colors.white // Displayed as the selected level above actual
                                                                                  : Colors.grey[300]; // Gray for levels above selected

                                                                              return Container(
                                                                                height:
                                                                                64.dg,
                                                                                margin: EdgeInsets.symmetric(
                                                                                    vertical:
                                                                                    1),
                                                                                decoration:
                                                                                BoxDecoration(
                                                                                  color:
                                                                                  color,
                                                                                  borderRadius:
                                                                                  BorderRadius.only(
                                                                                    topLeft: levelIndex == 2
                                                                                        ? Radius.circular(30)
                                                                                        : Radius.zero,
                                                                                    topRight: levelIndex == 2
                                                                                        ? Radius.circular(30)
                                                                                        : Radius.zero,
                                                                                    bottomLeft: levelIndex == 0
                                                                                        ? Radius.circular(30)
                                                                                        : Radius.zero,
                                                                                    bottomRight: levelIndex == 0
                                                                                        ? Radius.circular(30)
                                                                                        : Radius.zero,
                                                                                  ),
                                                                                ),
                                                                              );
                                                                            }),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  SizedBox(
                                                                      height: 8),
                                                                  // Decrease Button
                                                                  Container(
                                                                    decoration:
                                                                    BoxDecoration(
                                                                      color: Colors
                                                                          .white,
                                                                      borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                          30),
                                                                      boxShadow: [
                                                                        BoxShadow(
                                                                          color: Colors
                                                                              .grey
                                                                              .withOpacity(
                                                                              0.6),
                                                                          offset:
                                                                          Offset(
                                                                              4,
                                                                              4),
                                                                          blurRadius:
                                                                          10,
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    child:
                                                                    IconButton(
                                                                      key: const Key(
                                                                          'heat_decrease_button'),
                                                                      icon: Icon(
                                                                        Icons
                                                                            .remove,
                                                                        size: 28,
                                                                      ),
                                                                      onPressed:
                                                                          () async {
                                                                        actualHeatLevel =
                                                                            actualHeatLevel -
                                                                                1;
                                                                        List<BluetoothService>
                                                                        services =
                                                                        await devices
                                                                            .first
                                                                            .discoverServices();

                                                                        services.forEach(
                                                                                (service) {
                                                                              print(service
                                                                                  .uuid);
                                                                              if (service
                                                                                  .uuid ==
                                                                                  Guid(
                                                                                      'd0b12770-6b7d-4635-ab5a-e3abdd621537')) {
                                                                                print(
                                                                                    'Service found');
                                                                                service
                                                                                    .characteristics
                                                                                    .forEach((characteristic) {
                                                                                  print(
                                                                                      characteristic.uuid);
                                                                                  if (characteristic.uuid ==
                                                                                      Guid('d0b12771-6b7d-4635-ab5a-e3abdd621537')) {
                                                                                    print('Characteristic found');
                                                                                    characteristic.write([
                                                                                      20
                                                                                    ]);
                                                                                  }
                                                                                });
                                                                              }
                                                                            });
                                                                        _pollTensLevel();
                                                                        setState(
                                                                                () {});
                                                                      },
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                Showcase(
                                                  key: _playPauseExplainerKey,
                                                  title: 'Play/Pause',
                                                  description:
                                                      'You can pause and resume the therapy session by pressing the play/pause button.',
                                                  descTextStyle:
                                                      GoogleFonts.roboto(
                                                    color:
                                                        AppTheme.primaryColor,
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                  tooltipBorderRadius:
                                                      BorderRadius.circular(20),
                                                  tooltipBackgroundColor:
                                                      Color(0xffFAF2DF),
                                                  tooltipPadding:
                                                      EdgeInsets.all(20),
                                                  titleTextStyle:
                                                      GoogleFonts.roboto(
                                                    color:
                                                        AppTheme.primaryColor,
                                                    fontSize: 20,
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                  child: Container(
                                                    width: .4.sw - 13,
                                                    height: .125.sh,
                                                    decoration: BoxDecoration(
                                                      color: Color(0xffFAF2DF),
                                                    ),
                                                    child: Center(
                                                      child: Container(
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(50),
                                                          boxShadow: [
                                                            BoxShadow(
                                                              color: Colors.grey
                                                                  .withOpacity(
                                                                      0.6),
                                                              offset:
                                                                  Offset(4, 4),
                                                              blurRadius: 10,
                                                            ),
                                                            // BoxShadow(
                                                            //   color: Colors.white,
                                                            //   offset: Offset(-4, -4),
                                                            //   blurRadius: 10,
                                                            // ),
                                                          ],
                                                        ),
                                                        child: IconButton(
                                                          icon: Icon(
                                                            Icons
                                                                .play_circle_outline_rounded,
                                                            size: 50,
                                                          ),
                                                          onPressed: () async {
                                                            _pollTensLevel();
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: .01.sh,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Showcase(
                                            key: _modesExplainerKey,
                                            title: 'Modes',
                                            description:
                                                'The TENS unit has 3 modes: Pulse, Constant, and Burst. Pulse mode delivers a series of electrical pulses in a pattern, Constant mode delivers a continuous electrical pulse, and Burst mode delivers a series of electrical pulses in rapid succession. Each mode provides a different type of pain relief, so you can choose the Two that works best for you.',
                                            targetBorderRadius:
                                                BorderRadius.only(
                                              bottomLeft: Radius.circular(26),
                                            ),
                                            descTextStyle: GoogleFonts.roboto(
                                              color: AppTheme.primaryColor,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                            ),
                                            tooltipBorderRadius:
                                                BorderRadius.circular(20),
                                            tooltipBackgroundColor:
                                                Color(0xffFAF2DF),
                                            tooltipPadding: EdgeInsets.all(20),
                                            titleTextStyle: GoogleFonts.roboto(
                                              color: AppTheme.primaryColor,
                                              fontSize: 20,
                                              fontWeight: FontWeight.w700,
                                            ),
                                            child: Container(
                                              height: .125.sh,
                                              width: .5.sw - 13,
                                              decoration: BoxDecoration(
                                                color: Color(0xffFAF2DF),
                                                borderRadius: BorderRadius.only(
                                                  bottomLeft:
                                                      Radius.circular(26),
                                                ),
                                              ),
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  SizedBox(
                                                    height: 10,
                                                  ),
                                                  Row(
                                                    children: [
                                                      SizedBox(
                                                        width: .15.sw,
                                                      ),
                                                      Text(
                                                        'Modes',
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium!
                                                            .copyWith(
                                                                color: Color(
                                                                    0xff26204A),
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400,
                                                                fontSize: 16),
                                                      ),
                                                      SizedBox(
                                                        width: 10,
                                                      ),
                                                      GestureDetector(
                                                        onTap: () {
                                                          showDialog(
                                                            context: context,
                                                            builder:
                                                                (BuildContext
                                                                    context) {
                                                              return AlertDialog(
                                                                backgroundColor:
                                                                    Color(
                                                                        0xffFAF2DF),
                                                                title: Text(
                                                                  'Modes',
                                                                  style: TextStyle(
                                                                      color: Color(
                                                                          0xff26204A),
                                                                      fontSize:
                                                                          20,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w500),
                                                                ),
                                                                content: Text(
                                                                  'The TENS unit has 3 modes: Pulse, Constant, and Burst. Pulse mode delivers a series of electrical pulses in a pattern, Constant mode delivers a continuous electrical pulse, and Burst mode delivers a series of electrical pulses in rapid succession. Each mode provides a different type of pain relief, so you can choose the Two that works best for you.',
                                                                  style: TextStyle(
                                                                      color: Color(
                                                                          0xff26204A),
                                                                      fontSize:
                                                                          16),
                                                                ),
                                                                actions: [
                                                                  TextButton(
                                                                    onPressed:
                                                                        () {
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();
                                                                    },
                                                                    child: Text(
                                                                        'OK'),
                                                                  ),
                                                                ],
                                                              );
                                                            },
                                                          );
                                                        },
                                                        child: Icon(
                                                          Icons.info_outline,
                                                          size: 23,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(
                                                    height: 10,
                                                  ),
                                                  TensMode()
                                                ],
                                              ),
                                            ),
                                          ),
                                          Showcase(
                                            key: _syncButtonKey,
                                            title: 'Sync',
                                            description:
                                                'Press the sync button to manually sync the device with the app.',
                                            descTextStyle: GoogleFonts.roboto(
                                              color: AppTheme.primaryColor,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                            ),
                                            tooltipBorderRadius:
                                                BorderRadius.circular(20),
                                            tooltipBackgroundColor:
                                                Color(0xffFAF2DF),
                                            tooltipPadding: EdgeInsets.all(20),
                                            titleTextStyle: GoogleFonts.roboto(
                                              color: AppTheme.primaryColor,
                                              fontSize: 20,
                                              fontWeight: FontWeight.w700,
                                            ),
                                            targetBorderRadius:
                                                BorderRadius.only(
                                              bottomRight: Radius.circular(26),
                                            ),
                                            child: Container(
                                              height: .125.sh,
                                              width: .4.sw - 13,
                                              decoration: BoxDecoration(
                                                  color: Color(0xffFAF2DF),
                                                  borderRadius:
                                                      BorderRadius.only(
                                                    bottomRight:
                                                        Radius.circular(26),
                                                  )),
                                              child: Center(
                                                  child: SyncButtonContainer()),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            ],
                          ),
                        )),
                  ),

                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   crossAxisAlignment: CrossAxisAlignment.center,
                  //   children: [
                  //     Text('Use Recommended Settings',
                  //         style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  //             color: Color(0xff26204A),
                  //             fontWeight: FontWeight.w400,
                  //             fontSize: 16)),
                  //     Switch(materialTapTargetSize: MaterialTapTargetSize.padded,
                  //       value: switchValue, onChanged: (value) {
                  //
                  //         switchValue = value; setState(() {});},
                  //       thumbIcon:  WidgetStateProperty.resolveWith ((Set  states) {
                  //         if (states.contains(WidgetState.selected)) {
                  //           return const Icon(Icons.check, color: Color(0xff6750A4));
                  //         }
                  //         return null; // All other states will use the default thumbIcon.
                  //       }
                  //       ),
                  //
                  //       thumbColor: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
                  //         if (states.contains(WidgetState.selected)) {
                  //           return Colors.white;
                  //         }
                  //         return Colors.white; // Use the compTwont's default.
                  //       }),
                  //       activeColor: Color(0xff6750A4),
                  //       activeTrackColor:Color(0xff6750A4) ,
                  //
                  //       trackOutlineWidth:WidgetStateProperty.resolveWith((Set<WidgetState> states) {
                  //         if (states.contains(WidgetState.selected)) {
                  //           return 10;
                  //         }
                  //         return 10; // Use the compTwont's default.
                  //       }) ,
                  //     ),
                  //   ],
                  // ),
                ],
              ),
            ),
          ),
        ));
  }

  final devices = <BluetoothDevice>{};

  int actualTensLevel = 0;
  int selectedTensLevel = 0;
  int actualHeatLevel = 0;

  void _startScan() async {
    late List<ScanResult> scanResult;
    final subscription = FlutterBluePlus.scanResults.listen((results) {
      scanResult = results;
    });
    await FlutterBluePlus.startScan(
        withKeywords: ['Lily'], timeout: const Duration(seconds: 20));
    FlutterBluePlus.cancelWhenScanComplete(subscription);

    print("System Devices");
    print(await FlutterBluePlus.systemDevices);
    print(await FlutterBluePlus.connectedDevices);
    print("Bonded Devices");

    await FlutterBluePlus.isScanning.where((val) => val == false).first;
    for (ScanResult result in scanResult) {
      print(result.device);
      if (result.device.platformName.isEmpty) {
        continue;
      }
      devices.add(result.device);
    }
  }

  _connectToDevice() async {
    BluetoothDevice device = devices.first;
    print('device: $device');
    try {
      if (device.isConnected) {
        print('device already connected');
        List<BluetoothService> services = await device.discoverServices();
        services.forEach((service) {
          print('service: ${service.uuid}');
          service.characteristics.forEach((characteristic) {
            print('characteristic: ${characteristic.uuid}');
          });
        });
      } else {
        // await device.requestMtu(185);
        await device.connect(autoConnect: false,timeout: const Duration(seconds: 60));
        await device.connectionState
            .where((val) => val == BluetoothConnectionState.connected)
            .first;
        print('device connected');
        // print(device.servicesList);
      }
    } catch (e) {
      print('error: $e');
    }
  }

  void _connectionCheck() async {
    try {
      // print if the device is connected
      print('checking connection');
      print('is connected: ${await FlutterBluePlus.adapterState.first}');

      final connectedDevices = Platform.isIOS
          ? await FlutterBluePlus.systemDevices
          : await FlutterBluePlus.bondedDevices;

      if (connectedDevices.isNotEmpty) {
        BluetoothDevice? device = connectedDevices.firstWhere(
          (element) => element.platformName.toLowerCase().contains('Lily'),
          orElse: () => BluetoothDevice(remoteId: const DeviceIdentifier('')),
        );

        if (device.remoteId.str != 'Lily') {
          if (device.isConnected) {
            print('service uuids: ${device.servicesList}');
          } else {
            try {
              await device.connect();
              await device.connectionState
                  .where((val) => val == BluetoothConnectionState.connected)
                  .first;
              // print('service uuids: ${device.servicesList}');
            } catch (e) {
              print('error: $e');
            }
          }
        } else {
          print('device not found');
        }
      } else {
        print('no devices connected');
      }
    } catch (e) {
      print('error: $e');
    }
  }

  //polling the device for the tens level
  void _pollTensLevel() async {
    final device = devices.first;
    final services = await device.discoverServices();
    services.forEach((service) {
      print('service: ${service.uuid}');
      if (service.uuid.toString() ==
          Guid('23289aa0-670c-4635-8b38-e1ab58c0e9c4').str) {
        print('service found');
        service.characteristics.forEach((characteristic) async {
          print('characteristic: ${characteristic.uuid}');
          if (characteristic.uuid ==
              Guid('23289aa3-670c-44f3-8b38-e1ab58c0e9c4')) {
            //
            // Timer.periodic(Duration(milliseconds: 500), (timer) async {
            //  await characteristic.write([0]);
             List<int> value= await characteristic.read();
            actualTensLevel=value[0];
              setState(() {});
            // });
          }
        });
      }
      //========================
      if (service.uuid.toString() ==
          Guid('d0b12770-6b7d-4635-ab5a-e3abdd621537').str) {
        print('service found');
        service.characteristics.forEach((characteristic) async {
          print('characteristic: ${characteristic.uuid}');
          if (characteristic.uuid ==
              Guid('d0b12771-6b7d-4635-ab5a-e3abdd621537')) {
            //
            // Timer.periodic(Duration(milliseconds: 500), (timer) async {
            //  await characteristic.write([0]);
            List<int> value= await characteristic.read();
            print('==============================================${value}');
            // });
          }
        });
      }
    });
  }
}

class SyncButtonContainer extends StatefulWidget {
  @override
  _SyncButtonContainerState createState() => _SyncButtonContainerState();
}

class _SyncButtonContainerState extends State<SyncButtonContainer> {
  bool _isSyncing = false;

  void _startSync() {
    setState(() {
      _isSyncing = true;
    });

    // Revert back to the button after 4 seconds
    Timer(Duration(seconds: 4), () {
      setState(() {
        _isSyncing = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 0.125.sh,
      width: 0.4.sw - 13,
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(26),
        ),
      ),
      child: Center(
        child: AnimatedSwitcher(
          duration: Duration(milliseconds: 600),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(opacity: animation, child: child);
          },
          child: _isSyncing
              ? Text(
                  'Syncing...',
                  key: ValueKey<int>(1),
                  style: TextStyle(fontSize: 24, color: Colors.black),
                )
              : Container(
                  key: ValueKey<int>(2),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(50),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.sync,
                      size: 50,
                    ),
                    onPressed: _startSync,
                  ),
                ),
        ),
      ),
    );
  }
}
