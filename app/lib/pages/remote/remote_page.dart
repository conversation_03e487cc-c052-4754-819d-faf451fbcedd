import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../custom_widgets/battery_indicator.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../custom_widgets/slider.dart';
@RoutePage()
class RemotePage extends StatefulWidget {
  const RemotePage({super.key});

  @override
  State<RemotePage> createState() => _RemotePageState();
}

class _RemotePageState extends State<RemotePage> {
  double value = 1;
  double battery_level = 0.5;
  double value2 = 0;
  List<int> modes = [1, 2, 3];
  int _selectedMode = 1;
  bool switchValue=true;
  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        height: .35.sw, // The height of your curved app bar
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              child: Row(
                children: [
                  Image.asset('assets/remote/device_pair.png'
                    ,width: 250,
                    height: 150,
                  ),


        Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: BatteryIndicator(value: battery_level,trackHeight: 20,trackBorderRadius: BorderRadius.circular(5),barBorderRadius: BorderRadius.circular(5),),
          ),
          Text('${(battery_level * 100).ceil()}%',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: Color(0xff26204A),
                  fontWeight: FontWeight.w400,
                  fontSize: 16)),
        ],
      )
                ],
              ),
            ),

            Container(
                width: .8.sw,
                height: .8.sw,
                decoration: BoxDecoration(
                    color: Colors.white, borderRadius: BorderRadius.circular(20)),
                child: Row(
                  children: [
                    Container(
                      width: .5.sw,
                      decoration: BoxDecoration(
                          color: Color(0xffFAF2DF).withOpacity(.4),
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(32),
                              bottomLeft: Radius.circular(32))),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 30,
                          ),
                          SvgPicture.asset(
                            'assets/remote/remote_charge.svg',
                            width: 18,
                            height: 18,
                          ),
                          Text(
                            'Electric',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                    color: Color(0xff26204A),
                                    fontWeight: FontWeight.w400,
                                    fontSize: 20),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            'Modes',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                    color: Color(0xff26204A),
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16),
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  _selectedMode = 1;
                                  setState(() {});
                                },
                                child: Container(
                                  height: 40,
                                  width: 40,
                                  decoration: BoxDecoration(
                                      color: _selectedMode==1?Color(0xffF7A600):Color(0xffDDDAF5),
                                      borderRadius: BorderRadius.circular(15)),
                                  child: Center(
                                      child: Text(
                                    '1',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            color: _selectedMode==1?Colors.white:   Color(0xff26204A),
                                            fontWeight: FontWeight.w400,
                                            fontSize: 16),
                                  )),
                                ),
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              GestureDetector(
                                onTap: () {
                                  _selectedMode = 2;
                                  setState(() {});
                                },
                                child: Container(
                                  height: 40,
                                  width: 40,
                                  decoration: BoxDecoration(
                                      color:  _selectedMode==2?Color(0xffF7A600):Color(0xffDDDAF5),
                                      borderRadius: BorderRadius.circular(15)),
                                  child: Center(
                                      child: Text(
                                    '2',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            color: _selectedMode==2? Colors.white:Color(0xff26204A),
                                            fontWeight: FontWeight.w400,
                                            fontSize: 16),
                                  )),
                                ),
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              GestureDetector(
                                onTap: () {
                                  _selectedMode = 3;
                                  setState(() {});
                                },
                                child: Container(
                                  height: 40,
                                  width: 40,
                                  decoration: BoxDecoration(
                                      color: _selectedMode==3?Color(0xffF7A600):Color(0xffDDDAF5),
                                      borderRadius: BorderRadius.circular(15)),
                                  child: Center(
                                      child: Text(
                                    '3',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            color: _selectedMode==3? Colors.white:Color(0xff26204A),
                                            fontWeight: FontWeight.w400,
                                            fontSize: 16),
                                  )),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 25,
                          ),
                          Text(
                            'Intensity',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                    color: Color(0xff26204A),
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                width: .3.sw,
                                child: SliderTheme(
                                    data: SliderTheme.of(context).copyWith(
                                      trackHeight: 15,
                                      activeTrackColor:
                                          Color(0xff6750A4).withOpacity(0.75),
                                      thumbColor: Color(
                                          0xff6750A4), // Set the thickness of the slider track
                                      trackShape: CustomSliderTrackShape(),
                                      overlayShape:
                                          CustomSliderOverlayShape(), // Set the size of the slider thumb
                                    ),
                                    child: Slider(
                                      value: value2,
                                      onChanged: (value) {
                                        value2 = value;
                                        setState(() {});
                                      },
                                      min: 0,
                                      max: 10,
                                      inactiveColor: Color(0xffE1E1E1),
                                    )),
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Text(
                                value2.toInt().toString(),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                        color: Color(0xff26204A),
                                        fontWeight: FontWeight.w400,
                                        fontSize: 16),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: .3.sw,
                      decoration: BoxDecoration(
                          color: Color(0xffFAF2DF),
                          borderRadius: BorderRadius.only(
                              topRight: Radius.circular(32),
                              bottomRight: Radius.circular(32))),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 30,
                          ),
                          SvgPicture.asset(
                            'assets/remote/remote_heat.svg',
                            width: 18,
                            height: 18,
                          ),
                          Text(
                            'Heat',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                    color: Color(0xff26204A),
                                    fontWeight: FontWeight.w400,
                                    fontSize: 20),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            value.toInt().toString(),
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                    color: Color(0xff26204A),
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                            height: .3
                                .sw, // Ensure this is adequately set for your needs.
                            child: SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                trackHeight: 12,
                                trackShape: CustomSliderTrackShape(),
                                overlayShape: CustomSliderOverlayShape(),
                                showValueIndicator: ShowValueIndicator.always,
                                valueIndicatorColor: Colors.black,
                                valueIndicatorTextStyle:
                                    TextStyle(color: Colors.black),
                                activeTrackColor:
                                    Color(0xff6750A4).withOpacity(0.75),
                                thumbColor: Color(0xff6750A4),
                              ),
                              child: RotatedBox(
                                quarterTurns: 3,
                                child: Slider(
                                  value: value,
                                  onChanged: (newValue) {
                                    setState(() {
                                      value = newValue;
                                    });
                                  },
                                  min: 0,
                                  max: 6,
                                  inactiveColor: Color(0xffE1E1E1),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 25,
                          ),
                        ],
                      ),
                    )
                  ],
                )),
            SizedBox(
              height: 20,
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text('Use Recommended Settings',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: Color(0xff26204A),
                        fontWeight: FontWeight.w400,
                        fontSize: 16)),
                Switch(materialTapTargetSize: MaterialTapTargetSize.padded,
                  value: switchValue, onChanged: (value) {

                  switchValue = value; setState(() {});},
                  thumbIcon:  WidgetStateProperty.resolveWith ((Set  states) {
                  if (states.contains(WidgetState.selected)) {
                    return const Icon(Icons.check, color: Color(0xff6750A4));
                  }
                  return null; // All other states will use the default thumbIcon.
                }
                ),

                  thumbColor: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return Colors.white;
                    }
                    return Colors.white; // Use the component's default.
                  }),
                activeColor: Color(0xff6750A4),
                  activeTrackColor:Color(0xff6750A4) ,

                  trackOutlineWidth:WidgetStateProperty.resolveWith((Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return 10;
                    }
                    return 10; // Use the component's default.
                  }) ,
                ),
              ],
            ),

          ],
        ),
      ),
    );
  }
}
