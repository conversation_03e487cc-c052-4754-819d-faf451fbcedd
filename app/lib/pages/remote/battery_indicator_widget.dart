import 'package:remote/application/device_status_watcher_bloc/device_status_watcher_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../custom_widgets/battery_indicator.dart';
class BatteryIndicatorWidget extends StatelessWidget {
  const BatteryIndicatorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DeviceStatusWatcherBloc, DeviceStatusWatcherState>(
      builder: (context, state) {
        print(state.deviceInformation.toJson());
        return BatteryIndicator(
          trackAspectRatio: 2.0,
          trackHeight: 15,
           value: state.deviceInformation.batteryLevel!.batteryLevel.toDouble()/100,
          icon: Text(
            '${state.deviceInformation.batteryLevel?.batteryLevel}%',
            style: TextStyle(
              color: Colors.white,
              fontSize: 8,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
      },
    );
  }
}
