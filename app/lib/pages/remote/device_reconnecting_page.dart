import 'package:flutter/material.dart';
import 'package:rive/rive.dart';

/// The DeviceReconnectingPage class is a StatelessWidget that displays the page when the device is reconnecting.
class DeviceReconnectingPage extends StatelessWidget {
  const DeviceReconnectingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          height: 100,
          width: 200,
          child: RiveAnimation.asset(
            'assets/rive/loading_dots.riv',
            fit: BoxFit.cover,


          ),
        ),
      ),
    );
  }
}
