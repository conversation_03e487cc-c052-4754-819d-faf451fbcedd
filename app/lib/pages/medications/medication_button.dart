
import 'package:account_management/application/medication_watcher_bloc/medication_watcher_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../helpers.dart';
import '../../routing/app_pages.gr.dart';

class MedicationButton extends StatelessWidget {
  const MedicationButton({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(create: (_) => getIt<MedicationWatcherBloc>()
      ..add(MedicationWatcherEvent.watchAllStarted()),
      child: BlocBuilder<MedicationWatcherBloc, MedicationWatcherState>(
        builder: (context, state) {
          if (state is Success){
            return GestureDetector(
              key: Key('medication_button'),
              onTap: (){
                context.router.push(MedicationRoute());
              },
              child: Container(
                width: .38.sw,
                height: .4.sw,
                decoration: BoxDecoration(
                    color: Color(0xffFAF2DF),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x40000000), // #******** in CSS corresponds to 0x40000000 in Flutter
                        blurRadius: 4.0, // the blur radius
                        offset: Offset(0, 1), // the x,y offset of the shadow
                      ),
                    ],
                    borderRadius: BorderRadius.all(Radius.circular(32))),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text('Medication',style: Theme.of(context).textTheme.bodyMedium!.copyWith(color:Color(0xff30285D), ),),
                    SizedBox(height: 10,),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CircleAvatar(backgroundColor: Colors.white, radius: 30,

                            child: SvgPicture.asset('assets/home/<USER>',height: 30,width: 30,)),
                        Text('${state.medications.length} Active Medications',style: Theme.of(context).textTheme.bodyMedium!.copyWith(color:Color(0xff30285D), ),textAlign: TextAlign.center,),
                      ],
                    ),
                  ],
                ),
              ),
            );
          } else {
            return GestureDetector(
              onTap: (){
                context.router.push(MedicationRoute());
              },
              child: Container(
                width: .38.sw,
                height: .39.sw,
                decoration: BoxDecoration(
                    color: Color(0xffFAF2DF),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x40000000), // #******** in CSS corresponds to 0x40000000 in Flutter
                        blurRadius: 4.0, // the blur radius
                        offset: Offset(0, 1), // the x,y offset of the shadow
                      ),
                    ],
                    borderRadius: BorderRadius.all(Radius.circular(32))),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text('Medication',style: Theme.of(context).textTheme.bodyMedium!.copyWith(color:Color(0xff30285D), ),),
                    SizedBox(height: 10,),
                    Container(
                      width: .28.sw,
                      height: .25.sw,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(10))),
                      child:Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset('assets/home/<USER>',height: 30,width: 30,),
                          SizedBox(height: 10,),
                          Text('0 Active Medications',style: Theme.of(context).textTheme.bodyMedium!.copyWith(color:Color(0xff30285D), ),textAlign: TextAlign.center,),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        }
        )
    );
  }
}
