import 'package:account_management/account_management.dart';
import 'package:account_management/application/medication_form_bloc/medication_form_bloc.dart';
import 'package:account_management/domain/model/medication_model.dart';
import 'package:account_management/domain/value_objects/dosage_unit.dart';
import 'package:authentication/widgets/text_form_feild.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fpdart/fpdart.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/medications/medication.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:notifications/application/manage_scheduled_notifications_bloc/manage_scheduled_notifications_bloc.dart';
import '../../helpers.dart';

class MedicationFormPage extends StatelessWidget {
  final MedicationModel? medication;
  const MedicationFormPage({Key? key, this.medication}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<MedicationFormBloc>(
          create: (context) => getIt<MedicationFormBloc>()
            ..add(MedicationFormEvent.initialized(Option.fromNullable(medication))),
        ),
        BlocProvider<ManageScheduledNotificationsBloc>(
          create: (context) => getIt<ManageScheduledNotificationsBloc>(),
        ),
      ],

      child: BlocConsumer<MedicationFormBloc, MedicationFormState>(
        listener: (context, state) {
          state.saveFailureOrSuccessOption.getOrNull()?.mapBoth(
            onLeft:  (failure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Failed to save medication',
                  ),
                ),
              );
            },
            onRight:  (_) {
              Navigator.of(context).pop();
            },
          );
        },
        buildWhen: (previous, current) => previous.isEditing != current.isEditing,  builder: (context, state) {

    return MedicationFormPageScaffold(medication: medication);
  },
),
    );
  }
}

class MedicationFormPageScaffold extends StatelessWidget {
  final MedicationModel? medication;
  MedicationFormPageScaffold({Key? key, this.medication}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    return BlocBuilder<MedicationFormBloc, MedicationFormState>(
      buildWhen: (previous, current) => previous.medication != current.medication,
      builder: (context, state) {
        final formBloc = context.read<MedicationFormBloc>();


        return Scaffold(
          bottomNavigationBar: Container(
            padding: const EdgeInsets.fromLTRB(20, 10, 20, 10),
            child: ElevatedButton(
              key: const Key('submit_medication_button'),
              onPressed: () {
                formBloc.add(MedicationFormEvent.save());
                if(state.medication.isNotificationEnabled == true && state.medication.frequencyUnit=='weekly'){
                  context.read<ManageScheduledNotificationsBloc>().add(
                    ManageScheduledNotificationsEvent.scheduleNotificationsWeekly(
                      daystoBeNotified: state.medication.daystoBeNotified!,
                      timeofDay: state.medication.timeofDay!,
                      body: 'Time to take your medication',
                      notificationGroupId: state.medication.id!,
                    ),
                  );
                }else if(state.medication.isNotificationEnabled == true && state.medication.frequencyUnit!='monthly'){
                  context.read<ManageScheduledNotificationsBloc>().add(
                    ManageScheduledNotificationsEvent.scheduleNotificationsDaily(
                      timeofDay: state.medication.timeofDay!,
                      body: 'Time to take your medication',
                      notificationGroupId: state.medication.id!,
                    ),
                  );
                }else if(state.medication.isNotificationEnabled == true && state.medication.frequencyUnit=='monthly'){
                  context.read<ManageScheduledNotificationsBloc>().add(
                    ManageScheduledNotificationsEvent.scheduleNotificationsMonthly(
                      dateTime: tz.TZDateTime.from(state.medication.monthlyDateToBeNotified!, tz.local),
                      body: 'Time to take your medication',
                      notificationGroupId: state.medication.id!,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                minimumSize: const Size(200, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              child: Text(
                medication == null ? 'Add Medication' : 'Update Medication',
                style: const TextStyle(fontSize: 16.0, color: Colors.white),
              ),
            ),
          ),
          appBar: AppBar(
            leading: IconButton(
              icon: Icon(Icons.close_rounded),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            centerTitle: true,
            backgroundColor: Colors.white,
            title: const Text(
              'Add Medication',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20.0),
            ),
          ),
          body: SingleChildScrollView(
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xffFAF2DF),
                borderRadius: BorderRadius.circular(20),
              ),
              height: 1.sh,
              child: Form(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      SizedBox(height: 10),
                      MyTextFormField(
                        refkey: Key('medication_name_field'),
                        heading: "Medication Name",
                        initialvalue: medication?.name,
                        onchanged: (value) =>
                            formBloc.add(MedicationFormEvent.nameChanged(value!)),
                        validator: (value) => value == null || value.isEmpty
                            ? 'Missing Information'
                            : null,
                      ),
                      SizedBox(height: 10),
                      const SizedBox(
                        height: 27,
                        width: 300,
                        child: Text('Dosage & Frequency',
                            style: TextStyle(color: Colors.black)),
                      ),
                      Column(
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Dosage Container
                              Expanded( // Use Expanded to allow it to take up available space
                                child: Container(
                                  padding: EdgeInsets.symmetric(horizontal: 8),
                                  decoration: BoxDecoration(
                                    color: Color.fromRGBO(221, 218, 245, 1.0),
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Color(0x40000000),
                                        blurRadius: 4.0,
                                        offset: Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.medication),
                                      SizedBox(width: 3),
                                      // Dosage TextField
                                      SizedBox(
                                        height: 40,
                                        width: 40,
                                        child: TextFormField(
                                          initialValue: medication?.dosage,
                                          key: Key('dosage_field'),
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            counterText: '',
                                            contentPadding: EdgeInsets.only(bottom: 5.0),
                                            hintText: '10',
                                            hintStyle: TextStyle(fontSize: 16.0, color: Colors.grey),
                                          ),
                                          maxLength: 3,
                                          keyboardType: TextInputType.number,
                                          onChanged: (value) => formBloc.add(
                                            MedicationFormEvent.dosageChanged(value),
                                          ),
                                        ),
                                      ),
                                      // Dosage Unit Dropdown
                                      SizedBox(
                                        width: 60, // Set fixed width for the dropdown
                                        height: 40,
                                        child: DropdownButtonFormField<DosageUnit>(
                                          key: Key('dosage_dropdown'),
                                          value:DosageUnit(state.medication.dosageUnit!),
                                          padding: EdgeInsets.zero,
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.only(bottom: 5.0),
                                          ),
                                          items: DosageUnit.availableWeightUnits.map((DosageUnit unit) {
                                            return DropdownMenuItem<DosageUnit>(
                                              value: unit,
                                              child: Text(unit.value),
                                            );
                                          }).toList(),
                                          onChanged: (newValue) {
                                            formBloc.add(
                                              MedicationFormEvent.dosageUnitChanged(newValue!.value),
                                            );
                                          },
                                          validator: (value) => value == null ? 'Please select a unit' : null,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(width: 15),
                              // Frequency Container
                              Expanded(
                                child: Container(
                                  padding: EdgeInsets.symmetric(horizontal: 8),
                                  decoration: BoxDecoration(
                                    color: Color.fromRGBO(221, 218, 245, 1.0),
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Color(0x40000000),
                                        blurRadius: 4.0,
                                        offset: Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.timer),
                                      SizedBox(width: 3),
                                      // Frequency TextField
                                      SizedBox(
                                        height: 40,
                                        width: 40,
                                        child: TextFormField(
                                          initialValue: medication?.frequency,
                                          key: Key('frequency_field'),
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            counterText: '',
                                            contentPadding: EdgeInsets.only(bottom: 5.0),
                                            hintText: '3',
                                            hintStyle: TextStyle(fontSize: 16.0, color: Colors.grey),
                                          ),
                                          maxLength: 2,
                                          keyboardType: TextInputType.number,
                                          onChanged: (value) => formBloc.add(
                                            MedicationFormEvent.frequencyChanged(value),
                                          ),
                                        ),
                                      ),
                                      // Frequency Unit Dropdown
                                      SizedBox(
                                        width: 90,
                                        height: 40,
                                        child: DropdownButtonFormField<FrequencyUnit>(
                                          padding: EdgeInsets.zero,
                                          key: Key('frequency_dropdown'),
                                          value: FrequencyUnit(state.medication.frequencyUnit!),
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.only(bottom: 5.0),
                                          ),
                                          items: FrequencyUnit.availableFrequencyUnits.map((FrequencyUnit unit) {
                                            return DropdownMenuItem<FrequencyUnit>(
                                              value: unit,
                                              child: Text(unit.value),
                                            );
                                          }).toList(),
                                          onChanged: (newValue) {
                                            formBloc.add(
                                              MedicationFormEvent.frequencyUnitChanged(newValue!.value),
                                            );
                                          },
                                          validator: (value) => value == null ? 'Please select a unit' : null,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          // Notes
                          Padding(
                            padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
                            child: MyTextFormField(
                              heading: "Notes",
                              refkey: Key('notes_field'),
                              initialvalue: medication?.notes,
                              onchanged: (value) => formBloc.add(
                                MedicationFormEvent.notesChanged(value!),
                              ),
                              validator: (value) => value == null || value.isEmpty ? 'Missing Information' : null,
                            ),
                          ),
                          // Notifications Switch
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text('Notifications', style: TextStyle(color: Colors.black)),
                              Switch(
                                value: state.medication.isNotificationEnabled ?? false,
                                key: Key('notification_switch'),
                                onChanged: (value) {
                                  formBloc.add(MedicationFormEvent.isNotificationEnabledChanged(value));
                                },
                                activeColor: Theme.of(context).primaryColor,
                              ),
                            ],
                          ),
                          // Notification Settings
                          if (state.medication.isNotificationEnabled == true && state.medication.frequencyUnit=='weekly') ...[
                            // Days of the Week
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
                                    .map((day) => _buildDayCircle(day, formBloc, context))
                                    .toList(),
                              ),
                            ),],
                          if (state.medication.isNotificationEnabled == true && state.medication.frequencyUnit!='monthly') ...[
                            // Notification Times
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Column(
                                children: state.medication.timeofDay!
                                    .asMap()
                                    .entries
                                    .map((entry) => _buildNotificationTimeRow(context, entry.key, formBloc))
                                    .toList(),
                              ),
                            ),
                            if (state.medication.timeofDay!.length < 3 && state.medication.daystoBeNotified!.isNotEmpty)
                              ElevatedButton(
                                onPressed: () async {
                                  TimeOfDay? pickedTime = await showTimePicker(
                                    context: context,
                                    initialTime: TimeOfDay.now(),
                                  );
                                  if (pickedTime != null) {
                                    final updatedTimes = List<String>.from(state.medication.timeofDay!);
                                    updatedTimes.add(pickedTime.format(context));
                                    formBloc.add(MedicationFormEvent.timeofDayChanged(updatedTimes));
                                  }
                                },
                                child: Text('Add Notification Time'),
                              ),
                          ],

                          // Notification Date for Monthly date time picker
                          if (state.medication.isNotificationEnabled == true && state.medication.frequencyUnit=='monthly') ...[
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 5.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Color.fromRGBO(221, 218, 245, 1.0),
                                  borderRadius: BorderRadius.circular(30),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      blurRadius: 4.0,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 3.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Day: ${DateFormat('dd').format(state.medication.monthlyDateToBeNotified??DateTime.now())}, Time: ${DateFormat('HH:MM a').format(state.medication.monthlyDateToBeNotified??DateTime.now())}",
                                          style: GoogleFonts.roboto(
                                            fontSize: 16,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w500,
                                          )),
                                      IconButton(
                                        icon: Icon(Icons.calendar_today),
                                        onPressed: () async {
                                          final DateTime? pickedDate = await showDatePicker(
                                            context: context,
                                            initialDate: state.medication.monthlyDateToBeNotified??DateTime.now(),
                                            firstDate: DateTime.now(),
                                            lastDate: DateTime.now().add(Duration(days: 60)),
                                          );
                                          final TimeOfDay? pickedTime = await showTimePicker(
                                            context: context,
                                            initialTime: TimeOfDay.now(),
                                          );


                                          if (pickedDate != null) {
                                            final updatedDate = DateTime(
                                              pickedDate.year,
                                              pickedDate.month,
                                              pickedDate.day,
                                              pickedTime?.hour??DateTime.now().hour,
                                              pickedTime?.minute??DateTime.now().minute,
                                            );
                                            formBloc.add(MedicationFormEvent.monthlyDateToBeNotifiedChanged(updatedDate));
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ],
                      )
    
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDayCircle(String day, MedicationFormBloc formBloc,BuildContext context) {
    final bool isSelected = formBloc.state.medication.daystoBeNotified!.contains(day);
    final List<String> updatedDays = List<String>.from(formBloc.state.medication.daystoBeNotified ?? []);
    return GestureDetector(
      onTap: () {
        if (isSelected) {
          updatedDays.remove(day);
          formBloc.add(MedicationFormEvent.daystoBeNotifiedChanged(
            updatedDays,
          ));
        } else {
          updatedDays.add(day);
          formBloc.add(MedicationFormEvent.daystoBeNotifiedChanged(
            updatedDays,
          ));
        }
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
        ),
        alignment: Alignment.center,
        child: Text(
          day,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationTimeRow(BuildContext context, int index, MedicationFormBloc formBloc) {
    final time = formBloc.state.medication.timeofDay![index];
    final updatedTimes = List<String>.from(formBloc.state.medication.timeofDay!);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 5.0),
      child: Container(
        decoration: BoxDecoration(
          color: Color.fromRGBO(221, 218, 245, 1.0),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Color(0x40000000),
              blurRadius: 4.0,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 3.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(time,
                  style: GoogleFonts.roboto(
                    fontSize: 16,
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                  )),

              IconButton(
                icon: Icon(Icons.close_outlined),
                onPressed: () {
                  updatedTimes.removeAt(index);
                  formBloc.add(MedicationFormEvent.timeofDayChanged(updatedTimes));
                },
              ),
            ],
          ),
        ),
      ),
    );
  }


}
