// lib/pages/medications/medication_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:design_system/design/theme.dart';
import 'package:auto_route/annotations.dart';
import 'package:account_management/application/manage_medications_bloc/manage_medications_bloc.dart';
import 'package:account_management/application/medication_watcher_bloc/medication_watcher_bloc.dart';
import 'package:account_management/di/di.dart';
import 'package:account_management/domain/model/medication_model.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/medications/medication_form.dart';
import 'package:uuid/uuid.dart';
import 'package:bluetooth/bluetooth.dart';
import '../../custom_widgets/curved_app_bar.dart';
import 'delete_medication_dialog.dart';
import 'logged_time_widget.dart'; // Import the new widget

@RoutePage()
class MedicationPage extends StatelessWidget {
  const MedicationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) => getIt<MedicationWatcherBloc>()
              ..add(const MedicationWatcherEvent.watchAllStarted())),
      ],
      child: MedicationViewPage(),
    );
  }
}

class MedicationViewPage extends StatefulWidget {
  const MedicationViewPage({super.key});

  @override
  State<MedicationViewPage> createState() => _MedicationViewPageState();
}

class _MedicationViewPageState extends State<MedicationViewPage> {
  void _showDeleteDialog(BuildContext context, MedicationModel medication) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BlocProvider(
          create: (context) => getIt<ManageMedicationsBloc>(),
          child: DeleteMedicationDialog(
            medication: medication,
          ),
        );
      },
    );
  }

  void _editMedication(BuildContext context, MedicationModel medication) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (buildContext) => Container(
            height: .85.sh,
            decoration: BoxDecoration(
              color: Color(0xffFAF2DF),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                topRight: Radius.circular(32),
              ),
            ),
            clipBehavior: Clip.hardEdge,
            child: MedicationFormPage(medication: medication)));
  }

  Widget _buildMedicationItem(MedicationModel medication) {
    TimeOfDay selectedTime = TimeOfDay.now();
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xffFAF2DF),
        borderRadius: BorderRadius.circular(32),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
      ),
      constraints: BoxConstraints(minHeight: 0.44.sw, minWidth: 0.75.sw),
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 0.20.sw,
                height: 0.20.sw,
                margin: EdgeInsets.only(left: 20, top: 20, right: 0),
                decoration: BoxDecoration(
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x40000000),
                      blurRadius: 4.0,
                      offset: Offset(0, 4),
                    ),
                  ],
                  borderRadius: BorderRadius.circular(42),
                  color: Color(0xffDDDAF5),
                  border: Border.all(color: Color(0xffB2A8FE), width: 2),
                ),
                child: Padding(
                  padding: EdgeInsets.only(top: 4),
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                        filterQuality: FilterQuality.high,
                        scale: 0.75,
                        opacity: 0.80,
                        image: AssetImage('assets/home/<USER>'),
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 8),
                alignment: Alignment.topLeft,
                constraints: BoxConstraints(minWidth: 185),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: 20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${medication.name!}',
                            style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: Color(0xff30285D)),
                          ),
                          SizedBox(height: 3),
                          Text(
                            '${medication.dosage!} ${medication.dosageUnit}, ${medication.frequency!} ${medication.frequencyUnit}',
                            style: TextStyle(
                                color: Color(0xff30285D).withOpacity(0.6)),
                          ),
                          SizedBox(height: 3),
                          if (medication.notes != null)
                            Container(
                              width: 0.4.sw,
                              child: Text('${medication.notes!}  ',
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  style: TextStyle(color: Color(0xff30285D))),
                            ),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 5, left: 20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: medication.loggedTimes!
                            .where((time) => time.isSameDate(DateTime.now()))
                            .map((time) {
                          return LoggedTimeWidget(
                            time: time,
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return AlertDialog(
                                    title: Text(
                                        "Are you sure you want to delete this logged time?"),
                                    actions: [
                                      TextButton(
                                        child: Text("Cancel"),
                                        onPressed: () {
                                          Navigator.of(context)
                                              .pop(); // Close the dialog
                                        },
                                      ),
                                      TextButton(
                                        child: Text("Delete"),
                                        onPressed: () {
                                          setState(() {
                                            medication.loggedTimes?.remove(
                                                time); // Delete the time
                                          });
                                          Navigator.of(context)
                                              .pop(); // Close the dialog
                                        },
                                      ),
                                    ],
                                  );
                                },
                              );
                            },
                          );
                        }).toList(),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    )
                  ],
                ),
              ),
              SizedBox(width: 10),
              Wrap(
                children: [
                  PopupMenuButton<String>(
                    onSelected: (String result) {
                      if (result == 'Edit') {
                        _editMedication(context, medication);
                      } else if (result == 'Delete') {
                        _showDeleteDialog(context, medication);
                      }
                    },
                    padding: EdgeInsets.zero,
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'Edit',
                        child: Text('Edit'),
                      ),
                      const PopupMenuItem(
                        value: 'Delete',
                        child: Text('Delete'),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                style: ButtonStyle(
                  textStyle: WidgetStateProperty.all(
                      TextStyle(fontWeight: FontWeight.w400)),
                  minimumSize: WidgetStateProperty.all(Size(155, 18)),
                  shape: WidgetStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  foregroundColor:
                      WidgetStateProperty.all(const Color(0xff4D2772)),
                  backgroundColor: WidgetStateProperty.all(Colors.white),
                ),
                onPressed: () async {
                  // Show a snackbar to confirm that the medication has been skipped
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          'Medication skipped at ${DateFormat('jm').format(DateTime.now())}'),
                      action: SnackBarAction(
                        label: 'Undo',
                        onPressed: () {
                          setState(() {
                            medication.loggedTimes!.removeLast();
                          });
                        },
                      ),
                    ));

                },
                child: Row(children: [Icon(Icons.close), Text('Skip')]),
              ),
              SizedBox(width: 5),
              TextButton(
                style: ButtonStyle(
                  textStyle: WidgetStateProperty.all(
                      TextStyle(fontWeight: FontWeight.w400)),
                  minimumSize: WidgetStateProperty.all(Size(155, 18)),
                  shape: WidgetStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  foregroundColor:
                      WidgetStateProperty.all(const Color(0xff4D2772)),
                  backgroundColor: WidgetStateProperty.all(Colors.white),
                ),
                onPressed: () async {
                  // Filter logged times to get only today's logs
                  final todayLogs = medication.loggedTimes!
                      .where((time) => time.isSameDate(DateTime.now()))
                      .toList();

                  // Determine the maximum allowed logs based on frequency and frequency unit
                  int maxLogs = 0;
                  if (medication.frequencyUnit == 'daily') {
                    maxLogs = int.parse(medication.frequency ?? '0');
                  } else if (medication.frequencyUnit == 'weekly') {
                    maxLogs =
                        (int.parse(medication.frequency ?? '0') / 7).ceil();
                  } else if (medication.frequencyUnit == 'biweekly') {
                    maxLogs =
                        (int.parse(medication.frequency ?? '0') / 14).ceil();
                  } else if (medication.frequencyUnit == 'monthly') {
                    maxLogs =
                        (int.parse(medication.frequency ?? '0') / 30).ceil();
                  }

                  // Check if the number of logs for today is less than the allowed frequency
                  if (todayLogs.length < maxLogs) {
                    String periodofDay = 'PM';
                    String minutes;

                    final TimeOfDay? timeofDay = await showTimePicker(
                      context: context,
                      initialTime: selectedTime,
                      initialEntryMode: TimePickerEntryMode.dialOnly,
                    );
                    if (timeofDay != null) {
                      if (timeofDay.period.toString().contains('am')) {
                        periodofDay = "AM";
                      }
                      if (timeofDay.minute < 10) {
                        minutes = "0" + timeofDay.minute.toString();
                      } else {
                        minutes = timeofDay.minute.toString();
                      }
                      setState(() {
                        selectedTime = timeofDay;
                        medication.loggedTimes!.add(DateTime(
                            DateTime.now().year,
                            DateTime.now().month,
                            DateTime.now().day,
                            timeofDay.hour,
                            timeofDay.minute));
                      });
                    }
                  } else {
                    // Show a message to the user that they have reached the maximum logs for today
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(
                              'You have reached the maximum logs for today.')),
                    );
                  }
                },
                child: Row(
                  children: [Icon(Icons.check), Text('Done')],
                ),
              ),
            ],
          ),
          SizedBox(height: 10),
        ],
      ),
    );
  }

  Future<void> openDialog(BuildContext context) async {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (buildContext) => Container(
            height: .85.sh,
            decoration: BoxDecoration(
              color: Color(0xffFAF2DF),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                topRight: Radius.circular(32),
              ),
            ),
            clipBehavior: Clip.hardEdge,
            child: MedicationFormPage(medication: null)));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MedicationWatcherBloc, MedicationWatcherState>(
      builder: (context, state) {
        if (state is Loading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is Success) {
          return Scaffold(
            appBar: CurvedAppBar(
              appBarColor: AppTheme.primaryColor,
              logoColor: const Color(0xffFAF2DF),
              height: .35.sw,
              topLeftIcon:IconButton(
                icon: const Icon(Icons.arrow_back,color: Colors.white,),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              // The height of your curved app bar
            ),
            body: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(22.0),
                child: Column(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: 1.sw,
                      height: 0.20.sw,
                      child: const Text(
                        "Active Medications",
                        style: TextStyle(color: Colors.black, fontSize: 25),
                      ),
                    ),
                    state.medications.isEmpty
                        ? const Text("No medications added yet.")
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: state.medications.length,
                            itemBuilder: (context, index) {
                              return _buildMedicationItem(
                                  state.medications[index]);
                            },
                          ),
                    Container(
                      padding: const EdgeInsets.fromLTRB(30, 0, 0, 0),
                      alignment: Alignment.centerLeft,
                      width: 1.sw,
                      height: 0.18.sw,
                      decoration: const BoxDecoration(
                        color: Color(0xffFAF2DF),
                        borderRadius: BorderRadius.all(Radius.circular(30)),
                        boxShadow: [
                          BoxShadow(
                            color: Color(
                                0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                            blurRadius: 4.0, // the blur radius
                            offset:
                                Offset(0, 1), // the x,y offset of the shadow
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          const Text(
                            "Add Medication",
                            style: TextStyle(
                              color: Colors.black,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(110, 0, 0, 0),
                            child: ElevatedButton(
                              key: const Key('add_medication_button'),
                              onPressed: () {
                                openDialog(context);
                              },
                              child: const Text(
                                '+',
                                style: TextStyle(
                                    fontSize: 40, fontWeight: FontWeight.w200),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } else if (state is LoadFailure) {
          return const Center(child: Text('Failed to load medications'));
        }
        return const Center(child: Text('Something went wrong'));
      },
    );
  }
}

extension DateTimeExtension on DateTime {
  bool isSameDate(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }
}
