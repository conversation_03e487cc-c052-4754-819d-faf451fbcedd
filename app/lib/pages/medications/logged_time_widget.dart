import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class LoggedTimeWidget extends StatelessWidget {
  final DateTime time;
  final VoidCallback onTap;

  const LoggedTimeWidget({
    required this.time,
    required this.onTap,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.topLeft,
      padding: EdgeInsets.zero,
      width: 170,
      height: 20,
      child:
      InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Text('Taken at ${DateFormat.jm().format(time)}', style: TextStyle(color: Colors.black),),
            // Space between icon and text
            Icon(Icons.arrow_forward_ios,size: 15,), // Text
          ],
        ),
      )

    );
  }
}
