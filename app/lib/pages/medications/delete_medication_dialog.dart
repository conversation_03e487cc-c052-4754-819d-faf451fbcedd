import 'package:account_management/application/manage_medications_bloc/manage_medications_bloc.dart';
import 'package:account_management/domain/model/medication_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';


class DeleteMedicationDialog extends StatelessWidget {
  final MedicationModel medication;
  const DeleteMedicationDialog({Key? key, required this.medication}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocListener<ManageMedicationsBloc, ManageMedicationsState>(

       listener: (context, state) {
        state.maybeMap(
          medicationFailure: (state) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('An unexpected error occurred',
                )),

            );
          },
          orElse: () {},
        );},
      child: AlertDialog(
        title: Text('Delete Medication',style:
          GoogleFonts.roboto(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).primaryColor,
          ),
        ),
        content: Text('Are you sure you want to delete this medication?',style:
          GoogleFonts.roboto(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: Theme.of(context).primaryColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              BlocProvider.of<ManageMedicationsBloc>(context).add(ManageMedicationsEvent.deleteMedication(medication));
              Navigator.of(context).pop();
            },
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }
}
