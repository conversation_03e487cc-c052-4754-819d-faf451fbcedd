import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../routing/app_pages.gr.dart';
@RoutePage()
 class WelcomePage extends StatelessWidget {

  const WelcomePage({

    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return  PopScope(
      canPop: false,
      child: Scaffold(
      body: SingleChildScrollView(
        child: Container(
          width:1.sw ,
          height: 1.sh,
          decoration: BoxDecoration(
            gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
            Color(0xFFFBF0D5), // #FBF0D5
        Color.fromRGBO(217, 217, 217, 0), // rgba(217, 217, 217, 0)
        ],
        stops: [0.1243, 1.0],
        ),
        ),
          child: Column(
          children: [
            SizedBox(height: .08.sh,),
            SvgPicture.asset('assets/logo/juno_logo_violet.svg'),
            SizedBox(height: .04.sh,),
            Text("Welcome",style: Theme.of(context).textTheme.headlineLarge,),
            Text("Pain relief made smarter",style: Theme.of(context).textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w200),),
            Image.asset('assets/welcome_page/welcome_page_dot.png'),
            SizedBox(height: 15,),
            GestureDetector(
              onTap: (){
                context.router.push(const LoginRoute());
              },
              child: Container(
                                width: .5.sw,
                                height: .12.sw,
                                decoration: BoxDecoration(
                                  borderRadius : BorderRadius.only(
                                    topLeft: Radius.circular(25),
                                    topRight: Radius.circular(25),
                                    bottomLeft: Radius.circular(25),
                                    bottomRight: Radius.circular(25),
                                  ),
                                  color : Theme.of(context).primaryColor,
                                ),
                child: Center(child: Text("Login",style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 20),)),
                            ),
            ),
            SizedBox(height: 10,),
            GestureDetector(
              onTap: (){
                context.router.push(const SignUpRoute());
              },
              child: Container(
                width: .5.sw,
                height: .12.sw,
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xffBEBADE),width: 1),
                  borderRadius : BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                    bottomLeft: Radius.circular(25),
                    bottomRight: Radius.circular(25),
                  ),
                  color : Colors.white,
                ),
                child: Center(child: Text("Sign up",style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 20),)),
              ),
            )





        ],
          ),
        ),
      ),
      ),
    );
  }
}
