import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'package:authentication/authentication.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../routing/app_pages.gr.dart';
@RoutePage()
class SignUpPage extends StatelessWidget {
  const SignUpPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: Color(0xffFFFBF2),
      resizeToAvoidBottomInset: false,
      appBar: CurvedAppBar(
        appBarColor:AppTheme.primaryColor,
        logoColor:AppTheme.loginAppBarColor,
        height: .35.sw, // The height of your curved app bar
        topLeftIcon: IconButton(
          icon: Icon(Icons.arrow_back_rounded,color: Colors.white,),
          onPressed: (){
            context.router.pop();
          },),

      ),

      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(height: .1.sw,width: 1.sw,),
            Platform.isIOS ?BlocProvider(
              create: (context) => getIt<SignInBloc>(),
              child: AppleSignInButtonWrapper(
                buttonChild:Container(
                  width: .75.sw,
                  height: .15.sw,
                  decoration:  BoxDecoration(color: AppTheme.loginButtonColor,  borderRadius : BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: .25.sw,
                        child:SvgPicture.asset('assets/social_icons/apple_social_icon.svg',height: 30,width: 30,) ,
                      ),
                      SizedBox(
                        width: .5.sw,
                        child: Text(
                          "Sign up with Apple ID",
                          style: Theme.of(context).textTheme.bodySmall!.copyWith(color: Color(0xff554C9F)),
                        ),
                      ),
                    ],
                  ),
                ), onSignInSuccess: (user ) {
                user.isOnboarded == true ? context.router.push(HomeRoute()) : context.router.push(GetStartedRoute()); }, onSignInFailure: (failure) {  },),
            ):Container(),
            SizedBox(height: 20),
            Container(
              width: .75.sw,
              height: .15.sw,
              child: BlocProvider(
                create: (context) => getIt<SignInBloc>(),
                child: GoogleSignInButtonWrapper(
                  buttonChild:  Container(
                    width: .75.sw,
                    height: .15.sw,
                    decoration:  BoxDecoration(color: AppTheme.loginButtonColor,  borderRadius : BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: .25.sw,
                          child:SvgPicture.asset('assets/social_icons/google_social_icon.svg',height: 30,width: 30,) ,
                        ),
                        SizedBox(
                          width: .5.sw,
                          child: Text(
                            "Sign up with Google",
                            style: Theme.of(context).textTheme.bodySmall!.copyWith(color: Color(0xff554C9F)),
                          ),
                        ),
                      ],
                    ),
                  ),
                  onSignInSuccess: (user ) {
                    user.isOnboarded == true ? context.router.push(HomeRoute()) : context.router.push(GetStartedRoute());
                  },
                  onSignInFailure: (failure) {
                    // Handle sign-in failure
                  },
                ),
              ),
            ),
            SizedBox(height: 40),
            Text("Or continue with email ",style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w700,color: Colors.black)),
            SizedBox(height: 20),
            SizedBox(
              height: 1.sw,
              width: .8.sw,
              child: BlocProvider(
                  create: (context) => getIt<SignInBloc>(),
                  child:EmailRegisterWrapper(
                      child: Container(
                        width: .5.sw,
                        height: .12.sw,
                        decoration: BoxDecoration(
                          borderRadius : BorderRadius.only(
                            topLeft: Radius.circular(25),
                            topRight: Radius.circular(25),
                            bottomLeft: Radius.circular(25),
                            bottomRight: Radius.circular(25),
                          ),
                          color : Theme.of(context).primaryColor,
                        ),
                        child: Center(child: Text("Sign up",style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 20),)),
                      ),
                      onRegisterSuccess: (user ) {
                        user.isEmailVerified == true ? user.isOnboarded == true ? context.router.push(HomeRoute()) : context.router.push(GetStartedRoute()): context.router.push(EmailVerificationRoute()); }, onRegisterFailure: (failure) { print(failure.toString()); }
                  )),
            ),
            SizedBox(height: 40),

          ],),
      ),
    );
  }
}
