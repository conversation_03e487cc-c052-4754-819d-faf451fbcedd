import 'dart:async';

import 'package:account_management/di/di.dart';
import 'package:authentication/application/bloc/email_verification/email_verification_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../custom_widgets/curved_app_bar.dart';
import '../../routing/app_pages.gr.dart';

@RoutePage()
class EmailVerificationPage extends StatelessWidget {
  const EmailVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocProvider(
        create: (context) => getIt<EmailVerificationBloc>()
          ..add(const RequestVerificationEmail()),
        child: EmailVerificationPageOverview(),
      ),
    );
  }
}

class EmailVerificationPageOverview extends StatefulWidget {
  const EmailVerificationPageOverview({super.key});

  @override
  State<EmailVerificationPageOverview> createState() => _EmailVerificationPageOverviewState();
}



class _EmailVerificationPageOverviewState extends State<EmailVerificationPageOverview> {
  Timer? timer;


  @override
  void initState() {
// TODO: implement initState
    super.initState();
    timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      context.read<EmailVerificationBloc>().add(const CheckEmailVerified());
    });
  }



  @override
  void dispose() {
// TODO: implement dispose
    timer?.cancel();
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return BlocListener<EmailVerificationBloc, EmailVerificationState>(
      listener: (context, state) {
        state.map(
          initial: (_) {},
          verificationInProgress: (_) {},
          emailNotVerified: (_) {},
          emailVerified: (_) {
            timer?.cancel();
            context.router.replace(const SplashRoute());
          },
          verificationEmailSent: (_) {
            Fluttertoast.showToast(msg: 'Verification email sent. Please check your email.');
          },
          verificationFailed: (_) {
            Fluttertoast.showToast(msg: 'Email verification failed');
          },
        );
      },

     child  : PopScope(
        canPop: false,
       child: Scaffold(
           backgroundColor: Color(0xffFFFBF2),
           appBar: CurvedAppBar(
             appBarColor: AppTheme.loginAppBarColor,
             logoColor: AppTheme.AppBarLogoColor,
             height: .35.sw,
             // The height of your curved app bar
             topLeftIcon: IconButton(
               icon: const Icon(Icons.arrow_back),
               onPressed: () {
                 context.router.back();
               },
             ),
           ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
             crossAxisAlignment: CrossAxisAlignment.center,
             children: [
                const SizedBox(height: 20),
                      Text('Verification email has been sent to your email address. Please verify your email.', style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: AppTheme.primaryColor,fontSize: 16),textAlign: TextAlign.center,),
             SizedBox(height: .25.sh),
                      CircularProgressIndicator(),
             SizedBox(height: .3.sh),
               GestureDetector(
                 onTap: (){
                   context.read<EmailVerificationBloc>().add(const ResendVerificationEmail());
                 },
                 child: Container(
                   width: .7.sw,
                   height: .15.sw,
                   decoration: BoxDecoration(
                     borderRadius : BorderRadius.only(
                       topLeft: Radius.circular(50),
                       topRight: Radius.circular(50),
                       bottomLeft: Radius.circular(50),
                       bottomRight: Radius.circular(50),
                     ),
                     color : Theme.of(context).primaryColor,
                   ),
                   child: Center(child: Text("Resend verification email",style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 20),)),
                 ),
               ),

                    ],
                  ),




          )

           ),
     ));
  }
}
