
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'package:authentication/authentication.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
@RoutePage()
class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({Key? key}) : super(key: key);

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffFFFBF2),
      appBar: CurvedAppBar(
        appBarColor: AppTheme.loginAppBarColor,
        logoColor: AppTheme.AppBarLogoColor,
        height: .35.sw, // The height of your curved app bar
      ),


      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(height: .1.sw,width: 1.sw,),
          Text("password reset link will be sent to your email ",style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w700,color: Colors.black)),
          SizedBox(height: 20),
          SizedBox(
            height: 1.sw,
            width: .8.sw,
            child: BlocProvider(
                create: (context) => getIt<SignInBloc>(),
                child:ResetPasswordWrapper(
                    child: Container(
                      width: .5.sw,
                      height: .12.sw,
                      decoration: BoxDecoration(
                        borderRadius : BorderRadius.only(
                          topLeft: Radius.circular(25),
                          topRight: Radius.circular(25),
                          bottomLeft: Radius.circular(25),
                          bottomRight: Radius.circular(25),
                        ),
                        color : Theme.of(context).primaryColor,
                      ),
                      child: Center(child: Text("Send Email",style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 20),)),
                    ),

                    onRegisterSuccess: (UserModel ) { context.router.back(); }, onRegisterFailure: (failure) { print(failure.toString()); }
                )
            ),
          ),
          SizedBox(height: 40),

        ],),
    );
  }
}
