import 'package:account_management/application/onboardin_form_bloc/onboarding_form_bloc.dart';
import 'package:account_management/di/di.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../routing/app_pages.gr.dart';

@RoutePage()
class GetStartedPage extends StatefulWidget {
  @override
  _GetStartedPageState createState() => _GetStartedPageState();
}

class _GetStartedPageState extends State<GetStartedPage> {
  int cycleLength = 28;
  int periodDays = 4;
  List<String>? contraception = [];
  DateTime dob = DateTime.now().subtract(Duration(days: 365 * 10));
  DateTime lastPeriod = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return BlocListener<OnboardingFormBloc, OnboardingFormState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      child: PopScope(
        canPop: false,
        child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: CurvedAppBar(

          appBarColor: AppTheme.primaryColor ,
          logoColor: AppTheme.loginAppBarColor,

          height: .35.sw, // The height of your curved app bar
        ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [

                  SizedBox(height: 150),
                  Text(
                    'Lets Get Started',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 26,

                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Help us fine tune our predictions for you!',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 24),
                  _buildbirthDayCard(),
                  SizedBox(height: 24),
                  _buildCycleLengthCard(),
                  SizedBox(height: 24),
                  _buildPeriodDaysCard(),
                  SizedBox(height: 24),
                  _buildlastPeriodDayCard(),
                  SizedBox(height: 24),
                  _buildContraceptionCard(),
                  SizedBox(height: 24),
                  _buildButtons(context),

                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCycleLengthCard() {
    return Card(
      color: AppTheme.loginAppBarColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'How long is your average cycle?',
              style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: IconButton(
                    icon: Icon(Icons.remove, size: 25, color: AppTheme.primaryColor,),
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      shadowColor: MaterialStateProperty.all(Colors.black),
                      backgroundColor: MaterialStateProperty.all(Color(0xFFFBF0D5)),
                    ),
                    onPressed: () {
                      setState(() {
                        if (cycleLength >10) cycleLength--;
                      });
                    },
                  ),
                ),
                Text(
                  '$cycleLength',
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 30
                  )
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: IconButton(
                    icon: Icon(
                        Icons.add,
                        size: 25,
                        color: AppTheme.primaryColor,

                    ),
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      shadowColor: MaterialStateProperty.all(Colors.black),
                      backgroundColor: MaterialStateProperty.all(Color(0xFFFBF0D5)),
                    ),
                    onPressed: () {
                      setState(() {
                        if (cycleLength < 50) cycleLength++;
                      });
                    },
                  ),
                ),
              ],
            ),
            Text(
              'Average menstrual cycle is between 23 - 35 days',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodDaysCard() {
    return Card(
     color:  AppTheme.loginAppBarColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'How many days is your period?',
              style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18
              )
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: IconButton(
                    icon: Icon(Icons.remove, size: 25, color: AppTheme.primaryColor,),
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      shadowColor: MaterialStateProperty.all(Colors.black),
                      backgroundColor: MaterialStateProperty.all(Color(0xFFFBF0D5)),
                    ),
                    onPressed: () {
                      setState(() {
                        if (periodDays > 0) periodDays--;
                      });
                    },
                  ),
                ),
                Text(
                  '$periodDays',
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 30
                  )
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: IconButton(
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      shadowColor: MaterialStateProperty.all(Colors.black),
                      backgroundColor: MaterialStateProperty.all(Color(0xFFFBF0D5)),
                    ),
                    icon: Icon(Icons.add, size: 25, color: AppTheme.primaryColor,),
                    onPressed: () {
                      setState(() {
                        if (periodDays < 15) periodDays++;
                      });
                    },
                  ),
                ),
              ],
            ),
            Text(
              'The average period usually lasts 3-5 days',
              style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContraceptionCard() {
    return Card(
      color: AppTheme.loginAppBarColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'What reproductive health management are you using?',
              style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 16
              )
            ),
            SizedBox(height: 8),
            _buildCheckBoxOption('Birth control pill'),
            _buildCheckBoxOption('Birth control ring'),
            _buildCheckBoxOption('Patch'),
            _buildCheckBoxOption('Hormonal IUD'),
            _buildCheckBoxOption('Non-Hormonal IUD'),
            _buildCheckBoxOption('Other'),

          ],
        ),
      ),
    );
  }

  Widget _buildCheckBoxOption(String value) {
    return CheckboxListTile(
      title: Text(value, style: Theme.of(context).textTheme.headlineMedium!.copyWith(
        fontSize: 14,
      )),
      value: contraception!.contains(value),
      onChanged: (bool? checked) {
        setState(() {
          if (checked!) {
            contraception!.add(value);
          } else {
            contraception!.remove(value);
          }
        });
      },
    );
  }

  Widget _buildButtons(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: (){
            context.read<OnboardingFormBloc>().add(OnboardingFormEvent.updateOnboardingForm(
             HealthDataModel(
               cycleLength: cycleLength,
               periodLength: periodDays,
               contraceptionType: contraception,
             ),
              dob
            ));
            context.router.push(HomeRoute());
          },
          child: Container(
            width: .5.sw,
            height: .12.sw,
            decoration: BoxDecoration(
              borderRadius : BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
                bottomLeft: Radius.circular(25),
                bottomRight: Radius.circular(25),
              ),
              color : Theme.of(context).primaryColor,
            ),
            child: Center(child: Text("Continue",style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 20),)),
          ),
        ),
        SizedBox(height: 20,),
        GestureDetector(
          onTap: () async{
            context.read<OnboardingFormBloc>().add(OnboardingFormEvent.updateOnboardingForm(
                HealthDataModel(
                  cycleLength: cycleLength,
                  periodLength: periodDays,
                  contraceptionType: contraception,
                  lastPeriodDate: lastPeriod,
                ),
              dob
            ));
            context.router.push(HomeRoute());
          },
          child: Container(
            width: .5.sw,
            height: .12.sw,
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xffBEBADE),width: 1),
              borderRadius : BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
                bottomLeft: Radius.circular(25),
                bottomRight: Radius.circular(25),
              ),
              color : Colors.white,
            ),
            child: Center(child: Text("Skip",style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 20),)),
          ),
        )
      ],
    );
  }

  Widget _buildbirthDayCard() {
    return Card(
      color: AppTheme.loginAppBarColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'When were you born?',
              style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18
              )
            ),
            SizedBox(height: 14),
            SizedBox(
              width: .8.sw,
              height: 300,
              child: DatePicker(
                initialDate: dob ?? DateTime.now(),
                minDate: DateTime(1960, 10, 10),
                maxDate: DateTime.now().subtract(Duration(days: 365 * 10)),
                padding: EdgeInsets.zero,
                currentDate: DateTime.now(),
                selectedDate: dob,
                onDateSelected: (date) {
                  dob = date;
                },
                currentDateDecoration: const BoxDecoration(),
                currentDateTextStyle:
                GoogleFonts.roboto(
                    color: Color(0xff30285D),
                ),
                daysOfTheWeekTextStyle: const TextStyle(
                  color: Color(0xff30285D),
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                disabledCellsDecoration: const BoxDecoration(),
                disabledCellsTextStyle: const TextStyle(
                    color: Colors.transparent
                ),
                enabledCellsDecoration: const BoxDecoration(),
                enabledCellsTextStyle:
                GoogleFonts.roboto(
                    color:Color(0xff30285D),
                    fontWeight: FontWeight.w400,
                    fontSize: 18,
                ),
                initialPickerType: PickerType.days,
                selectedCellDecoration: BoxDecoration(
                  color: Theme
                      .of(context)
                      .primaryColor,
                  shape: BoxShape.circle,
                ),
                selectedCellTextStyle: GoogleFonts.roboto(
                  color: const Color(0xffFBF0D5),
                  fontWeight: FontWeight.w400,
                  fontSize: 18,
                ),
                leadingDateTextStyle:
                GoogleFonts.roboto(
                    color: Color(0xff30285D),
                    fontWeight: FontWeight.w400,
                    fontSize: 20,
                ),
                slidersColor: Color(0xff30285D),
                highlightColor: Color(0xff30285D),
                slidersSize: 20,
                splashColor: Theme
                    .of(context)
                    .primaryColor
                    .withOpacity(0.5),
                splashRadius: 0,
                centerLeadingDate: true,
              ),
            ),
            SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
  Widget _buildlastPeriodDayCard() {
    return Card(
      color: AppTheme.loginAppBarColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
                'When was your last period?',
                style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 18
                )
            ),
            SizedBox(height: 14),
            SizedBox(
              width: .8.sw,
              height: 300,
              child: DatePicker(
                initialDate: lastPeriod ?? DateTime.now(),
                minDate: DateTime.now().subtract(Duration(days: 670)),
                maxDate: DateTime.now(),
                padding: EdgeInsets.zero,
                currentDate: DateTime.now(),
                selectedDate: lastPeriod,
                onDateSelected: (date) {
                  lastPeriod = date;
                },
                currentDateDecoration: const BoxDecoration(),
                currentDateTextStyle:
                GoogleFonts.roboto(
                    color: const Color(0xff30285D),
                    fontWeight: FontWeight.w400,
                    fontSize: 18,
                ),
                daysOfTheWeekTextStyle: const TextStyle(
                  color: Color(0xff30285D),
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                disabledCellsDecoration: const BoxDecoration(),
                disabledCellsTextStyle: const TextStyle(color: Colors.transparent),
                enabledCellsDecoration: const BoxDecoration(),
                enabledCellsTextStyle:
                GoogleFonts.roboto(
                    color: const Color(0xff30285D),
                    fontWeight: FontWeight.w400,
                    fontSize: 18,),
                initialPickerType: PickerType.days,
                selectedCellDecoration: BoxDecoration(
                  color: Theme
                      .of(context)
                      .primaryColor,
                  shape: BoxShape.circle,
                ),
                selectedCellTextStyle: GoogleFonts.roboto(
                  color: const Color(0xffFBF0D5),
                  fontWeight: FontWeight.w400,
                  fontSize: 18,
                ),
                leadingDateTextStyle:
                GoogleFonts.roboto(
                    color: const Color(0xff30285D),
                    fontWeight: FontWeight.w400,
                    fontSize: 20,
                ),
                slidersColor: const Color(0xff30285D),
                highlightColor: const Color(0xff30285D),
                slidersSize: 20,
                splashColor: Theme
                    .of(context)
                    .primaryColor
                    .withOpacity(0.5),
                splashRadius: 0,
                centerLeadingDate: true,
              ),
            ),
            SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

}
