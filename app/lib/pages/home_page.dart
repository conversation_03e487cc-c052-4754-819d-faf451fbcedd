import 'dart:async';
import 'package:account_management/account_management.dart';
import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart';
import 'package:analytics/analytics.dart' hide getIt;
import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design_system.dart';
// import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:juno_plus/pages/settings/settings_page.dart';
import 'package:notifications/application/custom_notification_stream/custom_notification_stream_bloc.dart';
import 'package:notifications/application/notification_bloc/notification_bloc.dart';
import '../helpers.dart';
import 'connectivity/connectivity_mapper.dart';
import 'connectivity/force_update.dart';
import 'dashboard/dashboard_page.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final List<String> _tabs = ['Remote', 'Dashboard', 'Settings'];
  String _selectedTab = 'Dashboard';
  // late AppUpdateCheck _appUpdateCheck;
  // Create an instance of your controller
  final TensController _controller = TensController();

  @override
  void dispose() {
    // Make sure to dispose the controller when the screen is disposed
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // _initializeAppUpdateCheck();
    // _checkForNotifications();
  }

  // Future<void> _initializeAppUpdateCheck() async {
  //   FirebaseRemoteConfig remoteConfig = await FirebaseRemoteConfig.instance;
  //   _appUpdateCheck = AppUpdateCheck(remoteConfig);
  //   // Check for updates
  //   _appUpdateCheck.checkForUpdate(context);
  // }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => getIt<NotificationBloc>()
                ..add(NotificationEvent.initialize(context)),
            ),
            BlocProvider(
              create: (_) => getIt<CustomNotificationStreamBloc>()
                ..add(CustomNotificationStreamEvent
                    .loadCustomNotificationStream()),
            ),

          ],
          child: MultiBlocListener(
            listeners: [
              BlocListener<NotificationBloc, NotificationState>(
                listener: (context, state) {
                  state.maybeMap(
                    success: (_) {
                      SnackBar(
                        content: Text('Notifications initialized successfully'),
                        duration: const Duration(seconds: 2),
                      );
                    },
                    failure: (failure) {
                      SnackBar(
                        content: Text(
                            'Failed to initialize notifications: ${failure.failure}'),
                        duration: const Duration(seconds: 2),
                      );
                    },
                    orElse: () {},
                  );
                },
              ),
              // BlocListener<BluetoothServiceBloc, BluetoothServiceState>(
              //   listener: (context, state) {
              //     state.maybeMap(
              //       bluetoothError: (error) {
              //         SnackBar(
              //           content: Text('Bluetooth Error: ${error.failure}'),
              //           duration: const Duration(seconds: 2),
              //         );
              //       },
              //       orElse: () {},
              //     );
              //   },
              // ),
              BlocListener<MenstrualCycleBloc, MenstrualCycleState>(
                listener: (context, state) {},
              ),
              // Analytics initialization listener
              BlocListener<AnalyticsInitializationBloc,
                  AnalyticsInitializationState>(
                listener: (context, state) {
                  if (state is AnalyticsInitializationLoading) {
                    // Show loading indicator for analytics initialization
                    if (kDebugMode) {
                      print('🔄 Analytics services initializing...');
                    }
                  } else if (state is AnalyticsInitializationSuccess) {
                    // Analytics initialized successfully
                    if (kDebugMode) {
                      print('✅ Analytics initialized successfully');
                      print('📊 Stats: ${state.stats}');
              
                      // Show detailed stats
                      final stats =
                          state.stats['storageStats'] as Map<String, dynamic>?;
                      if (stats != null) {
                        print('📱 Total sessions: ${stats['totalSessions']}');
                        print(
                            '📤 Unsynced sessions: ${stats['unsyncedSessions']}');
                        print('🌐 Online: ${stats['isOnline']}');
                        print('🔄 Syncing: ${stats['isSyncing']}');
                      }
                    }
              
                    // Optional: Show success snackbar in debug mode
                    if (kDebugMode) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Analytics services ready'),
                          backgroundColor: Colors.green,
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }
                  } else if (state is AnalyticsInitializationFailure) {
                    // Analytics initialization failed
                    if (kDebugMode) {
                      print(
                          '❌ Analytics initialization failed: ${state.error}');
                    }
              
                    // Show error snackbar in debug mode
                    if (kDebugMode) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Analytics initialization failed'),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 3),
                          action: SnackBarAction(
                            label: 'Retry',
                            textColor: Colors.white,
                            onPressed: () {
                              // Retry analytics initialization
                              context
                                  .read<AnalyticsInitializationBloc>()
                                  .add(InitializeAnalyticsEvent());
                            },
                          ),
                        ),
                      );
                    }
                  }
                },
              ),
              // Therapy management listener
              // BlocListener<TherapyManagementBloc, TherapyManagementState>(
              //   listener: (context, state) {
              //     state.maybeMap(
              //       loading: (_) {
              //         if (kDebugMode) {
              //           print('🔄 Starting therapy session...');
              //         }
              //       },
              //       therapyStarted: (_) {
              //         if (kDebugMode) {
              //           print('✅ Therapy session started successfully!');
              //           print(
              //               '📊 Session will run for 5 minutes and then auto-end');
              //         }
              //
              //         // Show success snackbar
              //         ScaffoldMessenger.of(context).showSnackBar(
              //           SnackBar(
              //             content:
              //                 Text('Therapy session started! (5 min duration)'),
              //             backgroundColor: Colors.green,
              //             duration: Duration(seconds: 3),
              //           ),
              //         );
              //       },
              //       error: (errorState) {
              //         if (kDebugMode) {
              //           print(
              //               '❌ Failed to start therapy session: ${errorState.message}');
              //         }
              //
              //         // Show error snackbar
              //         ScaffoldMessenger.of(context).showSnackBar(
              //           SnackBar(
              //             content: Text('Failed to start therapy session'),
              //             backgroundColor: Colors.red,
              //             duration: Duration(seconds: 3),
              //           ),
              //         );
              //       },
              //       orElse: () {},
              //     );
              //   },
              // ),
              // listen for custom notification stream
              BlocListener<CustomNotificationStreamBloc,
                  CustomNotificationStreamState>(
                listener: (context, state) {
                  state.maybeMap(
                    successNotification: (notification) {
                      if (notification.notification != null) {
                        print(
                            'Notification received: ${notification.notification}');
                        final notificationModel = notification.notification!;
                        print(
                            'opening notification: ${notificationModel.title}');
                        // Show the notification
                        showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              title: Text(
                                  notificationModel.title ?? 'Notification'),
                              content:
                                  Text(notificationModel.body ?? 'No body'),
                              actions: [
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text('OK'),
                                ),
                              ],
                            );
                          },
                        );
                      }
                    },
                    orElse: () {},
                  );
                },
              ),
              BlocListener<PeriodReminderSettingsBloc,PeriodReminderSettingsState>(
                listener: (context, state) {
                  state.maybeMap(
                    loading: (_) {
                      if (kDebugMode) {
                        print('🔄 Loading period reminder settings...');
                      }
                    },
                    loaded: (loadedState) {
                      if (kDebugMode) {
                        print('✅ Period reminder settings loaded');
                      }
                    },
                    failure: (failureState) {
                      if (kDebugMode) {
                        print(
                            '❌ Failed to load period reminder settings: ${failureState.message}');
                      }
                    },
                    orElse: () {},
                  );
                },
              ),
            ],
            child: Scaffold(
                bottomNavigationBar: Padding(
                  padding: const EdgeInsets.only(
                      left: 40.0, right: 40.0, bottom: 20.0, top: 20.0),
                  child: Container(
                    width: 1.sw,
                    height: .15.sw,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: const BorderRadius.all(Radius.circular(40)),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          key: const Key('remote'),
                          onTap: () {
                            setState(() {
                              _selectedTab = _tabs[0];
                            });

                            // setState(() {
                            //   context.router.push(RemoteOneRoute());
                            // });
                          },
                          child: SvgPicture.asset(
                            'assets/home/<USER>',
                            height: 30,
                            width: 30,
                            color: _selectedTab == 'Remote'
                                ? Colors.white
                                : const Color(0xffCFB4FE),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedTab = _tabs[1];
                            });
                          },
                          child: SvgPicture.asset(
                            'assets/home/<USER>',
                            height: 30,
                            width: 30,
                            color: _selectedTab == 'Dashboard'
                                ? Colors.white
                                : const Color(0xffCFB4FE),
                          ),
                        ),
                        GestureDetector(
                          key: const Key('settings'),
                          onTap: () {
                            setState(() {
                              _selectedTab = _tabs[2];
                            });
                            print('Settings clicked');

                            // DEBUG: Force resync all sessions to test sync issue
                            // if (kDebugMode) {
                            //   // _debugForceResyncSessions(context);
                            // }
                          },
                          child: SvgPicture.asset(
                            'assets/home/<USER>',
                            height: 30,
                            width: 30,
                            color: _selectedTab == 'Settings'
                                ? Colors.white
                                : const Color(0xffCFB4FE),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                body: _selectedTab == 'Dashboard'
                    ? DashboardPage()
                    : _selectedTab == 'Remote'
                        ? ConnectivityMapper()
                        : SettingsPage() // Use actual SettingsPage instead of TENS UI

                ),
          ),
        ));
  }

  /// Debug method to force resync all sessions
  // void _debugForceResyncSessions(BuildContext context) async {
  //   try {
  //     print('🔍 DEBUG: Force resyncing all sessions...');
  //
  //     // Get analytics facade from context
  //     final analyticsFacade = getIt<IAnalyticsFacade>();
  //
  //     // Force resync all sessions
  //     final result = await analyticsFacade.forceResyncAllSessions();
  //
  //     result.mapBoth(
  //       onLeft: (error) {
  //         print('❌ DEBUG: Force resync failed: $error');
  //         if (kDebugMode) {
  //           ScaffoldMessenger.of(context).showSnackBar(
  //             SnackBar(
  //               content: Text('Force resync failed: $error'),
  //               backgroundColor: Colors.red,
  //               duration: Duration(seconds: 5),
  //             ),
  //           );
  //         }
  //       },
  //       onRight: (_) {
  //         print('✅ DEBUG: Force resync completed successfully');
  //         if (kDebugMode) {
  //           ScaffoldMessenger.of(context).showSnackBar(
  //             SnackBar(
  //               content: Text('Force resync completed - check console logs'),
  //               backgroundColor: Colors.green,
  //               duration: Duration(seconds: 3),
  //             ),
  //           );
  //         }
  //       },
  //     );
  //   } catch (e) {
  //     print('❌ DEBUG: Exception during force resync: $e');
  //   }
  // }
}

enum CommandType {
  increase,
  decrease,
}

class Command {
  final CommandType type;

  Command(this.type);

  @override
  String toString() => 'Command(${type.toString().split('.').last})';
}

class TensController extends ChangeNotifier {
  final BluetoothRepository _bluetoothRepo = BluetoothRepository();
  final List<Command> _commandQueue = [];
  bool _isProcessingQueue = false;
  Timer? _queueTimer;

  // Selected level (what user has chosen)
  int _selectedLevel = 0;
  // Actual level (confirmed by device)
  int _actualLevel = 0;
  // Currently blinking level (transitioning)
  int _blinkingLevel = -1;
  bool _isBlinking = false;
  Timer? _blinkTimer;

  // Maximum level of intensity - now set to 10 as per requirement
  static const int maxLevel = 10;
  // Minimum level of intensity
  static const int minLevel = 0;

  int get selectedLevel => _selectedLevel;
  int get actualLevel => _actualLevel;
  bool get isProcessingQueue => _isProcessingQueue;
  int get queueLength => _commandQueue.length;
  int get blinkingLevel => _blinkingLevel;
  bool get isBlinking => _isBlinking;

  // Adds multiple increase commands to queue
  void addIncreaseCommands(int count) {
    if (_selectedLevel + count <= maxLevel) {
      _selectedLevel += count;
      for (int i = 0; i < count; i++) {
        _commandQueue.add(Command(CommandType.increase));
      }
      _startProcessingQueue();
      notifyListeners();
    }
  }

  // Adds a single increase command to queue
  void increaseLevel() {
    if (_selectedLevel < maxLevel) {
      _selectedLevel++;
      _commandQueue.add(Command(CommandType.increase));
      _startProcessingQueue();
      notifyListeners();
    }
  }

  // Decreases level immediately and clears any pending increases
  void decreaseLevel() {
    // First handle special case: if we're ramping up and user decides to decrease
    if (_commandQueue.any((cmd) => cmd.type == CommandType.increase)) {
      // Clear all queued commands
      _commandQueue.clear();

      // Set the selected level to one less than actual level
      // This is the key fix - ensure selected level becomes actual - 1
      _selectedLevel = _actualLevel > minLevel ? _actualLevel - 1 : minLevel;

      // Add a decrease command to go one level down from current actual level
      if (_actualLevel > minLevel) {
        _commandQueue.add(Command(CommandType.decrease));
      }
    } else if (_selectedLevel > minLevel) {
      // Normal decrease operation
      _selectedLevel--;
      _commandQueue.add(Command(CommandType.decrease));
    }

    // Ensure we're processing the queue
    _startProcessingQueue();
    notifyListeners();
  }

  // Adds multiple decrease commands to queue
  void addDecreaseCommands(int count) {
    if (_selectedLevel - count >= minLevel) {
      _selectedLevel -= count;
      for (int i = 0; i < count; i++) {
        _commandQueue.add(Command(CommandType.decrease));
      }
      _startProcessingQueue();
      notifyListeners();
    }
  }

  void _startProcessingQueue() {
    if (!_isProcessingQueue && _commandQueue.isNotEmpty) {
      _isProcessingQueue = true;
      _processNextCommand();
    }
  }

  // Method to handle blinking animation
  void _startBlinkAnimation(int level) {
    _blinkingLevel = level;
    _isBlinking = true;
    notifyListeners();

    // Blink for 1 second then stop
    _blinkTimer?.cancel();
    _blinkTimer = Timer(Duration(seconds: 1), () {
      _isBlinking = false;
      _blinkingLevel = -1;
      notifyListeners();
    });
  }

  Future<void> _processNextCommand() async {
    if (_commandQueue.isEmpty) {
      _isProcessingQueue = false;
      _queueTimer?.cancel();
      _queueTimer = null;
      notifyListeners();
      return;
    }

    Command cmd = _commandQueue.first;
    bool success = false;

    try {
      if (cmd.type == CommandType.increase) {
        // Start blinking the next level
        _startBlinkAnimation(_actualLevel + 1);

        success = await _bluetoothRepo.sendIncreaseCommand();
        if (success && _actualLevel < maxLevel) {
          _actualLevel++;
        }
      } else {
        // Start blinking the current level that will be decreased
        _startBlinkAnimation(_actualLevel);

        success = await _bluetoothRepo.sendDecreaseCommand();
        if (success && _actualLevel > minLevel) {
          _actualLevel--;
        }
      }

      if (success) {
        _commandQueue.removeAt(0);
        notifyListeners();
      }
    } catch (e) {
      print('Error processing command: $e');
      // Optional: implement retry logic here
    }

    // Schedule next command after 2 seconds
    _queueTimer?.cancel();
    _queueTimer = Timer(Duration(seconds: 2), _processNextCommand);
  }

  // Cancel all pending operations and clear queue
  void cancelAll() {
    _commandQueue.clear();
    _queueTimer?.cancel();
    _blinkTimer?.cancel();
    _isBlinking = false;
    _blinkingLevel = -1;
    _isProcessingQueue = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _queueTimer?.cancel();
    _blinkTimer?.cancel();
    super.dispose();
  }
}

// Placeholder for your existing BluetoothRepository class
class BluetoothRepository {
  Future<bool> sendIncreaseCommand() async {
    // Implement your actual Bluetooth command logic
    print('Sending increase command to device');
    await Future.delayed(
        Duration(milliseconds: 200)); // Simulate Bluetooth latency
    return true; // Return true if command was acknowledged
  }

  Future<bool> sendDecreaseCommand() async {
    // Implement your actual Bluetooth command logic
    print('Sending decrease command to device');
    await Future.delayed(
        Duration(milliseconds: 200)); // Simulate Bluetooth latency
    return true; // Return true if command was acknowledged
  }
}

class TensLevelDisplay extends StatelessWidget {
  final TensController controller;

  const TensLevelDisplay({Key? key, required this.controller})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, _) {
        return Column(
          children: [
            Text(
              'TENS Intensity Level',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(TensController.maxLevel, (index) {
                // Level is 1-based in display, but 0-based in controller
                int level = index + 1;

                // Determine the color based on state
                Color levelColor;
                if (level <= controller.actualLevel) {
                  // Actual level - dark purple
                  levelColor = Color(0xFF5B0E91);
                } else if (level == controller.blinkingLevel &&
                    controller.isBlinking) {
                  // Blinking level - light purple
                  levelColor = Color(0xFFAC7DD1);
                } else if (level <= controller.selectedLevel) {
                  // Selected but not yet actual - white
                  levelColor = Colors.white;
                } else {
                  // Unselected level - grey
                  levelColor = Colors.grey.shade300;
                }

                return Container(
                  margin: EdgeInsets.symmetric(horizontal: 5),
                  child: Column(
                    children: [
                      AnimatedContainer(
                        duration: Duration(milliseconds: 300),
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: levelColor,
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: level <= controller.selectedLevel
                                ? Colors.blue
                                : Colors.grey,
                            width: 2,
                          ),
                          boxShadow: level == controller.blinkingLevel &&
                                  controller.isBlinking
                              ? [
                                  BoxShadow(
                                      color: Color(0xFFAC7DD1),
                                      blurRadius: 10,
                                      spreadRadius: 2)
                                ]
                              : [],
                        ),
                      ),
                      SizedBox(height: 5),
                      Text(
                        '$level',
                        style: TextStyle(
                          fontWeight: level <= controller.actualLevel
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 15,
                  height: 15,
                  decoration: BoxDecoration(
                    color: Color(0xFF5B0E91),
                    borderRadius: BorderRadius.circular(7.5),
                  ),
                ),
                SizedBox(width: 5),
                Text('Active Level'),
                SizedBox(width: 20),
                Container(
                  width: 15,
                  height: 15,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Colors.blue, width: 2),
                    borderRadius: BorderRadius.circular(7.5),
                  ),
                ),
                SizedBox(width: 5),
                Text('Selected Level'),
                SizedBox(width: 20),
                Container(
                  width: 15,
                  height: 15,
                  decoration: BoxDecoration(
                    color: Color(0xFFAC7DD1),
                    borderRadius: BorderRadius.circular(7.5),
                  ),
                ),
                SizedBox(width: 5),
                Text('Processing'),
              ],
            ),
            SizedBox(height: 30),
            Text(
              'Selected Level: ${controller.selectedLevel}',
              style: TextStyle(fontSize: 18),
            ),
            Text(
              'Current Level: ${controller.actualLevel}',
              style: TextStyle(fontSize: 18),
            ),
          ],
        );
      },
    );
  }
}

class TensControlPanel extends StatelessWidget {
  final TensController controller;

  const TensControlPanel({Key? key, required this.controller})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        children: [
          Text(
            'Intensity Control',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildControlButton(
                icon: Icons.remove,
                onPressed: () => controller.decreaseLevel(),
                color: Colors.red.shade400,
              ),
              SizedBox(width: 60),
              _buildControlButton(
                icon: Icons.add,
                onPressed: () => controller.increaseLevel(),
                color: Colors.green.shade400,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: MaterialButton(
        onPressed: onPressed,
        color: color,
        textColor: Colors.white,
        padding: EdgeInsets.all(16),
        shape: CircleBorder(),
        child: Icon(
          icon,
          size: 36,
        ),
      ),
    );
  }
}
