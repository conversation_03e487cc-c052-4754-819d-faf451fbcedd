import 'package:account_management/application/update_health_data_bloc/update_health_data_bloc.dart';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:design_system/design/theme.dart';
import 'package:google_fonts/google_fonts.dart';

class UpdateCycleLengthDialog extends StatefulWidget {
    final int cycleLength ;
   UpdateCycleLengthDialog({required this.cycleLength, Key? key}) : super(key: key);

  @override
  State<UpdateCycleLengthDialog> createState() => _UpdateCycleLengthDialogState();
}

class _UpdateCycleLengthDialogState extends State<UpdateCycleLengthDialog> {
  int cycleLength = 28;
  @override
  void initState() {
    cycleLength = widget.cycleLength;
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UpdateHealthDataBloc, UpdateHealthDataState>(
      builder: (context, state) {
        return AlertDialog(
          backgroundColor: AppTheme.loginAppBarColor,
          title:  Center(
            child: Text('Cycle Length',style:Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18
            ),),
          ),
          content: Padding(
            padding: const EdgeInsets.all(0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'How long is your average cycle?',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 16
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(Icons.remove, size: 30),
                      onPressed: () {
                        setState(() {
                          if (cycleLength > 21) cycleLength--;
                        });
                      },
                    ),
                    Text(
                        '${cycleLength}',
                        style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 30
                        )
                    ),
                    IconButton(
                      icon: Icon(Icons.add, size: 30),
                      onPressed: () {
                        setState(() {
                          if (cycleLength < 40) cycleLength++;
                        });
                      },
                    ),
                  ],
                ),
                Text(
                  'Average cycle length is between 21 - 40 days',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                BlocProvider.of<UpdateHealthDataBloc>(context).add(UpdateCycleLength(cycleLength));
                Fluttertoast.showToast(msg: 'Cycle length updated successfully');
                Navigator.of(context).pop();
              },
              child: const Text('Confirm'),
            )

          ],
        );
      },
    );
  }
}



class UpdatePeriodLengthDialog extends StatefulWidget {
   final int periodLength;
  UpdatePeriodLengthDialog({required this.periodLength, Key? key}) : super(key: key);

  @override
  State<UpdatePeriodLengthDialog> createState() => _UpdatePeriodLengthDialogState();
}


class _UpdatePeriodLengthDialogState extends State<UpdatePeriodLengthDialog> {
  int periodLength = 3;

  @override
  void initState() {
   periodLength = widget.periodLength;
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UpdateHealthDataBloc, UpdateHealthDataState>(
      builder: (context, state) {
        return AlertDialog(
          backgroundColor: AppTheme.loginAppBarColor,
          title:  Center(
            child: Text('Period Length',style:Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18
            ),),
          ),
          content: Padding(
            padding: const EdgeInsets.all(0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'How long is your average period?',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 16
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(Icons.remove, size: 30),
                      onPressed: () {
                        setState(() {
                          if (periodLength > 1) periodLength--;
                        });
                      },
                    ),
                    Text(
                        '${periodLength}',
                        style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 30
                        )
                    ),
                    IconButton(
                      icon: Icon(Icons.add, size: 30),
                      onPressed: () {
                        setState(() {
                          if (periodLength < 10) periodLength++;
                        });
                      },
                    ),
                  ],
                ),
                Text(
                  'Average period length is between 3 - 7 days',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                BlocProvider.of<UpdateHealthDataBloc>(context).add(UpdatePeriodLength(periodLength));
                Fluttertoast.showToast(msg: 'Period length updated successfully');
                Navigator.of(context).pop();
              },
              child: const Text('Confirm'),
            )

          ],
        );
      },
    );
  }
}


class UpdateOvulationDayDialog extends StatefulWidget {
  final DateTime ovulationDate;
  UpdateOvulationDayDialog({required this.ovulationDate, Key? key}) : super(key: key);

  @override
  State<UpdateOvulationDayDialog> createState() => _UpdateOvulationDayDialogState();
}

class _UpdateOvulationDayDialogState extends State<UpdateOvulationDayDialog> {
  DateTime ovulationDate = DateTime.now();

  @override
  void initState() {
    ovulationDate = widget.ovulationDate;
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UpdateHealthDataBloc, UpdateHealthDataState>(
      builder: (context, state) {
        return AlertDialog(
          backgroundColor: AppTheme.loginAppBarColor,
          title: Center(
            child: Text('Ovulation Date', style: Theme
                .of(context)
                .textTheme
                .headlineMedium!
                .copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18
            ),),
          ),
          content:  SizedBox(
            width: .7.sw,
            height: 240,
            child: DatePicker(
              initialDate: ovulationDate,
              minDate: DateTime(1960, 10, 10),
              maxDate: DateTime.now(),
              padding: EdgeInsets.zero,
              currentDate: DateTime.now(),
              selectedDate: ovulationDate,
              onDateSelected: (date) {
                ovulationDate = date;
              },
              currentDateDecoration: const BoxDecoration(),
              currentDateTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              daysOfTheWeekTextStyle: const TextStyle(
                color: Color(0xff71456F),
                fontWeight: FontWeight.bold,
                fontSize: 13,
              ),
              disabledCellsDecoration: const BoxDecoration(),
              disabledCellsTextStyle: const TextStyle(),
              enabledCellsDecoration: const BoxDecoration(),
              enabledCellsTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              initialPickerType: PickerType.days,
              selectedCellDecoration: BoxDecoration(
                color: Theme
                    .of(context)
                    .primaryColor,
                shape: BoxShape.circle,
              ),
              selectedCellTextStyle: GoogleFonts.roboto(
                color: const Color(0xffFBF0D5),
                fontWeight: FontWeight.w400,
                fontSize: 12,
              ),
              leadingDateTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              slidersColor: const Color(0xff71456F),
              highlightColor: const Color(0xff71456F),
              slidersSize: 20,
              splashColor: Theme
                  .of(context)
                  .primaryColor
                  .withOpacity(0.5),
              splashRadius: 0,
              centerLeadingDate: true,
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                BlocProvider.of<UpdateHealthDataBloc>(context).add(
                    UpdateHealthDataEvent.updateOvulationDate(ovulationDate));
                Fluttertoast.showToast(
                    msg: 'Ovulation day updated successfully');
                Navigator.of(context).pop();
              },
              child: const Text('Confirm'),
            )

          ],
        );
      },
    );
  }
  }