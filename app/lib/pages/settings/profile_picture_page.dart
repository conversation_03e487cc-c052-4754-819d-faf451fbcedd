import 'package:account_management/account_management.dart';
import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart';
import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import '../../helpers.dart';

@RoutePage()
class ProfilePicturePage extends StatefulWidget {
  @override
  State<ProfilePicturePage> createState() => _ProfilePicturePageState();
}

class _ProfilePicturePageState extends State<ProfilePicturePage> {
  String imageUrl = "";

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) => getIt<AccountWatcherBloc>()
              ..add(AccountWatcherEvent.watchAllStarted())),
        BlocProvider<AccountManagementBloc>(
          create: (context) => getIt<AccountManagementBloc>(),
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<AccountManagementBloc, AccountManagementState>(
            listener: (context, state) {
              state.map(
                initial: (_) {},
                updating: (_) {},
                updated: (_) {
                  // Trigger an update to the AccountWatcherBloc to reload account details
                  context.read<AccountWatcherBloc>().add(AccountWatcherEvent.watchAllStarted());
                },
                updateFailure: (state) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(state.failure.toString())),
                  );
                },
              );
            },
          ),
        ],
        child: BlocBuilder<AccountWatcherBloc, AccountWatcherState>(
          builder: (context, state) {
            return state.map(
              initial: (_) => Container(),
              loading: (_) => Center(
                child: CircularProgressIndicator(
                  color: AppTheme.primaryColor,
                ),
              ),
              loadSuccess: (state) {
                imageUrl = state.accountDetails.photoURL ??
                    "https://firebasestorage.googleapis.com/v0/b/junoplus-dev.appspot.com/o/profile_place_holder.png?alt=media&token=61113f40-e3b2-4c66-9843-2318aa4f38e0";

                return PopScope(
                  canPop: false,
                  child: Scaffold(
                    backgroundColor: Colors.black,
                    appBar: AppBar(

                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      centerTitle: true,
                      leading: IconButton(
                        key: const Key('back_button'),
                        icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
                        onPressed: () {
                          context.router.back();
                        },
                      ),
                      title: Text(
                        'Profile photo',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: Colors.white, fontSize: 20),
                      ),

                      actions: [
                        GestureDetector(
                          onTap: () {
                            showBottomSheet(context, context.read<AccountManagementBloc>());
                          },
                          child: Container(
                            height: 50,
                            width: 50,
                            child: Center(
                              child: Text(
                                "Edit",
                                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    color: Colors.white, fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                    body: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 1.sw,
                          height: 1.sw,
                          child: CachedNetworkImage(
                            imageUrl: imageUrl,
                            fit: BoxFit.cover,
                            cacheManager: DefaultCacheManager(),
                          ),
                        )
                      ],
                    ),
                  ),
                );
              },
              loadFailure: (_) => Container(),
            );
          },
        ),
      ),
    );
  }

  void showBottomSheet(BuildContext context, AccountManagementBloc bloc) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                key: Key('camera'),
                leading: Icon(Icons.camera_alt),
                title: Text("Take a photo"),
                onTap: () async {
                  final XFile? image = await ImagePicker().pickImage(source: ImageSource.camera);
                  if (image != null) {
                    await DefaultCacheManager().removeFile(imageUrl);
                    bloc.add(UpdateProfilePicture(image));
                    Navigator.pop(context);
                  }
                },
              ),
              ListTile(
                key: Key('gallery'),
                leading: Icon(Icons.photo),
                title: Text("Choose from gallery"),
                onTap: () async {
                  final XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);
                  if (image != null) {
                    await DefaultCacheManager().removeFile(imageUrl);
                    bloc.add(UpdateProfilePicture(image));
                    Navigator.pop(context);
                  }
                },
              ),
              ListTile(
                leading: Icon(Icons.delete),
                title: Text("Delete photo"),
                onTap: () async {
                  await DefaultCacheManager().removeFile(imageUrl);
                  bloc.add(DeleteProfilePicture());
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
