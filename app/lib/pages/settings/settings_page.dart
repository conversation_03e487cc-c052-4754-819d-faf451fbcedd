import 'dart:ui';

import 'package:account_management/account_management.dart';
import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart';
import 'package:account_management/application/manage_medications_bloc/manage_medications_bloc.dart';
import 'package:account_management/application/update_health_data_bloc/update_health_data_bloc.dart';
import 'package:account_management/domain/failure/health_data_failure.dart';
import 'package:authentication/application/bloc/auth/auth_bloc.dart';
import 'package:authentication/authentication.dart';
import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import '../../routing/app_pages.gr.dart';
import 'health_data_dialogs.dart';

@RoutePage()
class SettingsPage extends StatefulWidget {
  SettingsPage({Key? key}) : super(key: key);

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final _formKey = GlobalKey<FormState>();

  List<String> languages = ['English', 'French'];

  String currentLanguage = '';
  bool isExpanded = false;




  int calculateAge(DateTime dob) {
    final currentDate = DateTime.now();
    int age = currentDate.year - dob.year;
    if (dob.month > currentDate.month ||
        (dob.month == currentDate.month && dob.day > currentDate.day)) {
      age--;
    }
    return age;
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
  providers: [
    BlocProvider(
  create: (context) => getIt<AccountWatcherBloc>()
    ..add(const AccountWatcherEvent.watchAllStarted()),
),
    BlocProvider(
      create: (context) => getIt<AccountManagementBloc>(),
    ),
    BlocProvider(create: (context) => getIt<AuthBloc>()),
    BlocProvider(create: (context) => getIt<SignInBloc>()),
    BlocProvider(create: (context) => getIt<UpdateHealthDataBloc>()),
  ],
  child: MultiBlocListener(
    listeners: [
      BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          state.maybeMap(orElse: () {}, unauthenticated: (_) {
           context.router.replace(const LoginRoute());
          });
        },
      ),
      BlocListener<SignInBloc,SignInState>(
          listener: (context, state) {
            state.passwordResetOption.map(
                    (either) => either.mapBoth(onLeft: (failure) {

                        Fluttertoast.showToast(
                          msg: failure.map(
                            cancelledByUser: (_) => 'Cancelled',
                            serverError: (_) => 'Server error',
                            emailAlreadyInUse: (_) => 'Email already in use',
                            invalidEmailAndPasswordCombination: (_) =>
                            'Invalid email and password combination',
                            userNotFound: (_) => 'User not found',
                            emailVerificationFailed: (_) => 'Email verification failed',
                            emailVerificationSendFailure: (_) => 'Email verification email was not sent',
                          ),

                        );

                    }, onRight: (_) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Password reset email sent'),
                          duration: Duration(seconds: 3),
                        ),
                      );
                    }));
                    }
            ),
      BlocListener<UpdateHealthDataBloc, UpdateHealthDataState>(
        listener: (context, state) {
          state.map(
            initial: (_) {},
            loading: (_) {},
            updatedHealthData: (state) {
              Fluttertoast.showToast(
                msg: 'Updated ${state.type}',
              );
              }, failure: ( value) {
                Fluttertoast.showToast(
                  msg: value.failure.map(
                    unexpected: (_) => 'An unexpected error occurred',
                    cycleLengthFailure: (_) => 'could not update cycle length',
                    periodLengthFailure: (_) => 'could not update period length',
                    ovulationDateFailure: (_) => 'could not update ovulation date',
                    contraceptionTypeFailure: (_) => 'could not update contraception type',


                  ),
                );
              },

          );
        },
      ),



    ],
  child: BlocBuilder<AccountWatcherBloc, AccountWatcherState>(
        builder: (context, state) {
      return state.map(
        initial: (_) =>  Scaffold(
        appBar: CurvedAppBar(
          appBarColor: AppTheme.primaryColor,
          logoColor: Color(0xffFAF2DF),
          height: .35.sw, // The height of your curved app bar
        ),),
        loading: (_) => Scaffold(
          appBar: CurvedAppBar(
            appBarColor: AppTheme.primaryColor,
            logoColor: Color(0xffFAF2DF),
            height: .35.sw, // The height of your curved app bar
          ),
          body: Center(
            child: CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
          ),
        ),
        loadSuccess: (state) {
          {
           print(" ${state.accountDetails.uid}================================");
            final dob = state.accountDetails.dateOfBirth?.toDate() ?? DateTime.now();
            final age = calculateAge(dob);
            return Scaffold(
              extendBodyBehindAppBar: true,
              appBar: CurvedAppBar(
                appBarColor: AppTheme.primaryColor,
                logoColor: Color(0xffFAF2DF),
                height: .35.sw,
     // The height of your curved app bar
              ),
              body: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(25.0),
                  child: Column(
                    children: [
                      SizedBox(
                        height: .35.sw,
                      ),
                      Container(
                          width: 1.sw,
                          height: .8.sw,
                          decoration: BoxDecoration(
                              color: Color(0xffFAF2DF),
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0x40000000),
                                  // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                  blurRadius: 4.0,
                                  // the blur radius
                                  offset: Offset(
                                      0, 1), // the x,y offset of the shadow
                                ),
                              ],
                              borderRadius:
                                  BorderRadius.all(Radius.circular(32))),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 20,
                              ),
                              Align(
                                alignment: Alignment.centerRight,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20.0),
                                  child: GestureDetector(
                                    key: const Key('edit_profile'),
                                    onTap: () => AutoRouter.of(context)
                                        .push(ProfileRoute()),
                                    child: Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: Color(0xffEADDFF),
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(12)),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(6.0),
                                        child: Icon(
                                          Icons.mode_edit_outline_outlined,
                                          color: Color(0xff21005d),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Column(
                                children: [
                                  Text(
                                    'My Profile',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            fontSize: 24,
                                            color: Color(0xff26204a),
                                            fontWeight: FontWeight.w600),
                                  ),
                                  SizedBox(
                                    width: 40,
                                  ),
                                  Container(
                                    width: 100,
                                    height: 100,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      image: DecorationImage(
                                        image: CachedNetworkImageProvider(
                                            state.accountDetails.photoURL ??
                                                'https://firebasestorage.googleapis.com/v0/b/junoplus-dev.appspot.com/o/profile_place_holder.png?alt=media&token=61113f40-e3b2-4c66-9843-2318aa4f38e0'),
                                        fit:  state.accountDetails.photoURL != null? BoxFit.cover : BoxFit.scaleDown,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 0,
                                  ),
                                  Text(
                                    'Name: ${state.accountDetails.userName}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            fontSize: 20,
                                            color: Color(0xff26204a),
                                            fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.start,
                                  ),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  Text(
                                    'Age: $age',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            fontSize: 20,
                                            color: Color(0xff26204a),
                                            fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.start,
                                  ),
                                ],
                              ),
                            ],
                          )),
                      SizedBox(
                        height: 20,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Health Data',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                    fontSize: 22,
                                    color: Color(0xff26204a),
                                    fontWeight: FontWeight.bold),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Container(
                              width: 1.sw,
                              decoration: BoxDecoration(
                                  color: Color(0xffFAF2DF),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                      blurRadius: 4.0,
                                      // the blur radius
                                      offset: Offset(
                                          0, 1), // the x,y offset of the shadow
                                    ),
                                  ],
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(32))),
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 10,
                                    ),

                                    GestureDetector(
                                      onTap: (){
                                        showDialog(
                                            context: context, builder: (context) =>
                                            BlocProvider(
                                                create: (context) => getIt<UpdateHealthDataBloc>(),
                                                child: UpdatePeriodLengthDialog(periodLength: state.accountDetails.healthData != null? state.accountDetails.healthData!.periodLength?? 6 : 6,)));

                                      },
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            width: 30,
                                            height: 30,
                                            child: SvgPicture.asset(
                                              'assets/settings/period.svg',
                                              width: 30,
                                              height: 30,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 25,
                                          ),
                                          Container(
                                              width: .38.sw,
                                              child: Align(
                                                  alignment: Alignment.centerLeft,
                                                  child: Text(
                                                    'Period Length',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                            fontSize: 18,
                                                            color:
                                                                Color(0xff26204a),
                                                            fontWeight:
                                                                FontWeight.w400),
                                                  ))),
                                          Container(
                                              width: .25.sw,
                                              child: Align(
                                                  alignment:
                                                      Alignment.centerRight,
                                                  child: Text(
                                                    '${state.accountDetails.healthData?.periodLength}',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                            fontSize: 18,
                                                            color:
                                                                Color(0xff26204a),
                                                            fontWeight:
                                                                FontWeight.w400),
                                                  ))),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Divider(
                                      thickness: 1,
                                    ),
                                    GestureDetector(
                                      onTap: (){
                                        showDialog(
                                            context: context, builder: (context) =>
                                            BlocProvider(
                                                create: (context) => getIt<UpdateHealthDataBloc>(),
                                                child: UpdateCycleLengthDialog(cycleLength: state.accountDetails.healthData != null? state.accountDetails.healthData!.cycleLength?? 28 : 28,)));

                                      },
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            width: 30,
                                            height: 30,
                                            child: SvgPicture.asset(
                                              'assets/settings/cycle.svg',
                                              width: 30,
                                              height: 30,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 25,
                                          ),
                                          Container(
                                              width: .38.sw,
                                              child: Align(
                                                  alignment: Alignment.centerLeft,
                                                  child: Text(
                                                    'Cycle Length',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                            fontSize: 18,
                                                            color:
                                                                Color(0xff26204a),
                                                            fontWeight:
                                                                FontWeight.w400),
                                                  ))),
                                          Container(
                                              width: .25.sw,
                                              child: Align(
                                                  alignment:
                                                      Alignment.centerRight,
                                                  child: Text(
                                                    '${state.accountDetails.healthData?.cycleLength??"28"} Days',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                            fontSize: 18,
                                                            color:
                                                                Color(0xff26204a),
                                                            fontWeight:
                                                                FontWeight.w400),
                                                  ))),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Divider(
                                      thickness: 1,
                                    ),
                                    GestureDetector(
                                      onTap: (){
                                        showDialog(
                                            context: context, builder: (context) =>
                                            BlocProvider(
                                                create: (context) => getIt<UpdateHealthDataBloc>(),
                                                child: UpdateOvulationDayDialog(ovulationDate: state.accountDetails.healthData != null? state.accountDetails.healthData!.ovulationDate ?? DateTime.now() : DateTime.now(),)));

                                      },
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            width: 30,
                                            height: 30,
                                            child: SvgPicture.asset(
                                              'assets/settings/ovulation.svg',
                                              width: 30,
                                              height: 30,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 25,
                                          ),
                                          Container(
                                              width: .38.sw,
                                              child: Align(
                                                  alignment: Alignment.centerLeft,
                                                  child: Text(
                                                    'Ovulation',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                            fontSize: 18,
                                                            color:
                                                                Color(0xff26204a),
                                                            fontWeight:
                                                                FontWeight.w400),
                                                  ))),
                                          Container(
                                              width: .25.sw,
                                              child: Align(
                                                  alignment:
                                                      Alignment.centerRight,
                                                  child: Text(
                                                    '${state.accountDetails.healthData?.ovulationDate !=null? DateFormat('d MMMM yyyy').format(state.accountDetails.healthData?.ovulationDate ?? DateTime.now()): 'Tap to set'}',
                                                    textAlign: TextAlign.right,
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                            fontSize: 18,
                                                            color:
                                                                Color(0xff26204a),
                                                            fontWeight:
                                                                FontWeight.w400),
                                                  ))),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ))
                        ],
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Reminders',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                    fontSize: 22,
                                    color: Color(0xff26204a),
                                    fontWeight: FontWeight.bold),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Container(
                              width: 1.sw,
                              decoration: BoxDecoration(
                                  color: Color(0xffFAF2DF),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                      blurRadius: 4.0,
                                      // the blur radius
                                      offset: Offset(
                                          0, 1), // the x,y offset of the shadow
                                    ),
                                  ],
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(32))),
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: 30,
                                          height: 30,
                                          child: SvgPicture.asset(
                                            'assets/settings/period.svg',
                                            width: 30,
                                            height: 30,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 25,
                                        ),
                                        Container(
                                            width: .4.sw,
                                            child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      'Period & Ovulation',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .bodyMedium!
                                                          .copyWith(
                                                              fontSize: 18,
                                                              color: Color(
                                                                  0xff26204a),
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400),
                                                    ),
                                                    Text(
                                                      '2 Days before',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .bodySmall!
                                                          .copyWith(
                                                              fontSize: 16,
                                                              color: Color(
                                                                  0xff26204a),
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w100),
                                                    ),
                                                  ],
                                                ))),
                                        Container(
                                          width: .22.sw,
                                          child: Switch(
                                            materialTapTargetSize:
                                                MaterialTapTargetSize
                                                    .shrinkWrap,
                                            value: true,
                                            onChanged: (value) {},
                                            thumbIcon:
                                                WidgetStateProperty.resolveWith(
                                                    (Set states) {
                                              if (states.contains(
                                                  WidgetState.selected)) {
                                                return const Icon(Icons.check,
                                                    color: Color(0xff6750A4));
                                              }
                                              return null; // All other states will use the default thumbIcon.
                                            }),
                                            thumbColor:
                                                WidgetStateProperty.resolveWith(
                                                    (Set<WidgetState> states) {
                                              if (states.contains(
                                                  WidgetState.selected)) {
                                                return Colors.white;
                                              }
                                              return Colors
                                                  .white; // Use the component's default.
                                            }),
                                            activeColor: Color(0xff6750A4),
                                            activeTrackColor: Color(0xff6750A4),
                                            trackOutlineWidth:
                                                WidgetStateProperty.resolveWith(
                                                    (Set<WidgetState> states) {
                                              if (states.contains(
                                                  WidgetState.selected)) {
                                                return 10;
                                              }
                                              return 10; // Use the component's default.
                                            }),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Divider(
                                      thickness: 1,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: 30,
                                          height: 30,
                                          child: SvgPicture.asset(
                                            'assets/remote/battery_icon.svg',
                                            width: 30,
                                            height: 30,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 25,
                                        ),
                                        Container(
                                            width: .38.sw,
                                            child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'Battery',
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium!
                                                            .copyWith(
                                                                fontSize: 18,
                                                                color: Color(
                                                                    0xff26204a),
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400),
                                                      ),
                                                      Text(
                                                        '2 Days before',
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall!
                                                            .copyWith(
                                                                fontSize: 16,
                                                                color: Color(
                                                                    0xff26204a),
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w100),
                                                      ),
                                                    ],
                                                  ),
                                                ))),
                                        Container(
                                          width: .25.sw,
                                          child: Switch(
                                            materialTapTargetSize:
                                                MaterialTapTargetSize
                                                    .shrinkWrap,
                                            value: true,
                                            onChanged: (value) {},
                                            thumbIcon:
                                                WidgetStateProperty.resolveWith(
                                                    (Set states) {
                                              if (states.contains(
                                                  WidgetState.selected)) {
                                                return const Icon(Icons.check,
                                                    color: Color(0xff6750A4));
                                              }
                                              return null; // All other states will use the default thumbIcon.
                                            }),
                                            thumbColor:
                                                WidgetStateProperty.resolveWith(
                                                    (Set<WidgetState> states) {
                                              if (states.contains(
                                                  WidgetState.selected)) {
                                                return Colors.white;
                                              }
                                              return Colors
                                                  .white; // Use the component's default.
                                            }),
                                            activeColor: Color(0xff6750A4),
                                            activeTrackColor: Color(0xff6750A4),
                                            trackOutlineWidth:
                                                WidgetStateProperty.resolveWith(
                                                    (Set<WidgetState> states) {
                                              if (states.contains(
                                                  WidgetState.selected)) {
                                                return 10;
                                              }
                                              return 10; // Use the component's default.
                                            }),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ))
                        ],
                      ),
                      SizedBox(
                        height: 35,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'General Settings',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                    fontSize: 22,
                                    color: Color(0xff26204a),
                                    fontWeight: FontWeight.bold),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Container(
                              width: 1.sw,
                              decoration: BoxDecoration(
                                  color: Color(0xffFAF2DF),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(
                                          0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                      blurRadius: 4.0, // the blur radius
                                      offset: Offset(
                                          0, 1), // the x,y offset of the shadow
                                    ),
                                  ],
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(32))),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    left: 20.0, right: 20.0, top: 5, bottom: 5),
                                child: Column(
                                  children: [
                                    // Theme(
                                    //   data: Theme.of(context).copyWith(
                                    //       dividerColor: Colors.transparent),
                                    //   child: ExpansionTile(
                                    //     title: Align(
                                    //       alignment: Alignment(-0.80, 0),
                                    //       child: Text(
                                    //         'Change Email',
                                    //         style: Theme.of(context)
                                    //             .textTheme
                                    //             .bodyMedium!
                                    //             .copyWith(
                                    //                 fontSize: 18,
                                    //                 color: Color(0xff26204a),
                                    //                 fontWeight:
                                    //                     FontWeight.w400),
                                    //       ),
                                    //     ),
                                    //     leading: Icon(
                                    //       Icons.email_outlined,
                                    //       size: 30,
                                    //       color: Color(0xff6750A4),
                                    //     ),
                                    //     trailing: Icon(
                                    //       Icons.keyboard_arrow_down_rounded,
                                    //       size: 50,
                                    //       color: Color(0xff6750A4),
                                    //       weight: 100,
                                    //     ),
                                    //     tilePadding:
                                    //         EdgeInsets.only(left: 10, right: 4),
                                    //     children: [
                                    //       Form(
                                    //         child: Column(
                                    //           children: [
                                    //             SizedBox(
                                    //               width: 170,
                                    //               child: TextFormField(
                                    //                 obscureText: true,
                                    //                 decoration:
                                    //                     const InputDecoration(
                                    //                   contentPadding:
                                    //                       EdgeInsets.only(
                                    //                           top: 50),
                                    //                   label: Text(
                                    //                     'New Email',
                                    //                     style: TextStyle(
                                    //                       fontSize: 18,
                                    //                       color:
                                    //                           Color(0xff26204A),
                                    //                       fontWeight:
                                    //                           FontWeight.w400,
                                    //                     ),
                                    //                   ),
                                    //                 ),
                                    //                 onSaved: (String? value) {},
                                    //                 validator: (String? value) {
                                    //                   return (value != null &&
                                    //                           value.contains(
                                    //                               '@'))
                                    //                       ? 'Do not use the @ char.'
                                    //                       : null;
                                    //                 },
                                    //               ),
                                    //             ),
                                    //             SizedBox(
                                    //               height: 10,
                                    //             ),
                                    //             SizedBox(
                                    //               width: 170,
                                    //               child: TextFormField(
                                    //                 obscureText: true,
                                    //                 decoration:
                                    //                     const InputDecoration(
                                    //                   contentPadding:
                                    //                       EdgeInsets.only(
                                    //                           top: 50),
                                    //                   label: Text(
                                    //                     'Confirm Email',
                                    //                     style: TextStyle(
                                    //                       fontSize: 18,
                                    //                       color:
                                    //                           Color(0xff26204A),
                                    //                       fontWeight:
                                    //                           FontWeight.w400,
                                    //                     ),
                                    //                   ),
                                    //                 ),
                                    //               ),
                                    //             ),
                                    //             SizedBox(
                                    //               height: 30,
                                    //             ),
                                    //             ElevatedButton(
                                    //               style: ButtonStyle(
                                    //                 minimumSize:
                                    //                     WidgetStatePropertyAll(
                                    //                         Size(213, 45)),
                                    //                 backgroundColor:
                                    //                     WidgetStatePropertyAll(
                                    //                         Color(0xff584294)),
                                    //                 foregroundColor:
                                    //                     WidgetStatePropertyAll(
                                    //                         Colors.white),
                                    //               ),
                                    //               onPressed: () {
                                    //                 // Validate returns true if the form is valid, or false otherwise.
                                    //                 if (_formKey.currentState!
                                    //                     .validate()) {
                                    //                   // If the form is valid, display a snackbar. In the real world,
                                    //                   // you'd often call a server or save the information in a database.
                                    //                   ScaffoldMessenger.of(
                                    //                           context)
                                    //                       .showSnackBar(
                                    //                     const SnackBar(
                                    //                         content: Text(
                                    //                             'Processing Data')),
                                    //                   );
                                    //                 }
                                    //               },
                                    //               child: const Text(
                                    //                 'Reset Email',
                                    //                 style: TextStyle(
                                    //                     fontSize: 16,
                                    //                     fontWeight:
                                    //                         FontWeight.w400),
                                    //               ),
                                    //             ),
                                    //             SizedBox(
                                    //               height: 10,
                                    //             ),
                                    //           ],
                                    //         ),
                                    //       )
                                    //     ],
                                    //   ),
                                    // ),
                                    // Divider(
                                    //   thickness: 1,
                                    // ),
                                    // Theme(
                                    //   data: Theme.of(context).copyWith(
                                    //       dividerColor: Colors.transparent),
                                    //   child: ExpansionTile(
                                    //     title: Align(
                                    //       alignment: Alignment(-0.60, 0),
                                    //       child: Text(
                                    //         'Change Password',
                                    //         style: Theme.of(context)
                                    //             .textTheme
                                    //             .bodyMedium!
                                    //             .copyWith(
                                    //                 fontSize: 18,
                                    //                 color: Color(0xff26204a),
                                    //                 fontWeight:
                                    //                     FontWeight.w400),
                                    //       ),
                                    //     ),
                                    //     leading: Icon(
                                    //       Icons.password,
                                    //       size: 30,
                                    //       color: Color(0xff6750A4),
                                    //     ),
                                    //     trailing: Icon(
                                    //       Icons.keyboard_arrow_down_rounded,
                                    //       size: 50,
                                    //       color: Color(0xff6750A4),
                                    //     ),
                                    //     tilePadding:
                                    //         EdgeInsets.only(left: 10, right: 4),
                                    //     children: [
                                    //       Form(
                                    //           key: _formKey,
                                    //           child: Column(
                                    //             children: [
                                    //               SizedBox(
                                    //                 width: 170,
                                    //                 child: TextFormField(
                                    //                   obscureText: true,
                                    //                   decoration:
                                    //                       const InputDecoration(
                                    //                     contentPadding:
                                    //                         EdgeInsets.only(
                                    //                             top: 50),
                                    //                     label: Text(
                                    //                       'New Password',
                                    //                       style: TextStyle(
                                    //                         fontSize: 18,
                                    //                         color: Color(
                                    //                             0xff26204A),
                                    //                         fontWeight:
                                    //                             FontWeight.w400,
                                    //                       ),
                                    //                     ),
                                    //                   ),
                                    //                   onSaved:
                                    //                       (String? value) {},
                                    //                   validator:
                                    //                       (String? value) {
                                    //                     return (value != null &&
                                    //                             value.contains(
                                    //                                 '@'))
                                    //                         ? 'Do not use the @ char.'
                                    //                         : null;
                                    //                   },
                                    //                 ),
                                    //               ),
                                    //               SizedBox(
                                    //                 height: 10,
                                    //               ),
                                    //               SizedBox(
                                    //                 width: 170,
                                    //                 child: TextFormField(
                                    //                   obscureText: true,
                                    //                   decoration:
                                    //                       const InputDecoration(
                                    //                     label: Text(
                                    //                       'Confirm Password',
                                    //                       style: TextStyle(
                                    //                         fontSize: 18,
                                    //                         color: Color(
                                    //                             0xff26204A),
                                    //                         fontWeight:
                                    //                             FontWeight.w400,
                                    //                       ),
                                    //                     ),
                                    //                     contentPadding:
                                    //                         EdgeInsets.only(
                                    //                             top: 50),
                                    //                   ),
                                    //                 ),
                                    //               ),
                                    //               SizedBox(
                                    //                 height: 30,
                                    //               ),
                                    //               ElevatedButton(
                                    //                 style: ButtonStyle(
                                    //                   minimumSize:
                                    //                       WidgetStatePropertyAll(
                                    //                           Size(213, 45)),
                                    //                   backgroundColor:
                                    //                       WidgetStatePropertyAll(
                                    //                           Color(
                                    //                               0xff584294)),
                                    //                   foregroundColor:
                                    //                       WidgetStatePropertyAll(
                                    //                           Colors.white),
                                    //                 ),
                                    //                 onPressed: () {
                                    //                   if (_formKey.currentState!
                                    //                       .validate()) {
                                    //                     ScaffoldMessenger.of(
                                    //                             context)
                                    //                         .showSnackBar(
                                    //                       const SnackBar(
                                    //                           content: Text(
                                    //                               'Processing Data')),
                                    //                     );
                                    //                   }
                                    //                 },
                                    //                 child: Text(
                                    //                   'Reset Password',
                                    //                   style: TextStyle(
                                    //                       fontSize: 16,
                                    //                       fontWeight:
                                    //                           FontWeight.w400),
                                    //                 ),
                                    //               ),
                                    //               SizedBox(
                                    //                 height: 10,
                                    //               ),
                                    //             ],
                                    //           )),
                                    //     ],
                                    //   ),
                                    // ),

                                   state.accountDetails.providerData?.first.providerId=="password"? Column(
                                      children: [
                                        Theme(
                                          data: Theme.of(context).copyWith(
                                              dividerColor: Colors.transparent),
                                          child: ListTile(
                                            onTap: (){
                                              String? userEmail=state.accountDetails.userEmail;
                                              showDialog(
                                                 builder: (BuildContext context) {
                                                   return AlertDialog(
                                                     title: Text('Do you want to change your password?', style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 24, color: Color(0xff26204a), fontWeight: FontWeight.w600)),

                                                     content: Text('password change email will be sent your account', style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 18, color: Color(0xff26204a), fontWeight: FontWeight.w400)),

                                                     actions: [
                                                       TextButton(onPressed: () {
                                                         Navigator.of(context).pop();
                                                       }, child: Text('Cancel')),
                                                       TextButton(onPressed: () {
                                                         print('confirm:$userEmail');
                                                          context.read<SignInBloc>().add(SignInEvent.forgotPassword(userEmail!));

                                                         Navigator.of(context).pop();
                                                       }, child: Text('Confirm')),
                                                     ],
                                                   );
                                                 }, context: context
                                              );
                                            },
                                            title: Align(
                                              alignment: Alignment(-0.60, 0),
                                              child: Text(
                                                'Change Password',
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium!
                                                    .copyWith(
                                                        fontSize: 18,
                                                        color: Color(0xff26204a),
                                                        fontWeight:
                                                            FontWeight.w400),
                                              ),
                                            ),
                                            leading: Icon(
                                              Icons.password,
                                              size: 30,
                                              color: Color(0xff6750A4),
                                            ),)),
                                        Divider(
                                          thickness: 1,
                                        ),
                                      ],
                                    ):Container(),
                                    Theme(
                                      data: Theme.of(context).copyWith(
                                          dividerColor: Colors.transparent),
                                      child: ExpansionTile(
                                        title: Align(
                                          alignment: Alignment(-0.70, 0),
                                          child: Text(
                                            'Change Language',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium!
                                                .copyWith(
                                                    fontSize: 18,
                                                    color: Color(0xff26204a),
                                                    fontWeight:
                                                        FontWeight.w400),
                                          ),
                                        ),
                                        leading: Icon(
                                          Icons.language,
                                          size: 30,
                                          color: Color(0xff6750A4),
                                        ),
                                        trailing: Icon(
                                          Icons.keyboard_arrow_down_rounded,
                                          size: 50,
                                          color: Color(0xff6750A4),
                                        ),
                                        tilePadding:
                                            EdgeInsets.only(left: 10, right: 4),
                                        children: [
                                          Form(
                                            child: Column(
                                              children: [
                                                SizedBox(
                                                  width: 215,
                                                  child: RadioListTile(
                                                      title: Text(
                                                        'English',
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium!
                                                            .copyWith(
                                                                fontSize: 18,
                                                                color: Color(
                                                                    0xff26204a),
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400),
                                                      ),
                                                      value: languages[0],
                                                      controlAffinity:
                                                          ListTileControlAffinity
                                                              .trailing,
                                                      groupValue:
                                                          currentLanguage,
                                                      onChanged: (value) {
                                                        setState(() {
                                                          currentLanguage =
                                                              value.toString();
                                                        });
                                                      }),
                                                ),
                                                SizedBox(
                                                    width: 215,
                                                    child: RadioListTile(
                                                        title: Text(
                                                          'French',
                                                          style: Theme.of(
                                                                  context)
                                                              .textTheme
                                                              .bodyMedium!
                                                              .copyWith(
                                                                  fontSize: 18,
                                                                  color: Color(
                                                                      0xff26204a),
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400),
                                                        ),
                                                        value: languages[1],
                                                        controlAffinity:
                                                            ListTileControlAffinity
                                                                .trailing,
                                                        groupValue:
                                                            currentLanguage,
                                                        onChanged: (value) {
                                                          setState(() {
                                                            currentLanguage =
                                                                value
                                                                    .toString();
                                                          });
                                                        })),
                                                SizedBox(
                                                  height: 10,
                                                ),
                                                ElevatedButton(
                                                  style: ButtonStyle(
                                                    minimumSize:
                                                        WidgetStatePropertyAll(
                                                            Size(213, 45)),
                                                    backgroundColor:
                                                        WidgetStatePropertyAll(
                                                            Color(0xff584294)),
                                                    foregroundColor:
                                                        WidgetStatePropertyAll(
                                                            Colors.white),
                                                  ),
                                                  onPressed: () {},
                                                  child: Text(
                                                    'Confirm Language',
                                                    style: TextStyle(
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.w400),
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 10,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Divider(
                                      thickness: 1,
                                    ),
                            Theme(
                              data: Theme.of(context).copyWith(
                                  dividerColor: Colors.transparent),
                              child: ListTile(
                                onTap: (){
                                  showDialog(
                                     builder: (BuildContext context) {
                                       return AlertDialog(
                                         title: Text('Logout', style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 24, color: Color(0xff26204a), fontWeight: FontWeight.w600)),
                                         content: Text('Are you sure you want to logout?', style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 18, color: Color(0xff26204a), fontWeight: FontWeight.w400)),
                                         actions: [
                                           TextButton(onPressed: () async{

                                             context.read<AuthBloc>().add(const AuthEvent.signedOut());
                                             context.router.replaceAll([const WelcomeRoute()]);
                                           }, child: Text('Yes')),
                                           TextButton(onPressed: () {
                                             Navigator.of(context).pop();
                                           }, child: Text('No')),
                                         ],
                                       );
                                     }, context: context
                                  );
                                 
                                },
                                leading: Icon(Icons.logout ,size: 30,
                                  color: Color(0xff6750A4),),
                                title: Text(
                                  'Logout',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                          fontSize: 18,
                                          color: Color(0xff26204a),
                                          fontWeight: FontWeight.w400),
                                ),
                              )),
                                    SizedBox(
                                      height: 10,
                                    ),
                                  ],
                                ),
                              ))
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
        },
        loadFailure: (_) => Scaffold(
          appBar: CurvedAppBar(
            appBarColor: AppTheme.primaryColor,
            logoColor: Color(0xffFAF2DF),
            height: .35.sw, // The height of your curved app bar
          ),
          body: Center(
            child: CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
          ),
        ),
      );
    }),
),
);
  }
}
