// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:account_management/application/therapy_management_bloc/therapy_management_bloc.dart';
// import 'package:timezone/timezone.dart' as tz;
// import 'package:injectable/injectable.dart';
// import 'package:get_it/get_it.dart';
//
// import 'app_lifecycle_observer.dart';
//
// /// A service to manage therapy sessions and feedback
// @singleton
// class TherapyManager with WidgetsBindingObserver {
//   final GlobalKey<NavigatorState> navigatorKey;
//   final TherapyManagementBloc therapyBloc;
//   AppLifecycle _currentLifecycle = AppLifecycle.resumed;
//   DateTime? _therapyStartTime;
//   bool _feedbackPending = false;
//
//   TherapyManager(this.navigatorKey)
//       : therapyBloc = GetIt.instance<TherapyManagementBloc>();
//
//   /// Initialize the therapy manager
//   void initialize() {
//     final lifecycleObserver = AppLifecycleObserver((lifecycle) {
//       _handleAppLifecycleChange(lifecycle);
//     });
//     lifecycleObserver.initialize();
//   }
//
//   /// Start a therapy session and schedule feedback
//   /// [durationMinutes] is the duration of the therapy session in minutes
//   void startTherapy({int durationMinutes = 15}) {
//     _therapyStartTime = DateTime.now();
//     _feedbackPending = true;
//
//     // Trigger therapy started event in the bloc
//     therapyBloc.add(TherapyManagementEvent.therapyStart(
//     ));
//
//     print('Therapy started at $_therapyStartTime for $durationMinutes minutes');
//   }
//
//   /// Show feedback dialog directly in the app (for foreground app state)
//   void showFeedbackDialog() {
//     if (_feedbackPending && navigatorKey.currentContext != null) {
//       final context = navigatorKey.currentContext!;
//
//       // Request feedback
//       therapyBloc.add(const TherapyManagementEvent.feedbackRequested());
//
//       // Navigate to the feedback page
//       Navigator.of(context).pushNamed('/therapy_feedback');
//
//       // Reset the pending feedback flag
//       _feedbackPending = false;
//     }
//   }
//
//   /// Handle app lifecycle changes to trigger feedback at appropriate times
//   void _handleAppLifecycleChange(AppLifecycle lifecycle) {
//     final previousState = _currentLifecycle;
//     _currentLifecycle = lifecycle;
//
//     // Therapy was in progress, app went to background, and now returned to foreground
//     if (_feedbackPending &&
//         previousState == AppLifecycle.paused &&
//         lifecycle == AppLifecycle.resumed) {
//
//       // Check if it's time to show feedback
//       if (_therapyStartTime != null) {
//         final now = DateTime.now();
//         final timeElapsed = now.difference(_therapyStartTime!).inMinutes;
//
//         // If enough time has passed since therapy started, show feedback
//         if (timeElapsed >= 15) { // Default therapy duration
//           showFeedbackDialog();
//         }
//       }
//     }
//   }
// }