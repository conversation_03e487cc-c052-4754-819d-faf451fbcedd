import 'package:flutter/material.dart';

/// Enum representing the app lifecycle states we track
enum AppLifecycle { resumed, inactive, paused, detached, hidden }

/// An observer that tracks Flutter application lifecycle changes
class AppLifecycleObserver with WidgetsBindingObserver {
  final Function(AppLifecycle) _onLifecycleChanged;

  AppLifecycleObserver(this._onLifecycleChanged);

  /// Initialize the observer
  void initialize() {
    WidgetsBinding.instance.addObserver(this);
  }

  /// Dispose the observer
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _onLifecycleChanged(AppLifecycle.resumed);
        break;
      case AppLifecycleState.inactive:
        _onLifecycleChanged(AppLifecycle.inactive);
        break;
      case AppLifecycleState.paused:
        _onLifecycleChanged(AppLifecycle.paused);
        break;
      case AppLifecycleState.detached:
        _onLifecycleChanged(AppLifecycle.detached);
        break;
      case AppLifecycleState.hidden:
        _onLifecycleChanged(AppLifecycle.hidden);
        break;
    }
  }
}
