// import 'dart:async';
// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:account_management/application/therapy_management_bloc/therapy_management_bloc.dart';
// import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:timezone/timezone.dart' as tz;
// import 'package:workmanager/workmanager.dart';
//
// import '../widgets/therapy_feedback_dialog.dart';
//
// class TherapyFeedbackService {
//   static const THERAPY_FEEDBACK_TASK = "com.junoplus.therapy.feedback";
//   static const THERAPY_NOTIFICATION_TYPE = "therapy";
//   static const FEEDBACK_PAYLOAD = "open-feedback-dialog";
//   static const FEEDBACK_NOTIFICATION_ID = "therapy_feedback";
//   static const FEEDBACK_CHANNEL = "therapy_feedback_channel";
//
//   final ScheduledNotificationsFacade _notificationsFacade;
//   final GlobalKey<NavigatorState> navigatorKey;
//
//   TherapyFeedbackService(this._notificationsFacade, this.navigatorKey);
//
//   /// Initialize the feedback service
//   Future<void> initialize() async {
//     if (Platform.isAndroid) {
//       await Workmanager().initialize(
//         callbackDispatcher,
//         isInDebugMode: true,
//       );
//     }
//   }
//
//   /// Schedule feedback notification after therapy session
//   Future<void> scheduleTherapyFeedback({
//     required String therapyId,
//     required Duration afterDuration,
//   }) async {
//     // Save therapy ID in SharedPreferences for retrieval when notification is clicked
//     final prefs = await SharedPreferences.getInstance();
//     prefs.setString('last_therapy_id', therapyId);
//
//     // Schedule notification after specified duration
//     await _notificationsFacade.scheduleSingleNotification(
//       body: "Please provide feedback on your therapy session",
//       dateTime: tz.TZDateTime.now(tz.local).add(afterDuration),
//       notificationId: FEEDBACK_NOTIFICATION_ID,
//       notificationType: THERAPY_NOTIFICATION_TYPE,
//       payload: FEEDBACK_PAYLOAD,
//       isForeground:
//     );
//
//     // Also register background task for Android (fallback)
//     if (Platform.isAndroid) {
//       await Workmanager().registerOneOffTask(
//         THERAPY_FEEDBACK_TASK,
//         THERAPY_FEEDBACK_TASK,
//         initialDelay: afterDuration,
//         constraints: Constraints(
//           networkType: NetworkType.not_required,
//           // batteryNotLow: false,
//         ),
//         existingWorkPolicy: ExistingWorkPolicy.replace,
//       );
//     }
//   }
//
//   /// Handle a therapy feedback notification being tapped
//   Future<void> handleFeedbackNotificationTap() async {
//     // Get the therapy ID from SharedPreferences
//     final prefs = await SharedPreferences.getInstance();
//     final therapyId = prefs.getString('last_therapy_id') ?? 'unknown';
//
//     showFeedbackDialog(therapyId);
//   }
//
//   /// Show feedback dialog in foreground app
//   void showFeedbackDialog(String therapyId) {
//     // Check if navigator key is attached and has context
//     if (navigatorKey.currentContext != null) {
//       // Get the current context
//       final context = navigatorKey.currentContext!;
//
//       // Show dialog
//       showDialog(
//         context: context,
//         barrierDismissible: false,
//         builder: (context) => BlocProvider.value(
//           value: BlocProvider.of<TherapyManagementBloc>(context),
//           child: TherapyFeedbackDialog(therapyId: therapyId),
//         ),
//       );
//     }
//   }
// }
//
// // The background worker callback for Android
// @pragma('vm:entry-point')
// void callbackDispatcher() {
//   Workmanager().executeTask((task, inputData) async {
//     if (task == TherapyFeedbackService.THERAPY_FEEDBACK_TASK) {
//       // When triggered in background, show a notification
//       final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//           FlutterLocalNotificationsPlugin();
//
//       const AndroidNotificationDetails androidDetails =
//           AndroidNotificationDetails(
//         'therapy_feedback_channel',
//         'Therapy Feedback',
//         channelDescription: 'Notifications related to therapy feedback',
//         importance: Importance.high,
//         priority: Priority.high,
//       );
//
//       const NotificationDetails platformDetails = NotificationDetails(
//         android: androidDetails,
//         iOS: DarwinNotificationDetails(
//           presentAlert: true,
//           presentBadge: true,
//           presentSound: true,
//         ),
//       );
//
//       await flutterLocalNotificationsPlugin.show(
//         0,
//         'Therapy Session Feedback',
//         'How was your therapy session? Tap to provide feedback.',
//         platformDetails,
//         payload: TherapyFeedbackService.FEEDBACK_PAYLOAD,
//       );
//     }
//     return true;
//   });
// }
