import UIKit
import Flutter
import CoreBluetooth
// This is required for calling FlutterLocalNotificationsPlugin.setPluginRegistrantCallback method.
import flutter_local_notifications
@main
@objc class AppDelegate: FlutterAppDelegate, CBCentralManagerDelegate {
  var centralManager: CBCentralManager?

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
   UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
   //This is required to make any communication available in the action isolate.
    FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
      GeneratedPluginRegistrant.register(with: registry)
    }
    if #available(iOS 10.0, *) {
          UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
    }
    GeneratedPluginRegistrant.register(with: self)

    let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
    let bluetoothChannel = FlutterMethodChannel(name: "samples.flutter.dev/bluetooth",
                                                binaryMessenger: controller.binaryMessenger)
    bluetoothChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
      if call.method == "startBluetoothMonitoring" {
        self.startBluetoothMonitoring()
        result(nil)
      } else if call.method == "stopBluetoothMonitoring" {
        self.stopBluetoothMonitoring()
        result(nil)
      } else if call.method == "openBluetoothSettings" {
        self.showBluetoothPowerAlert()
        result(nil)
      } else {
        result(FlutterMethodNotImplemented)
      }
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  func centralManagerDidUpdateState(_ central: CBCentralManager) {
    if central.state == .poweredOff {
      // Optionally handle Bluetooth turned off state
    }
  }

  private func startBluetoothMonitoring() {
    centralManager = CBCentralManager(delegate: self, queue: nil)
  }

  private func stopBluetoothMonitoring() {
    centralManager = nil
  }

  private func showBluetoothPowerAlert() {
    centralManager = CBCentralManager(delegate: self, queue: nil, options: [CBCentralManagerOptionShowPowerAlertKey: true])
  }
}
