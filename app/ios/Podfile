# Uncomment this line to define a global platform for your project
platform :ios, '15.0'  # Updated to iOS 15 for Firebase 11.x compatibility

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

# This pre-install hook fixes issues with Firebase and Google Sign In dependencies
pre_install do |installer|
  # workaround for https://github.com/CocoaPods/CocoaPods/issues/3289
  Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}
end

# Force Firebase 11.x compatible versions
# pod 'GoogleSignIn', '~> 8.0'  # Now compatible with Firebase 11.x
# pod 'AppCheckCore', '~> 11.0'  # Firebase 11.x version

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  target 'RunnerUITests' do
    inherit! :complete
    pod 'CocoaAsyncSocket'
  end

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    if target.name == 'BoringSSL-GRPC'
      target.source_build_phase.files.each do |file|
        if file.settings && file.settings['COMPILER_FLAGS']
          flags = file.settings['COMPILER_FLAGS'].split
          flags.reject! { |flag| flag == '-GCC_WARN_INHIBIT_ALL_WARNINGS' }
          file.settings['COMPILER_FLAGS'] = flags.join(' ')
        end
      end
    end

    target.build_configurations.each do |config|
      # Force consistent deployment target across all configurations
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'

      # This allows for mixing static and dynamic frameworks despite using frameworks
      config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
      config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
      config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
    end
  end

  # Handle the main project's deployment target inconsistencies
  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
      end
    end
  end

  # Call flutter_additional_ios_build_settings after fixing deployment targets
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
  end
end