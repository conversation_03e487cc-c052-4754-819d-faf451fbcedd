PODS:
  - abseil/algorithm (1.20240722.0):
    - abseil/algorithm/algorithm (= 1.20240722.0)
    - abseil/algorithm/container (= 1.20240722.0)
  - abseil/algorithm/algorithm (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.20240722.0):
    - abseil/base/atomic_hook (= 1.20240722.0)
    - abseil/base/base (= 1.20240722.0)
    - abseil/base/base_internal (= 1.20240722.0)
    - abseil/base/config (= 1.20240722.0)
    - abseil/base/core_headers (= 1.20240722.0)
    - abseil/base/cycleclock_internal (= 1.20240722.0)
    - abseil/base/dynamic_annotations (= 1.20240722.0)
    - abseil/base/endian (= 1.20240722.0)
    - abseil/base/errno_saver (= 1.20240722.0)
    - abseil/base/fast_type_id (= 1.20240722.0)
    - abseil/base/log_severity (= 1.20240722.0)
    - abseil/base/malloc_internal (= 1.20240722.0)
    - abseil/base/no_destructor (= 1.20240722.0)
    - abseil/base/nullability (= 1.20240722.0)
    - abseil/base/poison (= 1.20240722.0)
    - abseil/base/prefetch (= 1.20240722.0)
    - abseil/base/pretty_function (= 1.20240722.0)
    - abseil/base/raw_logging_internal (= 1.20240722.0)
    - abseil/base/spinlock_wait (= 1.20240722.0)
    - abseil/base/strerror (= 1.20240722.0)
    - abseil/base/throw_delegate (= 1.20240722.0)
  - abseil/base/atomic_hook (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.20240722.0):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/nullability (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/poison (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/xcprivacy
  - abseil/base/prefetch (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.20240722.0):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.20240722.0):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.20240722.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.20240722.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_container_defaults
    - abseil/container/raw_hash_map
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.20240722.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_container_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hash_container_defaults (1.20240722.0):
    - abseil/base/config
    - abseil/container/hash_function_defaults
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.20240722.0):
    - abseil/base/config
    - abseil/container/common
    - abseil/hash/hash
    - abseil/meta/type_traits
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.20240722.0):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.20240722.0):
    - abseil/base/config
    - abseil/base/no_destructor
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/bounded_utf8_length_sequence (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/decode_rust_punycode (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/debugging/bounded_utf8_length_sequence
    - abseil/debugging/utf8_for_code_point
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/debugging/demangle_rust
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/debugging/demangle_rust (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/decode_rust_punycode
    - abseil/xcprivacy
  - abseil/debugging/examine_stack (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/debugging/utf8_for_code_point (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/commandlineflag
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.20240722.0):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.20240722.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.20240722.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/log/absl_check (1.20240722.0):
    - abseil/log/internal/check_impl
    - abseil/xcprivacy
  - abseil/log/absl_log (1.20240722.0):
    - abseil/log/internal/log_impl
    - abseil/xcprivacy
  - abseil/log/absl_vlog_is_on (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/check (1.20240722.0):
    - abseil/log/internal/check_impl
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/globals (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/hash/hash
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/append_truncated (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/check_impl (1.20240722.0):
    - abseil/base/core_headers
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/check_op (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/nullguard
    - abseil/log/internal/nullstream
    - abseil/log/internal/strip
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/conditions (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/voidify
    - abseil/xcprivacy
  - abseil/log/internal/config (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/fnmatch (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/format (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/append_truncated
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/globals (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/strings/strings
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/log/internal/log_impl (1.20240722.0):
    - abseil/log/absl_vlog_is_on
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/log_message (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/examine_stack
    - abseil/log/globals
    - abseil/log/internal/append_truncated
    - abseil/log/internal/format
    - abseil/log/internal/globals
    - abseil/log/internal/log_sink_set
    - abseil/log/internal/nullguard
    - abseil/log/internal/proto
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/log/log_sink_registry
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/log_sink_set (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/cleanup/cleanup
    - abseil/log/globals
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/nullguard (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/nullstream (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/proto (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/strip (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/log_message
    - abseil/log/internal/nullstream
    - abseil/xcprivacy
  - abseil/log/internal/vlog_config (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/log/internal/fnmatch
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/log/internal/voidify (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/log/log (1.20240722.0):
    - abseil/log/internal/log_impl
    - abseil/log/vlog_is_on
    - abseil/xcprivacy
  - abseil/log/log_entry (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/config
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/log_sink (1.20240722.0):
    - abseil/base/config
    - abseil/log/log_entry
    - abseil/xcprivacy
  - abseil/log/log_sink_registry (1.20240722.0):
    - abseil/base/config
    - abseil/log/internal/log_sink_set
    - abseil/log/log_sink
    - abseil/xcprivacy
  - abseil/log/vlog_is_on (1.20240722.0):
    - abseil/log/absl_vlog_is_on
    - abseil/xcprivacy
  - abseil/memory (1.20240722.0):
    - abseil/memory/memory (= 1.20240722.0)
  - abseil/memory/memory (1.20240722.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.20240722.0):
    - abseil/meta/type_traits (= 1.20240722.0)
  - abseil/meta/type_traits (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/types/compare
    - abseil/xcprivacy
  - abseil/numeric/representation (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.20240722.0):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.20240722.0):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.20240722.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.20240722.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.20240722.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.20240722.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.20240722.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.20240722.0):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/compare
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.20240722.0):
    - abseil/base/config
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.20240722.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.20240722.0):
    - abseil/time/internal (= 1.20240722.0)
    - abseil/time/time (= 1.20240722.0)
  - abseil/time/internal (1.20240722.0):
    - abseil/time/internal/cctz (= 1.20240722.0)
  - abseil/time/internal/cctz (1.20240722.0):
    - abseil/time/internal/cctz/civil_time (= 1.20240722.0)
    - abseil/time/internal/cctz/time_zone (= 1.20240722.0)
  - abseil/time/internal/cctz/civil_time (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.20240722.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.20240722.0):
    - abseil/types/any (= 1.20240722.0)
    - abseil/types/bad_any_cast (= 1.20240722.0)
    - abseil/types/bad_any_cast_impl (= 1.20240722.0)
    - abseil/types/bad_optional_access (= 1.20240722.0)
    - abseil/types/bad_variant_access (= 1.20240722.0)
    - abseil/types/compare (= 1.20240722.0)
    - abseil/types/optional (= 1.20240722.0)
    - abseil/types/span (= 1.20240722.0)
    - abseil/types/variant (= 1.20240722.0)
  - abseil/types/any (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.20240722.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.20240722.0)
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - BoringSSL-GRPC (0.0.37):
    - BoringSSL-GRPC/Implementation (= 0.0.37)
    - BoringSSL-GRPC/Interface (= 0.0.37)
  - BoringSSL-GRPC/Implementation (0.0.37):
    - BoringSSL-GRPC/Interface (= 0.0.37)
  - BoringSSL-GRPC/Interface (0.0.37)
  - cloud_firestore (5.6.8):
    - Firebase/Firestore (= 11.10.0)
    - firebase_core
    - Flutter
  - CocoaAsyncSocket (7.6.5)
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (11.10.0):
    - Firebase/Core
  - Firebase/Auth (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.10.0)
  - Firebase/Core (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Firestore (11.10.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - Firebase/Storage (11.10.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 11.10.0)
  - firebase_analytics (11.4.6):
    - Firebase/Analytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_app_check (0.3.2-6):
    - Firebase/CoreOnly (~> 11.10.0)
    - firebase_core
    - FirebaseAppCheck (~> 11.10.0)
    - Flutter
  - firebase_auth (5.5.4):
    - Firebase/Auth (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.1):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_messaging (15.2.6):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_storage (12.4.6):
    - Firebase/Storage (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.10.0):
    - FirebaseAnalytics/AdIdSupport (= 11.10.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheck (11.10.0):
    - AppCheckCore (~> 11.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
  - FirebaseAppCheckInterop (11.13.0)
  - FirebaseAuth (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.13.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseFirestore (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseFirestoreInternal (= 11.10.0)
    - FirebaseSharedSwift (~> 11.0)
  - FirebaseFirestoreInternal (11.10.0):
    - abseil/algorithm (~> 1.20240722.0)
    - abseil/base (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/memory (~> 1.20240722.0)
    - abseil/meta (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/time (~> 1.20240722.0)
    - abseil/types (~> 1.20240722.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - "gRPC-C++ (~> 1.69.0)"
    - gRPC-Core (~> 1.69.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseSharedSwift (11.13.0)
  - FirebaseStorage (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - Flutter (1.0.0)
  - flutter_blue_plus (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.10.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.10.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.69.0)":
    - "gRPC-C++/Implementation (= 1.69.0)"
    - "gRPC-C++/Interface (= 1.69.0)"
  - "gRPC-C++/Implementation (1.69.0)":
    - abseil/algorithm/container (~> 1.20240722.0)
    - abseil/base/base (~> 1.20240722.0)
    - abseil/base/config (~> 1.20240722.0)
    - abseil/base/core_headers (~> 1.20240722.0)
    - abseil/base/log_severity (~> 1.20240722.0)
    - abseil/base/no_destructor (~> 1.20240722.0)
    - abseil/cleanup/cleanup (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/container/flat_hash_set (~> 1.20240722.0)
    - abseil/container/inlined_vector (~> 1.20240722.0)
    - abseil/flags/flag (~> 1.20240722.0)
    - abseil/flags/marshalling (~> 1.20240722.0)
    - abseil/functional/any_invocable (~> 1.20240722.0)
    - abseil/functional/bind_front (~> 1.20240722.0)
    - abseil/functional/function_ref (~> 1.20240722.0)
    - abseil/hash/hash (~> 1.20240722.0)
    - abseil/log/absl_check (~> 1.20240722.0)
    - abseil/log/absl_log (~> 1.20240722.0)
    - abseil/log/check (~> 1.20240722.0)
    - abseil/log/globals (~> 1.20240722.0)
    - abseil/log/log (~> 1.20240722.0)
    - abseil/memory/memory (~> 1.20240722.0)
    - abseil/meta/type_traits (~> 1.20240722.0)
    - abseil/numeric/bits (~> 1.20240722.0)
    - abseil/random/bit_gen_ref (~> 1.20240722.0)
    - abseil/random/distributions (~> 1.20240722.0)
    - abseil/random/random (~> 1.20240722.0)
    - abseil/status/status (~> 1.20240722.0)
    - abseil/status/statusor (~> 1.20240722.0)
    - abseil/strings/cord (~> 1.20240722.0)
    - abseil/strings/str_format (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/synchronization/synchronization (~> 1.20240722.0)
    - abseil/time/time (~> 1.20240722.0)
    - abseil/types/optional (~> 1.20240722.0)
    - abseil/types/span (~> 1.20240722.0)
    - abseil/types/variant (~> 1.20240722.0)
    - abseil/utility/utility (~> 1.20240722.0)
    - "gRPC-C++/Interface (= 1.69.0)"
    - "gRPC-C++/Privacy (= 1.69.0)"
    - gRPC-Core (= 1.69.0)
  - "gRPC-C++/Interface (1.69.0)"
  - "gRPC-C++/Privacy (1.69.0)"
  - gRPC-Core (1.69.0):
    - gRPC-Core/Implementation (= 1.69.0)
    - gRPC-Core/Interface (= 1.69.0)
  - gRPC-Core/Implementation (1.69.0):
    - abseil/algorithm/container (~> 1.20240722.0)
    - abseil/base/base (~> 1.20240722.0)
    - abseil/base/config (~> 1.20240722.0)
    - abseil/base/core_headers (~> 1.20240722.0)
    - abseil/base/log_severity (~> 1.20240722.0)
    - abseil/base/no_destructor (~> 1.20240722.0)
    - abseil/cleanup/cleanup (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/container/flat_hash_set (~> 1.20240722.0)
    - abseil/container/inlined_vector (~> 1.20240722.0)
    - abseil/flags/flag (~> 1.20240722.0)
    - abseil/flags/marshalling (~> 1.20240722.0)
    - abseil/functional/any_invocable (~> 1.20240722.0)
    - abseil/functional/bind_front (~> 1.20240722.0)
    - abseil/functional/function_ref (~> 1.20240722.0)
    - abseil/hash/hash (~> 1.20240722.0)
    - abseil/log/check (~> 1.20240722.0)
    - abseil/log/globals (~> 1.20240722.0)
    - abseil/log/log (~> 1.20240722.0)
    - abseil/memory/memory (~> 1.20240722.0)
    - abseil/meta/type_traits (~> 1.20240722.0)
    - abseil/numeric/bits (~> 1.20240722.0)
    - abseil/random/bit_gen_ref (~> 1.20240722.0)
    - abseil/random/distributions (~> 1.20240722.0)
    - abseil/random/random (~> 1.20240722.0)
    - abseil/status/status (~> 1.20240722.0)
    - abseil/status/statusor (~> 1.20240722.0)
    - abseil/strings/cord (~> 1.20240722.0)
    - abseil/strings/str_format (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/synchronization/synchronization (~> 1.20240722.0)
    - abseil/time/time (~> 1.20240722.0)
    - abseil/types/optional (~> 1.20240722.0)
    - abseil/types/span (~> 1.20240722.0)
    - abseil/types/variant (~> 1.20240722.0)
    - abseil/utility/utility (~> 1.20240722.0)
    - BoringSSL-GRPC (= 0.0.37)
    - gRPC-Core/Interface (= 1.69.0)
    - gRPC-Core/Privacy (= 1.69.0)
  - gRPC-Core/Interface (1.69.0)
  - gRPC-Core/Privacy (1.69.0)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - leveldb-library (1.22.6)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - patrol (0.0.1):
    - CocoaAsyncSocket (~> 7.6)
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (101.0.0)
  - rive_common (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - CocoaAsyncSocket
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus (from `.symlinks/plugins/flutter_blue_plus/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - patrol (from `.symlinks/plugins/patrol/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - rive_common (from `.symlinks/plugins/rive_common/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - AppCheckCore
    - BoringSSL-GRPC
    - CocoaAsyncSocket
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSharedSwift
    - FirebaseStorage
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - RecaptchaInterop

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus:
    :path: ".symlinks/plugins/flutter_blue_plus/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  patrol:
    :path: ".symlinks/plugins/patrol/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  rive_common:
    :path: ".symlinks/plugins/rive_common/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  abseil: a05cc83bf02079535e17169a73c5be5ba47f714b
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  BoringSSL-GRPC: dded2a44897e45f28f08ae87a55ee4bcd19bc508
  cloud_firestore: 418c4911a2e4f1c38ac6dbb838ef19cdc32a515f
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_analytics: 943aa4699bf6afe056a96991ba8ee34776323d4c
  firebase_app_check: a3fad167381a08d4e9534da64429c9b648e51f24
  firebase_auth: 519455ee22dea9cb8b4e05d7821f93f77db88882
  firebase_core: 3c2f323cae65c97a636a05a23b17730ef93df2cf
  firebase_messaging: 456e01ff29a451c90097d0b45925551d5be0c143
  firebase_storage: a53140c25885d8de77fecf98cbaa0d9fff501598
  FirebaseAnalytics: 4e42333f02cf78ed93703a5c36f36dd518aebdef
  FirebaseAppCheck: 9687ebd909702469bc09d2d58008697b83f4ac27
  FirebaseAppCheckInterop: 72066489c209823649a997132bcd9269bc33a4bb
  FirebaseAuth: c4146bdfdc87329f9962babd24dae89373f49a32
  FirebaseAuthInterop: 4fa327ec3c551a80a6929561f83af80b1dd44937
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseFirestore: 3f1488ff7739cb3c5d10e572bc4e9fcd8e8cb4ac
  FirebaseFirestoreInternal: 97a2bb5f16951c77753c860d3519379702ab6f8a
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  FirebaseSharedSwift: aca73668bc95e8efccb618e0167eab05d19d3a75
  FirebaseStorage: e83d1b9c8a5318d46ccfb2955f0d98095e0bf598
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue_plus: 4837da7d00cf5d441fdd6635b3a57f936778ea96
  flutter_local_notifications: ff50f8405aaa0ccdc7dcfb9022ca192e8ad9688f
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  google_sign_in_ios: 07375bfbf2620bc93a602c0e27160d6afc6ead38
  GoogleAppMeasurement: 36684bfb3ee034e2b42b4321eb19da3a1b81e65d
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  "gRPC-C++": cc207623316fb041a7a3e774c252cf68a058b9e8
  gRPC-Core: 860978b7db482de8b4f5e10677216309b5ff6330
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  integration_test: 252f60fa39af5e17c3aa9899d35d908a0721b573
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  patrol: cf2cd48c7f3e5171610111994f7b466cd76d1f57
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  rive_common: 4743dbfd2911c99066547a3c6454681e0fa907df
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 76957ab028e12bfa4e66813c99e46637f367fc7e

PODFILE CHECKSUM: d41d74776ef100a743d7027a147dc83450cfc28a

COCOAPODS: 1.16.2
