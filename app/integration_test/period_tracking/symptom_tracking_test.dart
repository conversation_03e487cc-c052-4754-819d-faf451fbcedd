import 'dart:io';

import 'package:authentication/widgets/text_form_feild.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:integration_test/integration_test.dart';
import 'package:patrol/patrol.dart';
import '../create_app.dart';

void main() {
  patrolSetUp(() {
    expect(2 + 2, equals(4)); // Basic smoke test
  });


  patrolTest(
    'test1_TC_KEY_JM_606, test1_TC_KEY_JM_607, test1_TC_KEY_JM_608, test1_TC_KEY_JM_609 ',
        ($) async {
          // Setup and initial navigation
          await createApp($);
          await $.pumpAndSettle();
          // Navigate to symptom tracking page
          await $.tap(find.text("Login"));
          await $.pumpAndSettle();
          await $.enterText(find.widgetWithText(MyTextFormField, 'Email'),
              '<EMAIL>');
          await $.pumpAndSettle();
          await $.enterText(
              find.widgetWithText(MyTextFormField, 'Password'), 'abcdef');
          await $.pumpAndSettle();
          await $.pumpAndSettle();
          await $.tap(find.text("Login"));
          await $.pumpAndSettle();
          await $.pumpAndSettle();
          await Future.delayed(const Duration(seconds: 5));
          await $.pumpAndSettle();
          await $.scrollUntilVisible(finder: find.text('Continue'),);
          await $.waitUntilVisible(
            find.text('Continue'),
            timeout: Duration(seconds: 20), // Increase the duration as needed
          );
          await $.tap(find.widgetWithText(GestureDetector, 'Continue'));
          await $.pumpAndSettle();
          await $.tap(find.byKey(const Key('open_calendar_button')));
          await $.pumpAndSettle();
          await $.tap(find.byKey(const Key('add_symptoms_button')));
          await $.pumpAndSettle();

          // Verify initial state
          final emojiSlider = find.byKey(const Key('emoji_slider'));
          expect(emojiSlider, findsOneWidget);

          // Test slider interaction
          // First, find the slider's center position
          final sliderCenter = $.tester.getCenter(emojiSlider);

          // Test different slider positions
          for (var offset in [50.0, 100.0, -50.0]) {
            await $.tester.drag(
              emojiSlider,
              Offset(offset, 0.0),
              // Slower duration for more reliable testing
              // duration: const Duration(milliseconds: 500),
            );
            await $.pumpAndSettle();

            // Verify UI updates after each drag
            // You might want to verify the emoji displayed or pain level text
            expect(find.byType(Text), findsWidgets);
          }

          // Test edge cases
          // Drag to minimum value
          await $.tester.drag(
            emojiSlider,
            Offset(-200.0, 0.0),
            // duration: const Duration(milliseconds: 500),
          );
          await $.pumpAndSettle();

          // Drag to maximum value
          await $.tester.drag(
            emojiSlider,
            Offset(200.0, 0.0),
            // duration: const Duration(milliseconds: 500),
          );
          await $.pumpAndSettle();

          // Verify the pain description text updates
          expect(find.textContaining('Pain'), findsWidgets);

          // Test rapid interactions
          for (var i = 0; i < 3; i++) {
            await $.tester.drag(
              emojiSlider,
              const Offset(30.0, 0.0),
              // duration: const Duration(milliseconds: 100),
            );
            await $.pumpAndSettle();
          }
          // Assuming the first symptom is "Headache"
          final headacheSymptom = find.text('Headache');
          await $.tap(headacheSymptom);
          await $.pumpAndSettle();
          //scroll to the bottom of the screen
          await $.scrollUntilVisible(
            finder: find.byKey(const Key('flow_level_2')),
          );
          // Test flow level selection
          // Assuming you have buttons with specific keys for flow levels
          await $.tap(find.byKey(const Key('flow_level_2')));
          await $.pumpAndSettle();
          // Test save functionality
          final saveButton = find.text('Save');
          await $.tap(saveButton);
          await $.pumpAndSettle();
        }
  );


}





