

import 'dart:io';

import 'package:authentication/widgets/text_form_feild.dart';
import 'package:integration_test/integration_test.dart';
import 'package:juno_plus/app.dart';
import 'package:patrol/patrol.dart';
import 'package:flutter/material.dart';

import 'create_app.dart';

Future<void> login(PatrolIntegrationTester $) async {
  await $.tap(find.text("Login"));
  await $.pumpAndSettle();
  await $.enterText(find.widgetWithText(MyTextFormField, 'Email'), '<EMAIL>');
  await $.pumpAndSettle();
  await $.enterText(find.widgetWithText(MyTextFormField, 'Password'), 'abcdef');
  await $.pumpAndSettle();
  await $.tap(find.text("Login"));
  await $.pumpAndSettle();
  await $.pumpAndSettle();
  await Future.delayed(const Duration(seconds: 5));
  await $.pumpAndSettle();
  await $.scrollUntilVisible(finder: find.text('Continue'), );
  await $.waitUntilVisible(
    find.text('Continue'),
    timeout: Duration(seconds: 20), // Increase the duration as needed
  );
  await $.tap(find.widgetWithText(GestureDetector, 'Continue'));
  await $.pumpAndSettle();
}
void main() {
  patrolSetUp(() {
    // Smoke test for
    expect(2 + 2, equals(4));

  });

  patrolTest(
      'test1_TC_KEY_JM_423, test1_TC_KEY_JM_425, test1_TC_KEY_JM_426, test1_TC_KEY_JM_427,test1_TC_KEY_JM_429, test1_TC_KEY_JM_430, test1_TC_KEY_JM_431, test1_TC_KEY_JM_432',
          ($) async {
             await createApp($);
              await $.pumpAndSettle();
             await $.tap(find.text("Login"));
             await $.pumpAndSettle();
             await $.enterText(find.widgetWithText(MyTextFormField, 'Email'),
                 '<EMAIL>');
             await $.pumpAndSettle();
             await $.enterText(find.widgetWithText(MyTextFormField, 'Password'), 'abcdef');
             await $.pumpAndSettle();
             await $.pumpAndSettle();
             await $.tap(find.text("Login"));
             await $.pumpAndSettle();
             await $.pumpAndSettle();
             await Future.delayed(const Duration(seconds: 5));
             await $.pumpAndSettle();
             await $.scrollUntilVisible(finder: find.text('Continue'),);
             await $.waitUntilVisible(
               find.text('Continue'),
               timeout: Duration(seconds: 20), // Increase the duration as needed
             );
             await $.tap(find.widgetWithText(GestureDetector, 'Continue'));
             await $.pumpAndSettle();
              await $.tap(find.byKey(Key('remote')));
              await $.pumpAndSettle();
              await $.tap(find.byKey(Key('tens_mode_1')));
              await $.pumpAndSettle();
              await $.tap(find.byKey(Key('tens_mode_2')));
              await $.pumpAndSettle();
              await $.tap(find.byKey(Key('tens_mode_3')));
              await $.pumpAndSettle();
              await $.tap(find.byKey(Key('tens_mode_1')));

              await $.tap(find.byKey(Key('tens_increase_button')));
              await $.tap(find.byKey(Key('tens_increase_button')));
              await $.tap(find.byKey(Key('tens_increase_button')));
              await $.tap(find.byKey(Key('tens_increase_button')));
              await $.tap(find.byKey(Key('tens_increase_button')));

              await $.pumpAndSettle();
              await $.tap(find.byKey(Key('tens_decrease_button')));
              await $.tap(find.byKey(Key('tens_increase_button')));

              await $.pumpAndSettle();
              await $.tap(find.byKey(Key('back_button')));




      });

  }


