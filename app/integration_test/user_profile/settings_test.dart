import 'dart:io';
import 'dart:math';

import 'package:authentication/widgets/text_form_feild.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../create_app.dart';

void main() {
  patrolSetUp(() {
    // Smoke test for https://github.com/leancodepl/patrol/issues/2021
    expect(2 + 2, equals(4));
  });
  patrolTest(
      'Profile  My Profile test',
          ($) async {
        await createApp($);
        await $.pumpAndSettle();
        await $.pumpAndSettle();
        await $.tap(find.byKey(Key('edit_profile')));
        await $.pumpAndSettle();
        await $.tap(find.byKey(Key('edit_photo')));
        await $.pumpAndSettle();
        await $.tap(find.text('Edit'));
        await $.pumpAndSettle();
        await $.tap(find.byKey(Key('camera')));
        await $.native.pressBack();
        await $.pumpAndSettle();
        await $.tap(find.byKey(Key('gallery')));
        await $.native.pressBack();
        await $.native.tapAt(Offset(0, 0));
        await $.native.pressBack();
        await $.tap(find.byKey(Key('back_button')));
        await $.pumpAndSettle();
        await $.pumpAndSettle();
        await $.enterText(find.byKey(Key('name')), 'Test User');
        await $.pumpAndSettle();
        await $.tap(find.byKey(Key('dob')) );
        await $.pumpAndSettle();
        await $.tap(find.text('Confirm'));
        await $.pumpAndSettle();
        await $.pumpAndSettle();
        await $.tap(find.text('Save'));
        await $.tap(find.byKey(Key('back_button')));
        await $.pumpAndSettle();
      });

}