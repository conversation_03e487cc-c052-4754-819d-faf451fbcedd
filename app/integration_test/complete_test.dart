import '../integration_test/authentication/login_test.dart' as sign_in_test;
import '../integration_test/period_tracking/symptom_tracking_test.dart' as symptom_tracking_test;
import 'remote_test.dart' as remote_test;
import 'create_app.dart';

void main() {

  patrolSetUp(() {
    // Smoke test for https://github.com/leancodepl/patrol/issues/2021
    expect(2 + 2, equals(4));
  });
  sign_in_test.main();
  remote_test.main();

}


