import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:patrol/patrol.dart';
import '../create_app.dart';

void main() {
    patrolSetUp(() {
        expect(2 + 2, equals(4)); // Basic smoke test
    });

    test();

}

Future<void> test() async {
  patrolTest(
      'test1_TC_KEY_JM_605, test1_TC_KEY_JM_606, test1_TC_KEY_JM_607, test1_TC_KEY_JM_608,test1_TC_KEY_JM_609, test1_TC_KEY_JM_610, test1_TC_KEY_JM_611, test1_TC_KEY_JM_612,test1_TC_KEY_JM_613, test1_TC_KEY_JM_614, test1_TC_KEY_JM_615',
          ($) async {
        // Setup and initial navigation
        await createApp($);
        await $.pumpAndSettle();
        await $
            .tap(find.byKey(Key('medication_button'))); // Assuming a button to access the page exists
        await $
            .pumpAndSettle();
        await $
            .tap(find.byKey(Key('add_medication_button'))); // Assuming a button to access the page exists
        await $
            .pumpAndSettle();

        // Test: Add Medication Name
        await $
            .enterText(find.byKey(Key('medication_name_field')), 'Paracetamol');
        await $
            .pumpAndSettle();

        // Test: Dosage Input
        await $
            .tap(find.byKey(Key('dosage_dropdown')));

        await $
            .pumpAndSettle();
        await $
            .tap(find.text('mg'));
        await $
            .pumpAndSettle();
        await $
            .tap(find.byKey(Key('dosage_field')));
        await $.enterText(find.byKey(Key('dosage_field')), '500');

        await $
            .pumpAndSettle();

        // Test: Frequency Input
        await $
            .tap(find.byKey(Key('frequency_dropdown')));
        await $
            .pumpAndSettle();
        await $
            .tap(find.text('weekly'));

        await $
            .pumpAndSettle();
        await $
            .tap(find.byKey(Key('frequency_field')));
        await $.enterText(find.byKey(Key('frequency_field')), '2');
        await $
            .pumpAndSettle();


        // Test: Add Notes
        await $
            .enterText(find.byKey(Key('notes_field')), 'Take after meals');
        await $
            .pumpAndSettle();

        // Test: Enable Notifications
        await $
            .tap(find.byKey(Key('notification_switch')));
        await $
            .pumpAndSettle();
        await $
            .tap(find.text('Sun'));
        await $
            .pumpAndSettle();
        await $
            .tap(find.text('Mon'));
        await $
            .pumpAndSettle();
        await $
            .tap(find.text('Add Notification Time'));
        await $
            .pumpAndSettle();
        await $
            .tap(find.text('OK'));
        // Test: Add Medication Button
        await $.tap(find.byKey(Key('submit_medication_button')));
        await $.pumpAndSettle();

        // Test: Validation - Ensure Medication is Added
        expect(
          find.text('Paracetamol'),
          findsOneWidget,
        );
      }
  );
}