import 'package:authentication/widgets/text_form_feild.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:patrol/patrol.dart';
import 'package:juno_plus/main_dev.dart' as app_main;
export 'package:flutter_test/flutter_test.dart';
export 'package:patrol/patrol.dart';

final _patrolTesterConfig = PatrolTesterConfig(

);
final _nativeAutomatorConfig = NativeAutomatorConfig(
  findTimeout: Duration(seconds: 10), // 10 seconds is too short for some CIs
);

Future<void> createApp(PatrolIntegrationTester $) async {
  await app_main.main();
  await $.pumpAndSettle();

}

void patrol(
    String description,
    Future<void> Function(PatrolIntegrationTester) callback, {
      bool? skip,
      NativeAutomatorConfig? nativeAutomatorConfig,
      LiveTestWidgetsFlutterBindingFramePolicy framePolicy =
          LiveTestWidgetsFlutterBindingFramePolicy.fadePointers,
    }) {
  patrolTest(
    description,
    config: _patrolTesterConfig,
    nativeAutomatorConfig: nativeAutomatorConfig ?? _nativeAutomatorConfig,
    framePolicy: framePolicy,
    skip: skip,
    callback,
  );
}