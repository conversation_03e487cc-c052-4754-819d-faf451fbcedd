import 'dart:io';

import 'package:authentication/widgets/text_form_feild.dart';
import 'package:flutter/material.dart';
import 'package:juno_plus/app.dart';

import '../create_app.dart';

void main() {
  patrolSetUp(() {
    // Smoke test for https://github.com/leancodepl/patrol/issues/2021
    expect(2 + 2, equals(4));
  });
  patrolTest(
      'test1_TC_KEY_JM_233,test1_TC_KEY_JM_230,test1_TC_KEY_JM_232,test1_TC_KEY_JM_234',
          ($) async {
        await createApp($);
        await $.pumpAndSettle();
        await $.tap(find.text("Login"));
        await $.pumpAndSettle();
        await $.enterText(find.widgetWithText(MyTextFormField, 'Email'), '<EMAIL>');
        await $.pumpAndSettle();
        await $.enterText(find.widgetWithText(MyTextFormField, 'Password'), 'abcdef');
        await $.pumpAndSettle();
        await $.pumpAndSettle();
        await $.tap(find.text("Login"));
        await $.pumpAndSettle();
        await $.pumpAndSettle();
        await Future.delayed(const Duration(seconds: 5));
        await $.pumpAndSettle();
        await $.scrollUntilVisible(finder: find.text('Continue'), );
        await $.waitUntilVisible(
          find.text('Continue'),
          timeout: Duration(seconds: 20), // Increase the duration as needed
        );
        await $.tap(find.widgetWithText(GestureDetector, 'Continue'));
        // map this test to Test Case by Automation Test Key
       print("RTM_AUT_TEST_KEY=example.calculator.addFunction");
        // when creating Test Case from test results use summary below
        print("TC_SUMMARY=\"Calculate the sum of two numbers\"");
        // when creating Test Case from test results link it with requirement by issue key
        print("REQ_KEY=PR-109");
        await $.pumpAndSettle();
      });


  // patrolTest(
  //   'Google +',
  //       ($) async {
  //     await createApp($);
  //     await $.tap(find.text('Sign up'));
  //     await $.pumpAndSettle();
  //     await $.tap(find.text('Sign up with Google'));
  //     await $.pumpAndSettle();
  //     await $.native.tap(
  //       Selector(
  //         resourceId: 'com.google.android.gms:id/account_display_name',
  //         text: 'rovian dsouza',
  //       ),
  //     );
  //     await $.pumpAndSettle();
  //     await $.pumpAndSettle();
  //     await $.tap(find.byKey(Key('settings')));
  //     await $.scrollUntilVisible(finder: find.text('Logout'), );
  //     await $.tap(find.text('Logout'));
  //     await $.pumpAndSettle();
  //     await $.tap(find.text('Yes'));
  //     await $.pumpAndSettle();
  //     if (!Platform.isMacOS) {
  //       await $.native.pressHome();
  //     }
  //   },
  // );

}