import 'dart:io';

import 'package:authentication/widgets/text_form_feild.dart';
import 'package:flutter/material.dart';
import 'package:juno_plus/app.dart';

import '../create_app.dart';

void main() {
  patrolSetUp(() {
    // Smoke test for https://github.com/leancodepl/patrol/issues/2021
    expect(2 + 2, equals(4));
  });
  patrolTest(
    tags: ['JM-129'],
      'Sign up with <PERSON>ail',
          ($) async {
        await createApp($);
        await $.pumpAndSettle();
        await $.tap(find.text("Sign up"));
        await $.pumpAndSettle();
        await $.enterText(find.widgetWithText(MyTextFormField, 'Email'), '<EMAIL>');
        await $.pumpAndSettle();
        await $.enterText(find.widgetWithText(MyTextFormField, 'Password'), 'abcdef');
        await $.pumpAndSettle();
        await $.enterText(find.widgetWithText(MyTextFormField, 'Confirm Password'), 'abcdef');
        await $.pumpAndSettle();
        await $.pumpAndSettle();
        await $.tap(find.widgetWithText(GestureDetector, 'Sign up'));
        await $.pumpAndSettle();
        await $.pumpAndSettle();
        await $.pump(Duration(seconds: 20));
        await Future.delayed(const Duration(seconds: 20));
        await $.pumpAndSettle();
        await $.scrollUntilVisible(finder: find.text('Continue'), );
        await $.waitUntilVisible(
          find.text('Continue'),
          timeout: Duration(seconds: 20), // Increase the duration as needed
        );
        await $.tap(find.widgetWithText(GestureDetector, 'Continue'));
        await $.pumpAndSettle();
        await $.pumpAndSettle();
        await $.tap(find.byKey(Key('settings')));
        await $.scrollUntilVisible(finder: find.text('Logout'), );
        await $.tap(find.text('Logout'));
        await $.pumpAndSettle();
        await $.tap(find.text('Yes'));
        await $.pumpAndSettle();
      });
  patrolTest(
    'Google +',
        ($) async {
          await createApp($);
          await $.tap(find.text('Sign up'));
          await $.pumpAndSettle();
          await $.tap(find.text('Sign up with Google'));
          await $.pumpAndSettle();
          await $.native.tap(
            Selector(
              resourceId: 'com.google.android.gms:id/account_display_name',
              text: 'rovian dsouza',
            ),
          );
          await $.pumpAndSettle();
          await $.pumpAndSettle();
          await $.tap(find.byKey(Key('settings')));
          await $.scrollUntilVisible(finder: find.text('Logout'), );
          await $.tap(find.text('Logout'));
          await $.pumpAndSettle();
          await $.tap(find.text('Yes'));
          await $.pumpAndSettle();
      if (!Platform.isMacOS) {
        await $.native.pressHome();
      }
    },
  );

  // patrolTest(
  //   'Google -',
  //       ($) async {
  //         await createApp($);
  //         // Replace later with your app's main widget
  //         await $.pumpWidgetAndSettle(JunoPlus(env: 'Dev')
  //         );
  //         await $.tap(find.text('Login'));
  //         await $.pumpAndSettle();
  //         await $.tap(find.text('Sign up with Google'));
  //         await $.pumpAndSettle();
  //         await $.native.tap(
  //           Selector(
  //             resourceId: "com.google.android.gms:id/add_account_chip_title",
  //             text: 'Add another account',
  //           ),
  //         );
  //         await $.pumpAndSettle();
  //         await $.native.enterText(
  //           Selector(
  //             resourceId: 'identifierId', // Resource ID for email input field
  //           ),
  //           text: '<EMAIL>',
  //         );
  //          await $.pumpAndSettle();
  //
  //
  //         await $.native.tap(
  //           Selector(
  //             text: 'Next',
  //             // resourceId: 'identifierNext', // Resource ID for next button
  //           ),
  //
  //         );
  //         await $.pumpAndSettle();
  //         await $.native.enterText(
  //           Selector(
  //             resourceId: 'password', // Resource ID for password input field
  //           ),
  //           text: 'wrongpassword',
  //         );
  //         await $.pumpAndSettle();
  //
  //         await $.native.tap(
  //           Selector(
  //             resourceId: 'passwordNext', // Resource ID for next button
  //           ),
  //         );
  //         await $.pumpAndSettle();
  //
  //         // Check for the presence of an error message
  //         final errorMessage = find.text(
  //             'Wrong password. Try again or click Forgot password to reset it.'
  //         );
  //         expect(errorMessage, findsOneWidget);
  //
  //         if (!Platform.isMacOS) {
  //           await $.native.pressHome();
  //         }
  //       });

}