<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite errors="0" failures="0" tests="17" skipped="0" name=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" timestamp="2024-04-16T17:31:32">
    <properties>
      <property name="platform" value="vm"/>
    </properties>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [] when nothing is added" time="0.021"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] when SignInEvent.emailChanged is added" time="0.006"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] when SignInEvent.passwordChanged is added" time="0.002"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] when SignInEvent.SignInWithGoogle is added" time="0.006"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] when SignInEvent.SignInWithApple is added" time="0.002"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] when SignInEvent.RegisterWithEmail is added" time="0.017"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] when SignInEvent.SignInWithEmail is added" time="0.003"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] with failure when SignInEvent.SignInWithEmail is added and signInWithEmailAndPassword fails" time="0.002"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] when SignInEvent.ForgotPassword is added" time="0.003"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInBloc emits [SignInState] when SignInEvent.ToggleObscureText is added" time="0.002"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInEvent EmailChanged creates correct event" time="0.002"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInEvent PasswordChanged creates correct event" time="0.001"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInEvent ForgotPassword creates correct event" time="0.001"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInEvent SignInWithEmail creates correct event" time="0.001"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInEvent RegisterWithEmail creates correct event" time="0.001"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInState SignInState.initial creates correct initial state" time="0.001"/>
    <testcase classname=".Users.roviandsouza.Documents.JunoPlus.authentication.test.signin_bloc" name="SignInState SignInState creates correct state" time="0.001"/>
  </testsuite>
</testsuites>