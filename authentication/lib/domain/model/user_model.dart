import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

// This class represents a user model with JSON serialization.
// It includes fields for user ID, email, and name.
@JsonSerializable(explicitToJson: true)
class UserModel {
  // User ID
  String? uid;
  // User email
  String? userEmail;
  // User name
  String? userName;
  // isOnboarded flag
  bool? isOnboarded;
  //Email Verification flag
  bool? isEmailVerified;

  // Constructor for the UserModel class
  UserModel({this.uid, this.userEmail, this.userName, this.isOnboarded,this.isEmailVerified});

  // Method to convert a UserModel instance into a JSON map.
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  // Factory constructor to create a UserModel instance from a JSON map.
  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}