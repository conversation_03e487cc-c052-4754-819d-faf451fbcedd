import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import '../failure/auth_failure.dart';
import '../model/user_model.dart';
import '../model/value_objects.dart';

// This interface defines the methods for authentication operations.
abstract class IAuthFacade {
  // Method to sign in with Google
  Future<Either<AuthFailure, UserModel>> signInWithGoogle();

  // Method to sign in with Apple
  Future<Either<AuthFailure, UserModel>> signInWithApple();

  // Method to get the currently signed-in user
  Future<Option<UserModel>> getSignedInUser();

  // Method to register a user with email and password
  Future<Either<AuthFailure, UserModel>> registerWithEmailAndPassword({
    required EmailAddress emailAddress,
    required Password password,
  });

  // Method to sign out the current user
  Future<void> signOut();

  // Method to sign in a user with email and password
  Future<Either<AuthFailure, UserModel>> signInWithEmailAndPassword({
    required EmailAddress emailAddress,
    required Password password,
  });

  // Method to send a password reset email to a user
  Future<Either<AuthFailure, Unit>> sendPasswordResetEmail({
    required EmailAddress emailAddress,
  });
  // email verification
  Future<Either<AuthFailure, Unit>> sendEmailVerification();
  // check if email is verified
  Future<Either<AuthFailure, Unit>> isEmailVerified();
}