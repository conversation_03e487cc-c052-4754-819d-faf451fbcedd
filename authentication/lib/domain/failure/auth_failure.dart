import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_failure.freezed.dart';

// This class represents different types of authentication failures.
// It uses the Freezed package for union types.
@freezed
abstract class AuthFailure with _$AuthFailure {
  // Represents a failure where the user was not found.
  const factory AuthFailure.userNotFound() = UserNotFound;

  // Represents a failure where the user cancelled the operation.
  const factory AuthFailure.cancelledByUser() = CancelledByUser;

  // Represents a failure where a server error occurred.
  const factory AuthFailure.serverError() = ServerError;

  // Represents a failure where the email is already in use.
  const factory AuthFailure.emailAlreadyInUse() = EmailAlreadyInUse;

  // Represents a failure where the email and password combination is invalid.
  const factory AuthFailure.invalidEmailAndPasswordCombination() =
  InvalidEmailAndPasswordCombination;
  // Represents a failure where the email verification failed.
  const factory AuthFailure.emailVerificationFailed() = EmailVerificationFailed;
  // Represents a failure where the email verification email was not sent.
  const factory AuthFailure.emailVerificationSendFailure() = EmailVerificationSendFailure;
}