import 'package:fpdart/fpdart.dart';
import 'package:meta/meta.dart';
import 'package:uuid/uuid.dart';

import 'errors.dart';
import 'value_validators.dart';
import 'failures.dart';



@immutable
abstract class ValueObject<T> {
  const ValueObject();
  Either<ValueFailure<T>, T> get value;

  T getOrCrash() {
    return value.getOrElse((f) => throw UnexpectedValueError(f));
  }

  T getOrElse(T dflt) {
    return value.getOrElse((_) => dflt);
  }

  // Either<ValueFailure<dynamic>, Unit> get failureOrUnit {
  //      return value.mapBoth(onLeft:Left(ValueFailure<dynamic>(value)), (_) => Right(unit));
  //
  //
  // }

  bool isValid() {
    return value.getOrNull()==null?false:true;
  }

  @override
  bool operator ==(Object o) {
    if (identical(this, o)) return true;
    return o is ValueObject<T> && o.value == value;
  }

  @override
  int get hashCode => value.hashCode;

  @override
  String toString() => 'Value($value)';
}

class UniqueId extends ValueObject<String> {
  @override
  final Either<ValueFailure<String>, String> value;

  factory UniqueId() {
    return UniqueId._(Right(Uuid().v1()));
  }

  factory UniqueId.fromUniqueString(String uniqueIdStr) {
    return UniqueId._(Right(uniqueIdStr));
  }

  const UniqueId._(this.value);
}

class StringSingleLine extends ValueObject<String> {
  @override
  final Either<ValueFailure<String>, String> value;

  factory StringSingleLine(String input) {
    return StringSingleLine._(validateSingleLine(input));
  }

  const StringSingleLine._(this.value);
}
