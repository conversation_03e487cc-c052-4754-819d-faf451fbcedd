import 'package:fpdart/fpdart.dart';
import 'failures.dart';

// Function to validate the maximum length of a string
// Returns the input string if its length is less than or equal to the maximum length
// Otherwise, returns a ValueFailure indicating that the string length exceeds the maximum length
Either<ValueFailure<String>, String> validateMaxStringLength(String input, int maxLength) {
  if (input.length <= maxLength) {
    return Right(input);
  } else {
    return Left(ValueFailure.exceedingLength(failedValue: input, max: maxLength));
  }
}

// Function to validate that a string is not empty
// Returns the input string if it is not empty
// Otherwise, returns a ValueFailure indicating that the string is empty
Either<ValueFailure<String>, String> validateStringNotEmpty(String input) {
  if (input.isEmpty) {
    return Left(ValueFailure.empty(failedValue: input));
  } else {
    return Right(input);
  }
}

// Function to validate that a string does not contain multiple lines
// Returns the input string if it does not contain a newline character
// Otherwise, returns a ValueFailure indicating that the string contains multiple lines
Either<ValueFailure<String>, String> validateSingleLine(String input) {
  if (input.contains('\n')) {
    return Left(ValueFailure.multiline(failedValue: input));
  } else {
    return Right(input);
  }
}

// Function to validate the maximum length of a list
// Returns the input list if its length is less than or equal to the maximum length
// Otherwise, returns a ValueFailure indicating that the list length exceeds the maximum length
Either<ValueFailure<List<T>>, List<T>> validateMaxListLength<T>(List<T> input, int maxLength) {
  if (input.length <= maxLength) {
    return Right(input);
  } else {
    return Left(ValueFailure.listTooLong(failedValue: input, max: maxLength));
  }
}

// Function to validate an email address
// Returns the input string if it matches the email regex
// Otherwise, returns a ValueFailure indicating that the string is not a valid email address
Either<ValueFailure<String>, String> validateEmailAddress(String input) {
  const emailRegex = r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+$";
  if (RegExp(emailRegex).hasMatch(input)) {
    return Right(input);
  } else {
    return Left(ValueFailure.invalidEmail(failedValue: input));
  }
}

// Function to validate a password
// Returns the input string if its length is greater than or equal to 6
// Otherwise, returns a ValueFailure indicating that the password is too short
Either<ValueFailure<String>, String> validatePassword(String input) {
  if (input.length >= 6) {
    return Right(input);
  } else {
    return Left(ValueFailure.shortPassword(failedValue: input));
  }
}