import 'package:firebase_auth/firebase_auth.dart';  // Importing FirebaseAuth for Firebase authentication
import 'package:google_sign_in/google_sign_in.dart';  // Importing GoogleSignIn for Google authentication
import 'package:injectable/injectable.dart';  // Importing injectable for dependency injection

// Annotation to mark the class as a module for dependency injection
@module
abstract class FirebaseInjectableModule {
  // Defining a lazy singleton for GoogleSignIn
  @lazySingleton
  GoogleSignIn get googleSignIn => GoogleSignIn();

  // Defining a lazy singleton for FirebaseAuth
  @lazySingleton
  FirebaseAuth get firebaseAuth => FirebaseAuth.instance;
}
