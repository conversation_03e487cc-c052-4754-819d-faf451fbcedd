// Import necessary packages and files
import 'package:authentication/domain/model/user_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/services.dart';
import 'package:fpdart/fpdart.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../domain/facade/i_auth_facade.dart';
import '../domain/failure/auth_failure.dart';
import '../domain/model/value_objects.dart';
import 'firebase_user_mapper.dart';

// Injectable class annotation for lazy singleton
@LazySingleton(as: IAuthFacade)
class FirebaseAuthFacade implements IAuthFacade {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final FirebaseUserMapper _firebaseUserMapper;

  // Constructor to initialize dependencies
  FirebaseAuthFacade(
      this._firebaseAuth,
      this._googleSignIn,
      this._firebaseUserMapper,
      );

  @override
  Future<Option<UserModel>> getSignedInUser() async {
    final user = _firebaseAuth.currentUser;
    if (user != null) {
      final userModel = await _firebaseUserMapper.toDomain(user);
      return Option.fromNullable(userModel);
    } else {
      return None();
    }
  }

  @override
  Future<Either<AuthFailure, UserModel>> registerWithEmailAndPassword({
    required EmailAddress emailAddress,
    required Password password,
  }) async {
    final emailAddressStr = emailAddress.value.getOrElse((failure) => 'INVALID EMAIL');
    final passwordStr = password.value.getOrElse((failure) => 'INVALID PASSWORD');
    try {
      final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: emailAddressStr,
        password: passwordStr,
      );
      final userModel = await _firebaseUserMapper.toDomain(userCredential.user!);
      if (userModel != null) {
        return Right(userModel);
      } else {
        return Left(const AuthFailure.serverError());
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == 'email-already-in-use') {
        return Left(const AuthFailure.emailAlreadyInUse());
      } else {
        return Left(const AuthFailure.serverError());
      }
    }
  }

  @override
  Future<Either<AuthFailure, UserModel>> signInWithEmailAndPassword({
    required EmailAddress emailAddress,
    required Password password,
  }) async {
    final emailAddressStr = emailAddress.value.getOrElse((failure) => 'INVALID EMAIL');
    final passwordStr = password.value.getOrElse((failure) => 'INVALID PASSWORD');
    try {
      print('email: $emailAddressStr, password: $passwordStr');
      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: emailAddressStr,
        password: passwordStr,
      );
      final userModel = await _firebaseUserMapper.toDomain(userCredential.user!);
      if (userModel != null) {
        return Right(userModel);
      } else {
        return Left(const AuthFailure.serverError());
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found' || e.code == 'invalid-credential') {
        return Left(const AuthFailure.invalidEmailAndPasswordCombination());
      } else {
        return Left(const AuthFailure.serverError());}
    }
  }

  @override
  Future<Either<AuthFailure, UserModel>> signInWithGoogle() async {
    try {
      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return Left(const AuthFailure.cancelledByUser());
      }
      final googleAuthentication = await googleUser.authentication;
      final authCredential = GoogleAuthProvider.credential(
        accessToken: googleAuthentication.accessToken,
        idToken: googleAuthentication.idToken,
      );

      // Check if email is already in use with email/password provider
      final signInMethods = await _firebaseAuth.fetchSignInMethodsForEmail(googleUser.email);
      if (signInMethods.contains('password')) {
        // Sign in with email/password first to get the user
        final emailUserCredential = await _firebaseAuth.signInWithEmailAndPassword(
          email: googleUser.email,
          password: 'your_password_here', // Replace with the actual password
        );
        // Link the Google credential to the email/password account
        await emailUserCredential.user!.linkWithCredential(authCredential);
        final userModel = await _firebaseUserMapper.toDomain(emailUserCredential.user!);
        if (userModel != null) {
          return Right(userModel);
        } else {
          return Left(const AuthFailure.serverError());
        }
      } else {
        // Sign in with the Google credential directly
        final userCredential = await _firebaseAuth.signInWithCredential(authCredential);
        final userModel = await _firebaseUserMapper.toDomain(userCredential.user!);
        if (userModel != null) {
          return Right(userModel);
        } else {
          return Left(const AuthFailure.serverError());
        }
      }
    } on PlatformException catch (_) {
      return Left(const AuthFailure.serverError());
    }
  }

  @override
  Future<Either<AuthFailure, UserModel>> signInWithApple() async {
    try {
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );
      final authResult = await _firebaseAuth.signInWithCredential(oauthCredential);
      final userModel = await _firebaseUserMapper.toDomain(authResult.user!);
      if (userModel != null) {
        return Right(userModel);
      } else {
        return Left(const AuthFailure.serverError());
      }
    } on PlatformException catch (e) {
      if (e.code == 'ERROR_ABORTED_BY_USER') {
        print(e);
        return Left(const AuthFailure.cancelledByUser());
      } else {
        return Left(const AuthFailure.serverError());
      }
    }
  }

  @override
  Future<Either<AuthFailure, Unit>> sendPasswordResetEmail({
    required EmailAddress emailAddress,
  }) async {

    final emailAddressStr = emailAddress.value.getOrElse((failure) => 'INVALID EMAIL');
    try {
      _firebaseAuth.sendPasswordResetEmail(email: emailAddressStr).timeout(const Duration(seconds: 5));
      return Right(unit);
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        return Left(const AuthFailure.userNotFound());
      } else {
        return Left(const AuthFailure.serverError());
      }
    }
  }

  @override
  Future<void> signOut() async {
    await Future.wait([
      _googleSignIn.signOut(),
      _firebaseAuth.signOut(),
    ]);
  }

  @override
  Future<Either<AuthFailure, Unit>> isEmailVerified() async {
    try {
      await FirebaseAuth.instance.currentUser?.reload();
      final user = _firebaseAuth.currentUser;

      if (user != null) {
        print('User: ${user.emailVerified}');
        if (user.emailVerified) {
          return Right(unit);
        } else {
          return Left(const AuthFailure.emailVerificationFailed());
        }
      } else {
        return Left(const AuthFailure.emailVerificationFailed());
      }
    } on FirebaseAuthException catch (e) {
      return Left(const AuthFailure.emailVerificationFailed());
    }
  }

  @override
  Future<Either<AuthFailure, Unit>> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await user.sendEmailVerification();
        return Right(unit);
      } else {
        return Left(const AuthFailure.emailVerificationSendFailure());
      }
    } on FirebaseAuthException catch (e) {
      return Left(const AuthFailure.emailVerificationSendFailure());
    }
  }
}
