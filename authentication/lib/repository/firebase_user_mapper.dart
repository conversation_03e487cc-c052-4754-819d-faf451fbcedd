import 'dart:convert';

import 'package:authentication/domain/model/user_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

@lazySingleton
class FirebaseUserMapper {
  // Method to map a User object from FirebaseAuth to a UserModel object from your domain
  // Now async to accommodate SharedPreferences and handling multiple users
  Future<UserModel?> toDomain(User _) async {
    final prefs = await SharedPreferences.getInstance();
    // Fetching onboardedUserIds as a JSON string and decoding it to a List
    final String? onboardedUserIdsJson = prefs.getString('onboardedUserIds');
    List<String> onboardedUserIds = [];
    if (onboardedUserIdsJson != null) {
      if (onboardedUserIdsJson != null) {
        final decoded = json.decode(onboardedUserIdsJson);
        if (decoded is List) {
          onboardedUserIds = List<String>.from(decoded);
        }
      }
    }

    // Checking if the current user's ID is in the list of onboarded user IDs
    final isOnboarded = onboardedUserIds.contains(_.uid);

    return UserModel(
      uid: _.uid,
      userName: _.displayName ?? _.email?.split('@').first,
      userEmail: _.email,
      isOnboarded: isOnboarded,
      isEmailVerified: _.emailVerified// Including isOnboarded in the UserModel based on the list
    );
  }
}
