import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../../../domain/facade/i_auth_facade.dart';
import 'package:injectable/injectable.dart';
part 'email_verification_event.dart';
part 'email_verification_state.dart';
part 'email_verification_bloc.freezed.dart';


@injectable
class EmailVerificationBloc extends Bloc<EmailVerificationEvent, EmailVerificationState> {
   final IAuthFacade _authFacade;
   EmailVerificationBloc(this._authFacade) : super(const EmailVerificationState.initial()) {
     on<RequestVerificationEmail>(_onRequestVerificationEmail);
     on<CheckEmailVerified>(_onCheckEmailVerified);
     on<ResendVerificationEmail>(_onResendVerificationEmail);
   }

   Future<void> _onRequestVerificationEmail(
       RequestVerificationEmail event, Emitter<EmailVerificationState> emit) async {
     final emailSentOrFailure = await _authFacade.sendEmailVerification();
      emailSentOrFailure.mapBoth(
        onLeft: (_) => emit(const VerificationFailed(message: 'Email verification failed')),
        onRight: (_) => emit(const VerificationEmailSent())
      );
   }

   Future<void> _onCheckEmailVerified(
       CheckEmailVerified event, Emitter<EmailVerificationState> emit) async {
       final isVerified = await _authFacade.isEmailVerified();
       isVerified.mapBoth(onLeft: (_) => emit(const EmailNotVerified()), onRight: (_) => emit(const EmailVerified()));

   }

   Future<void> _onResendVerificationEmail(
       ResendVerificationEmail event, Emitter<EmailVerificationState> emit) async {
     try {
       await _authFacade.sendEmailVerification();
       emit(const VerificationEmailSent());
     } catch (e) {
       emit(VerificationFailed(message: e.toString()));
     }
   }
}
