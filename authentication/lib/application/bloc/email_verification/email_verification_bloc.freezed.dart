// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'email_verification_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EmailVerificationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() requestVerificationEmail,
    required TResult Function() checkEmailVerified,
    required TResult Function() resendVerificationEmail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? requestVerificationEmail,
    TResult? Function()? checkEmailVerified,
    TResult? Function()? resendVerificationEmail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? requestVerificationEmail,
    TResult Function()? checkEmailVerified,
    TResult Function()? resendVerificationEmail,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RequestVerificationEmail value)
        requestVerificationEmail,
    required TResult Function(CheckEmailVerified value) checkEmailVerified,
    required TResult Function(ResendVerificationEmail value)
        resendVerificationEmail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult? Function(CheckEmailVerified value)? checkEmailVerified,
    TResult? Function(ResendVerificationEmail value)? resendVerificationEmail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult Function(CheckEmailVerified value)? checkEmailVerified,
    TResult Function(ResendVerificationEmail value)? resendVerificationEmail,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmailVerificationEventCopyWith<$Res> {
  factory $EmailVerificationEventCopyWith(EmailVerificationEvent value,
          $Res Function(EmailVerificationEvent) then) =
      _$EmailVerificationEventCopyWithImpl<$Res, EmailVerificationEvent>;
}

/// @nodoc
class _$EmailVerificationEventCopyWithImpl<$Res,
        $Val extends EmailVerificationEvent>
    implements $EmailVerificationEventCopyWith<$Res> {
  _$EmailVerificationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmailVerificationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$RequestVerificationEmailImplCopyWith<$Res> {
  factory _$$RequestVerificationEmailImplCopyWith(
          _$RequestVerificationEmailImpl value,
          $Res Function(_$RequestVerificationEmailImpl) then) =
      __$$RequestVerificationEmailImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RequestVerificationEmailImplCopyWithImpl<$Res>
    extends _$EmailVerificationEventCopyWithImpl<$Res,
        _$RequestVerificationEmailImpl>
    implements _$$RequestVerificationEmailImplCopyWith<$Res> {
  __$$RequestVerificationEmailImplCopyWithImpl(
      _$RequestVerificationEmailImpl _value,
      $Res Function(_$RequestVerificationEmailImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RequestVerificationEmailImpl implements RequestVerificationEmail {
  const _$RequestVerificationEmailImpl();

  @override
  String toString() {
    return 'EmailVerificationEvent.requestVerificationEmail()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequestVerificationEmailImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() requestVerificationEmail,
    required TResult Function() checkEmailVerified,
    required TResult Function() resendVerificationEmail,
  }) {
    return requestVerificationEmail();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? requestVerificationEmail,
    TResult? Function()? checkEmailVerified,
    TResult? Function()? resendVerificationEmail,
  }) {
    return requestVerificationEmail?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? requestVerificationEmail,
    TResult Function()? checkEmailVerified,
    TResult Function()? resendVerificationEmail,
    required TResult orElse(),
  }) {
    if (requestVerificationEmail != null) {
      return requestVerificationEmail();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RequestVerificationEmail value)
        requestVerificationEmail,
    required TResult Function(CheckEmailVerified value) checkEmailVerified,
    required TResult Function(ResendVerificationEmail value)
        resendVerificationEmail,
  }) {
    return requestVerificationEmail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult? Function(CheckEmailVerified value)? checkEmailVerified,
    TResult? Function(ResendVerificationEmail value)? resendVerificationEmail,
  }) {
    return requestVerificationEmail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult Function(CheckEmailVerified value)? checkEmailVerified,
    TResult Function(ResendVerificationEmail value)? resendVerificationEmail,
    required TResult orElse(),
  }) {
    if (requestVerificationEmail != null) {
      return requestVerificationEmail(this);
    }
    return orElse();
  }
}

abstract class RequestVerificationEmail implements EmailVerificationEvent {
  const factory RequestVerificationEmail() = _$RequestVerificationEmailImpl;
}

/// @nodoc
abstract class _$$CheckEmailVerifiedImplCopyWith<$Res> {
  factory _$$CheckEmailVerifiedImplCopyWith(_$CheckEmailVerifiedImpl value,
          $Res Function(_$CheckEmailVerifiedImpl) then) =
      __$$CheckEmailVerifiedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckEmailVerifiedImplCopyWithImpl<$Res>
    extends _$EmailVerificationEventCopyWithImpl<$Res, _$CheckEmailVerifiedImpl>
    implements _$$CheckEmailVerifiedImplCopyWith<$Res> {
  __$$CheckEmailVerifiedImplCopyWithImpl(_$CheckEmailVerifiedImpl _value,
      $Res Function(_$CheckEmailVerifiedImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckEmailVerifiedImpl implements CheckEmailVerified {
  const _$CheckEmailVerifiedImpl();

  @override
  String toString() {
    return 'EmailVerificationEvent.checkEmailVerified()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckEmailVerifiedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() requestVerificationEmail,
    required TResult Function() checkEmailVerified,
    required TResult Function() resendVerificationEmail,
  }) {
    return checkEmailVerified();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? requestVerificationEmail,
    TResult? Function()? checkEmailVerified,
    TResult? Function()? resendVerificationEmail,
  }) {
    return checkEmailVerified?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? requestVerificationEmail,
    TResult Function()? checkEmailVerified,
    TResult Function()? resendVerificationEmail,
    required TResult orElse(),
  }) {
    if (checkEmailVerified != null) {
      return checkEmailVerified();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RequestVerificationEmail value)
        requestVerificationEmail,
    required TResult Function(CheckEmailVerified value) checkEmailVerified,
    required TResult Function(ResendVerificationEmail value)
        resendVerificationEmail,
  }) {
    return checkEmailVerified(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult? Function(CheckEmailVerified value)? checkEmailVerified,
    TResult? Function(ResendVerificationEmail value)? resendVerificationEmail,
  }) {
    return checkEmailVerified?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult Function(CheckEmailVerified value)? checkEmailVerified,
    TResult Function(ResendVerificationEmail value)? resendVerificationEmail,
    required TResult orElse(),
  }) {
    if (checkEmailVerified != null) {
      return checkEmailVerified(this);
    }
    return orElse();
  }
}

abstract class CheckEmailVerified implements EmailVerificationEvent {
  const factory CheckEmailVerified() = _$CheckEmailVerifiedImpl;
}

/// @nodoc
abstract class _$$ResendVerificationEmailImplCopyWith<$Res> {
  factory _$$ResendVerificationEmailImplCopyWith(
          _$ResendVerificationEmailImpl value,
          $Res Function(_$ResendVerificationEmailImpl) then) =
      __$$ResendVerificationEmailImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResendVerificationEmailImplCopyWithImpl<$Res>
    extends _$EmailVerificationEventCopyWithImpl<$Res,
        _$ResendVerificationEmailImpl>
    implements _$$ResendVerificationEmailImplCopyWith<$Res> {
  __$$ResendVerificationEmailImplCopyWithImpl(
      _$ResendVerificationEmailImpl _value,
      $Res Function(_$ResendVerificationEmailImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResendVerificationEmailImpl implements ResendVerificationEmail {
  const _$ResendVerificationEmailImpl();

  @override
  String toString() {
    return 'EmailVerificationEvent.resendVerificationEmail()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResendVerificationEmailImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() requestVerificationEmail,
    required TResult Function() checkEmailVerified,
    required TResult Function() resendVerificationEmail,
  }) {
    return resendVerificationEmail();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? requestVerificationEmail,
    TResult? Function()? checkEmailVerified,
    TResult? Function()? resendVerificationEmail,
  }) {
    return resendVerificationEmail?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? requestVerificationEmail,
    TResult Function()? checkEmailVerified,
    TResult Function()? resendVerificationEmail,
    required TResult orElse(),
  }) {
    if (resendVerificationEmail != null) {
      return resendVerificationEmail();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RequestVerificationEmail value)
        requestVerificationEmail,
    required TResult Function(CheckEmailVerified value) checkEmailVerified,
    required TResult Function(ResendVerificationEmail value)
        resendVerificationEmail,
  }) {
    return resendVerificationEmail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult? Function(CheckEmailVerified value)? checkEmailVerified,
    TResult? Function(ResendVerificationEmail value)? resendVerificationEmail,
  }) {
    return resendVerificationEmail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult Function(CheckEmailVerified value)? checkEmailVerified,
    TResult Function(ResendVerificationEmail value)? resendVerificationEmail,
    required TResult orElse(),
  }) {
    if (resendVerificationEmail != null) {
      return resendVerificationEmail(this);
    }
    return orElse();
  }
}

abstract class ResendVerificationEmail implements EmailVerificationEvent {
  const factory ResendVerificationEmail() = _$ResendVerificationEmailImpl;
}

/// @nodoc
mixin _$EmailVerificationState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() verificationEmailSent,
    required TResult Function() verificationInProgress,
    required TResult Function() emailVerified,
    required TResult Function() emailNotVerified,
    required TResult Function(String message) verificationFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? verificationEmailSent,
    TResult? Function()? verificationInProgress,
    TResult? Function()? emailVerified,
    TResult? Function()? emailNotVerified,
    TResult? Function(String message)? verificationFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? verificationEmailSent,
    TResult Function()? verificationInProgress,
    TResult Function()? emailVerified,
    TResult Function()? emailNotVerified,
    TResult Function(String message)? verificationFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(VerificationEmailSent value)
        verificationEmailSent,
    required TResult Function(VerificationInProgress value)
        verificationInProgress,
    required TResult Function(EmailVerified value) emailVerified,
    required TResult Function(EmailNotVerified value) emailNotVerified,
    required TResult Function(VerificationFailed value) verificationFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(VerificationEmailSent value)? verificationEmailSent,
    TResult? Function(VerificationInProgress value)? verificationInProgress,
    TResult? Function(EmailVerified value)? emailVerified,
    TResult? Function(EmailNotVerified value)? emailNotVerified,
    TResult? Function(VerificationFailed value)? verificationFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(VerificationEmailSent value)? verificationEmailSent,
    TResult Function(VerificationInProgress value)? verificationInProgress,
    TResult Function(EmailVerified value)? emailVerified,
    TResult Function(EmailNotVerified value)? emailNotVerified,
    TResult Function(VerificationFailed value)? verificationFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmailVerificationStateCopyWith<$Res> {
  factory $EmailVerificationStateCopyWith(EmailVerificationState value,
          $Res Function(EmailVerificationState) then) =
      _$EmailVerificationStateCopyWithImpl<$Res, EmailVerificationState>;
}

/// @nodoc
class _$EmailVerificationStateCopyWithImpl<$Res,
        $Val extends EmailVerificationState>
    implements $EmailVerificationStateCopyWith<$Res> {
  _$EmailVerificationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$EmailVerificationStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'EmailVerificationState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() verificationEmailSent,
    required TResult Function() verificationInProgress,
    required TResult Function() emailVerified,
    required TResult Function() emailNotVerified,
    required TResult Function(String message) verificationFailed,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? verificationEmailSent,
    TResult? Function()? verificationInProgress,
    TResult? Function()? emailVerified,
    TResult? Function()? emailNotVerified,
    TResult? Function(String message)? verificationFailed,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? verificationEmailSent,
    TResult Function()? verificationInProgress,
    TResult Function()? emailVerified,
    TResult Function()? emailNotVerified,
    TResult Function(String message)? verificationFailed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(VerificationEmailSent value)
        verificationEmailSent,
    required TResult Function(VerificationInProgress value)
        verificationInProgress,
    required TResult Function(EmailVerified value) emailVerified,
    required TResult Function(EmailNotVerified value) emailNotVerified,
    required TResult Function(VerificationFailed value) verificationFailed,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(VerificationEmailSent value)? verificationEmailSent,
    TResult? Function(VerificationInProgress value)? verificationInProgress,
    TResult? Function(EmailVerified value)? emailVerified,
    TResult? Function(EmailNotVerified value)? emailNotVerified,
    TResult? Function(VerificationFailed value)? verificationFailed,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(VerificationEmailSent value)? verificationEmailSent,
    TResult Function(VerificationInProgress value)? verificationInProgress,
    TResult Function(EmailVerified value)? emailVerified,
    TResult Function(EmailNotVerified value)? emailNotVerified,
    TResult Function(VerificationFailed value)? verificationFailed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements EmailVerificationState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$VerificationEmailSentImplCopyWith<$Res> {
  factory _$$VerificationEmailSentImplCopyWith(
          _$VerificationEmailSentImpl value,
          $Res Function(_$VerificationEmailSentImpl) then) =
      __$$VerificationEmailSentImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VerificationEmailSentImplCopyWithImpl<$Res>
    extends _$EmailVerificationStateCopyWithImpl<$Res,
        _$VerificationEmailSentImpl>
    implements _$$VerificationEmailSentImplCopyWith<$Res> {
  __$$VerificationEmailSentImplCopyWithImpl(_$VerificationEmailSentImpl _value,
      $Res Function(_$VerificationEmailSentImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VerificationEmailSentImpl implements VerificationEmailSent {
  const _$VerificationEmailSentImpl();

  @override
  String toString() {
    return 'EmailVerificationState.verificationEmailSent()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationEmailSentImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() verificationEmailSent,
    required TResult Function() verificationInProgress,
    required TResult Function() emailVerified,
    required TResult Function() emailNotVerified,
    required TResult Function(String message) verificationFailed,
  }) {
    return verificationEmailSent();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? verificationEmailSent,
    TResult? Function()? verificationInProgress,
    TResult? Function()? emailVerified,
    TResult? Function()? emailNotVerified,
    TResult? Function(String message)? verificationFailed,
  }) {
    return verificationEmailSent?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? verificationEmailSent,
    TResult Function()? verificationInProgress,
    TResult Function()? emailVerified,
    TResult Function()? emailNotVerified,
    TResult Function(String message)? verificationFailed,
    required TResult orElse(),
  }) {
    if (verificationEmailSent != null) {
      return verificationEmailSent();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(VerificationEmailSent value)
        verificationEmailSent,
    required TResult Function(VerificationInProgress value)
        verificationInProgress,
    required TResult Function(EmailVerified value) emailVerified,
    required TResult Function(EmailNotVerified value) emailNotVerified,
    required TResult Function(VerificationFailed value) verificationFailed,
  }) {
    return verificationEmailSent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(VerificationEmailSent value)? verificationEmailSent,
    TResult? Function(VerificationInProgress value)? verificationInProgress,
    TResult? Function(EmailVerified value)? emailVerified,
    TResult? Function(EmailNotVerified value)? emailNotVerified,
    TResult? Function(VerificationFailed value)? verificationFailed,
  }) {
    return verificationEmailSent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(VerificationEmailSent value)? verificationEmailSent,
    TResult Function(VerificationInProgress value)? verificationInProgress,
    TResult Function(EmailVerified value)? emailVerified,
    TResult Function(EmailNotVerified value)? emailNotVerified,
    TResult Function(VerificationFailed value)? verificationFailed,
    required TResult orElse(),
  }) {
    if (verificationEmailSent != null) {
      return verificationEmailSent(this);
    }
    return orElse();
  }
}

abstract class VerificationEmailSent implements EmailVerificationState {
  const factory VerificationEmailSent() = _$VerificationEmailSentImpl;
}

/// @nodoc
abstract class _$$VerificationInProgressImplCopyWith<$Res> {
  factory _$$VerificationInProgressImplCopyWith(
          _$VerificationInProgressImpl value,
          $Res Function(_$VerificationInProgressImpl) then) =
      __$$VerificationInProgressImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VerificationInProgressImplCopyWithImpl<$Res>
    extends _$EmailVerificationStateCopyWithImpl<$Res,
        _$VerificationInProgressImpl>
    implements _$$VerificationInProgressImplCopyWith<$Res> {
  __$$VerificationInProgressImplCopyWithImpl(
      _$VerificationInProgressImpl _value,
      $Res Function(_$VerificationInProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VerificationInProgressImpl implements VerificationInProgress {
  const _$VerificationInProgressImpl();

  @override
  String toString() {
    return 'EmailVerificationState.verificationInProgress()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationInProgressImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() verificationEmailSent,
    required TResult Function() verificationInProgress,
    required TResult Function() emailVerified,
    required TResult Function() emailNotVerified,
    required TResult Function(String message) verificationFailed,
  }) {
    return verificationInProgress();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? verificationEmailSent,
    TResult? Function()? verificationInProgress,
    TResult? Function()? emailVerified,
    TResult? Function()? emailNotVerified,
    TResult? Function(String message)? verificationFailed,
  }) {
    return verificationInProgress?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? verificationEmailSent,
    TResult Function()? verificationInProgress,
    TResult Function()? emailVerified,
    TResult Function()? emailNotVerified,
    TResult Function(String message)? verificationFailed,
    required TResult orElse(),
  }) {
    if (verificationInProgress != null) {
      return verificationInProgress();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(VerificationEmailSent value)
        verificationEmailSent,
    required TResult Function(VerificationInProgress value)
        verificationInProgress,
    required TResult Function(EmailVerified value) emailVerified,
    required TResult Function(EmailNotVerified value) emailNotVerified,
    required TResult Function(VerificationFailed value) verificationFailed,
  }) {
    return verificationInProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(VerificationEmailSent value)? verificationEmailSent,
    TResult? Function(VerificationInProgress value)? verificationInProgress,
    TResult? Function(EmailVerified value)? emailVerified,
    TResult? Function(EmailNotVerified value)? emailNotVerified,
    TResult? Function(VerificationFailed value)? verificationFailed,
  }) {
    return verificationInProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(VerificationEmailSent value)? verificationEmailSent,
    TResult Function(VerificationInProgress value)? verificationInProgress,
    TResult Function(EmailVerified value)? emailVerified,
    TResult Function(EmailNotVerified value)? emailNotVerified,
    TResult Function(VerificationFailed value)? verificationFailed,
    required TResult orElse(),
  }) {
    if (verificationInProgress != null) {
      return verificationInProgress(this);
    }
    return orElse();
  }
}

abstract class VerificationInProgress implements EmailVerificationState {
  const factory VerificationInProgress() = _$VerificationInProgressImpl;
}

/// @nodoc
abstract class _$$EmailVerifiedImplCopyWith<$Res> {
  factory _$$EmailVerifiedImplCopyWith(
          _$EmailVerifiedImpl value, $Res Function(_$EmailVerifiedImpl) then) =
      __$$EmailVerifiedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EmailVerifiedImplCopyWithImpl<$Res>
    extends _$EmailVerificationStateCopyWithImpl<$Res, _$EmailVerifiedImpl>
    implements _$$EmailVerifiedImplCopyWith<$Res> {
  __$$EmailVerifiedImplCopyWithImpl(
      _$EmailVerifiedImpl _value, $Res Function(_$EmailVerifiedImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EmailVerifiedImpl implements EmailVerified {
  const _$EmailVerifiedImpl();

  @override
  String toString() {
    return 'EmailVerificationState.emailVerified()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EmailVerifiedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() verificationEmailSent,
    required TResult Function() verificationInProgress,
    required TResult Function() emailVerified,
    required TResult Function() emailNotVerified,
    required TResult Function(String message) verificationFailed,
  }) {
    return emailVerified();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? verificationEmailSent,
    TResult? Function()? verificationInProgress,
    TResult? Function()? emailVerified,
    TResult? Function()? emailNotVerified,
    TResult? Function(String message)? verificationFailed,
  }) {
    return emailVerified?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? verificationEmailSent,
    TResult Function()? verificationInProgress,
    TResult Function()? emailVerified,
    TResult Function()? emailNotVerified,
    TResult Function(String message)? verificationFailed,
    required TResult orElse(),
  }) {
    if (emailVerified != null) {
      return emailVerified();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(VerificationEmailSent value)
        verificationEmailSent,
    required TResult Function(VerificationInProgress value)
        verificationInProgress,
    required TResult Function(EmailVerified value) emailVerified,
    required TResult Function(EmailNotVerified value) emailNotVerified,
    required TResult Function(VerificationFailed value) verificationFailed,
  }) {
    return emailVerified(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(VerificationEmailSent value)? verificationEmailSent,
    TResult? Function(VerificationInProgress value)? verificationInProgress,
    TResult? Function(EmailVerified value)? emailVerified,
    TResult? Function(EmailNotVerified value)? emailNotVerified,
    TResult? Function(VerificationFailed value)? verificationFailed,
  }) {
    return emailVerified?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(VerificationEmailSent value)? verificationEmailSent,
    TResult Function(VerificationInProgress value)? verificationInProgress,
    TResult Function(EmailVerified value)? emailVerified,
    TResult Function(EmailNotVerified value)? emailNotVerified,
    TResult Function(VerificationFailed value)? verificationFailed,
    required TResult orElse(),
  }) {
    if (emailVerified != null) {
      return emailVerified(this);
    }
    return orElse();
  }
}

abstract class EmailVerified implements EmailVerificationState {
  const factory EmailVerified() = _$EmailVerifiedImpl;
}

/// @nodoc
abstract class _$$EmailNotVerifiedImplCopyWith<$Res> {
  factory _$$EmailNotVerifiedImplCopyWith(_$EmailNotVerifiedImpl value,
          $Res Function(_$EmailNotVerifiedImpl) then) =
      __$$EmailNotVerifiedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EmailNotVerifiedImplCopyWithImpl<$Res>
    extends _$EmailVerificationStateCopyWithImpl<$Res, _$EmailNotVerifiedImpl>
    implements _$$EmailNotVerifiedImplCopyWith<$Res> {
  __$$EmailNotVerifiedImplCopyWithImpl(_$EmailNotVerifiedImpl _value,
      $Res Function(_$EmailNotVerifiedImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EmailNotVerifiedImpl implements EmailNotVerified {
  const _$EmailNotVerifiedImpl();

  @override
  String toString() {
    return 'EmailVerificationState.emailNotVerified()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EmailNotVerifiedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() verificationEmailSent,
    required TResult Function() verificationInProgress,
    required TResult Function() emailVerified,
    required TResult Function() emailNotVerified,
    required TResult Function(String message) verificationFailed,
  }) {
    return emailNotVerified();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? verificationEmailSent,
    TResult? Function()? verificationInProgress,
    TResult? Function()? emailVerified,
    TResult? Function()? emailNotVerified,
    TResult? Function(String message)? verificationFailed,
  }) {
    return emailNotVerified?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? verificationEmailSent,
    TResult Function()? verificationInProgress,
    TResult Function()? emailVerified,
    TResult Function()? emailNotVerified,
    TResult Function(String message)? verificationFailed,
    required TResult orElse(),
  }) {
    if (emailNotVerified != null) {
      return emailNotVerified();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(VerificationEmailSent value)
        verificationEmailSent,
    required TResult Function(VerificationInProgress value)
        verificationInProgress,
    required TResult Function(EmailVerified value) emailVerified,
    required TResult Function(EmailNotVerified value) emailNotVerified,
    required TResult Function(VerificationFailed value) verificationFailed,
  }) {
    return emailNotVerified(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(VerificationEmailSent value)? verificationEmailSent,
    TResult? Function(VerificationInProgress value)? verificationInProgress,
    TResult? Function(EmailVerified value)? emailVerified,
    TResult? Function(EmailNotVerified value)? emailNotVerified,
    TResult? Function(VerificationFailed value)? verificationFailed,
  }) {
    return emailNotVerified?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(VerificationEmailSent value)? verificationEmailSent,
    TResult Function(VerificationInProgress value)? verificationInProgress,
    TResult Function(EmailVerified value)? emailVerified,
    TResult Function(EmailNotVerified value)? emailNotVerified,
    TResult Function(VerificationFailed value)? verificationFailed,
    required TResult orElse(),
  }) {
    if (emailNotVerified != null) {
      return emailNotVerified(this);
    }
    return orElse();
  }
}

abstract class EmailNotVerified implements EmailVerificationState {
  const factory EmailNotVerified() = _$EmailNotVerifiedImpl;
}

/// @nodoc
abstract class _$$VerificationFailedImplCopyWith<$Res> {
  factory _$$VerificationFailedImplCopyWith(_$VerificationFailedImpl value,
          $Res Function(_$VerificationFailedImpl) then) =
      __$$VerificationFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$VerificationFailedImplCopyWithImpl<$Res>
    extends _$EmailVerificationStateCopyWithImpl<$Res, _$VerificationFailedImpl>
    implements _$$VerificationFailedImplCopyWith<$Res> {
  __$$VerificationFailedImplCopyWithImpl(_$VerificationFailedImpl _value,
      $Res Function(_$VerificationFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$VerificationFailedImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$VerificationFailedImpl implements VerificationFailed {
  const _$VerificationFailedImpl({required this.message});

  @override
  final String message;

  @override
  String toString() {
    return 'EmailVerificationState.verificationFailed(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationFailedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationFailedImplCopyWith<_$VerificationFailedImpl> get copyWith =>
      __$$VerificationFailedImplCopyWithImpl<_$VerificationFailedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() verificationEmailSent,
    required TResult Function() verificationInProgress,
    required TResult Function() emailVerified,
    required TResult Function() emailNotVerified,
    required TResult Function(String message) verificationFailed,
  }) {
    return verificationFailed(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? verificationEmailSent,
    TResult? Function()? verificationInProgress,
    TResult? Function()? emailVerified,
    TResult? Function()? emailNotVerified,
    TResult? Function(String message)? verificationFailed,
  }) {
    return verificationFailed?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? verificationEmailSent,
    TResult Function()? verificationInProgress,
    TResult Function()? emailVerified,
    TResult Function()? emailNotVerified,
    TResult Function(String message)? verificationFailed,
    required TResult orElse(),
  }) {
    if (verificationFailed != null) {
      return verificationFailed(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(VerificationEmailSent value)
        verificationEmailSent,
    required TResult Function(VerificationInProgress value)
        verificationInProgress,
    required TResult Function(EmailVerified value) emailVerified,
    required TResult Function(EmailNotVerified value) emailNotVerified,
    required TResult Function(VerificationFailed value) verificationFailed,
  }) {
    return verificationFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(VerificationEmailSent value)? verificationEmailSent,
    TResult? Function(VerificationInProgress value)? verificationInProgress,
    TResult? Function(EmailVerified value)? emailVerified,
    TResult? Function(EmailNotVerified value)? emailNotVerified,
    TResult? Function(VerificationFailed value)? verificationFailed,
  }) {
    return verificationFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(VerificationEmailSent value)? verificationEmailSent,
    TResult Function(VerificationInProgress value)? verificationInProgress,
    TResult Function(EmailVerified value)? emailVerified,
    TResult Function(EmailNotVerified value)? emailNotVerified,
    TResult Function(VerificationFailed value)? verificationFailed,
    required TResult orElse(),
  }) {
    if (verificationFailed != null) {
      return verificationFailed(this);
    }
    return orElse();
  }
}

abstract class VerificationFailed implements EmailVerificationState {
  const factory VerificationFailed({required final String message}) =
      _$VerificationFailedImpl;

  String get message;

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationFailedImplCopyWith<_$VerificationFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
