part of 'sign_in_bloc.dart';

@freezed
abstract class SignInEvent with _$SignInEvent {
  // Event for when the email is changed
  const factory SignInEvent.emailChanged(String emailStr) = EmailChanged;

  // Event for when the password is changed
  const factory SignInEvent.passwordChanged(String passwordStr) = PasswordChanged;

  // Event for toggling the visibility of the password (obscure text)
  const factory SignInEvent.toggleObscureText() = ToggleObscureText;

  // Event for when the user requests a password reset
  const factory SignInEvent.forgotPassword(String email) = ForgotPassword;

  // Event for signing in with Google
  const factory SignInEvent.signInWithGoogle() = SignInWithGoogle;

  // Event for signing in with Apple
  const factory SignInEvent.signInWithApple() = SignInWithApple;

  // Event for signing in with email and password
  const factory SignInEvent.signInWithEmail(String email, String password) = SignInWithEmail;

  // Event for registering with email and password
  const factory SignInEvent.registerWithEmail(String email, String password) = RegisterWithEmail;
}
