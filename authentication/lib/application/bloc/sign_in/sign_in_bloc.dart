import 'package:authentication/domain/failure/auth_failure.dart';
import 'package:authentication/domain/facade/i_auth_facade.dart';
import 'package:authentication/domain/model/user_model.dart';
import 'package:authentication/domain/model/value_objects.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
part 'sign_in_bloc.freezed.dart';
part 'sign_in_event.dart';
part 'sign_in_state.dart';

@injectable
class SignInBloc extends Bloc<SignInEvent, SignInState> {
  late IAuthFacade _authFacade;

  SignInBloc(this._authFacade) : super(SignInState.initial()) {
    // Initialize the bloc with initial state and set up event handlers
    on<EmailChanged>(_onEmailChanged);
    on<PasswordChanged>(_onPasswordChanged);
    on<SignInWithGoogle>(_onSignInWithGoogle);
    on<SignInWithApple>(_onSignInWithApple);
    on<SignInWithEmail>(_onSignInWithEmail);
    on<RegisterWithEmail>(_onRegisterWithEmail);
    on<ToggleObscureText>(_onToggleObscureText);
    on<ForgotPassword>(_onForgotPassword);
  }

  // Event handler for email changed event
  void _onEmailChanged(
      EmailChanged event,
      Emitter<SignInState> emit,
      ) {
    emit(state.copyWith(
      emailAddress: EmailAddress(event.emailStr),
      authFailureOrSuccessOption: None(),
    ));
  }

  // Event handler for toggling obscure text (password visibility)
  void _onToggleObscureText(
      ToggleObscureText event,
      Emitter<SignInState> emit,
      ) {
    emit(state.copyWith(obscureText: !state.obscureText));
  }

  // Event handler for password changed event
  void _onPasswordChanged(
      PasswordChanged event,
      Emitter<SignInState> emit,
      ) {
    emit(state.copyWith(
      password: Password(event.passwordStr),
      authFailureOrSuccessOption: None(),
    ));
  }

  // Event handler for signing in with Google
  Future<void> _onSignInWithGoogle(
      SignInWithGoogle event,
      Emitter<SignInState> emit,
      ) async {
    emit(state.copyWith(isSubmitting: true));
    final failureOrSuccess = await _authFacade.signInWithGoogle();
    emit(state.copyWith(
      isSubmitting: false,
      authFailureOrSuccessOption: Some(failureOrSuccess),
    ));
  }

  // Event handler for signing in with Apple
  Future<void> _onSignInWithApple(
      SignInWithApple event,
      Emitter<SignInState> emit,
      ) async {
    emit(state.copyWith(isSubmitting: true));
    final failureOrSuccess = await _authFacade.signInWithApple();
    emit(state.copyWith(
      isSubmitting: false,
      authFailureOrSuccessOption: Some(failureOrSuccess),
    ));
  }

  // Event handler for signing in with email and password
  Future<void> _onSignInWithEmail(
      SignInWithEmail event,
      Emitter<SignInState> emit,
      ) async {
    emit(state.copyWith(isSubmitting: true));
    final failureOrSuccess = await _authFacade.signInWithEmailAndPassword(
      emailAddress: state.emailAddress,
      password: state.password,
    );
    emit(state.copyWith(
      isSubmitting: false,
      authFailureOrSuccessOption: Some(failureOrSuccess),
    ));
  }

  // Event handler for registering with email and password
  Future<void> _onRegisterWithEmail(
      RegisterWithEmail event,
      Emitter<SignInState> emit,
      ) async {
    emit(state.copyWith(isSubmitting: true));
    final failureOrSuccess = await _authFacade.registerWithEmailAndPassword(
      emailAddress: state.emailAddress,
      password: state.password,
    );
    emit(state.copyWith(
      isSubmitting: false,
      authFailureOrSuccessOption: Some(failureOrSuccess),
    ));
  }

  // Event handler for sending password reset email
  Future<void> _onForgotPassword(
      ForgotPassword event,
      Emitter<SignInState> emit,
      ) async {
    emit(state.copyWith(isSubmitting: true));
    final failureOrSuccess = await _authFacade.sendPasswordResetEmail(
      emailAddress: EmailAddress(event.email),
    );
    emit(state.copyWith(
      isSubmitting: false,
      passwordResetOption: Some(failureOrSuccess),
    ));
  }
}
