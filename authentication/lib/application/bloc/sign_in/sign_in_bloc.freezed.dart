// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sign_in_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SignInEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignInEventCopyWith<$Res> {
  factory $SignInEventCopyWith(
          SignInEvent value, $Res Function(SignInEvent) then) =
      _$SignInEventCopyWithImpl<$Res, SignInEvent>;
}

/// @nodoc
class _$SignInEventCopyWithImpl<$Res, $Val extends SignInEvent>
    implements $SignInEventCopyWith<$Res> {
  _$SignInEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$EmailChangedImplCopyWith<$Res> {
  factory _$$EmailChangedImplCopyWith(
          _$EmailChangedImpl value, $Res Function(_$EmailChangedImpl) then) =
      __$$EmailChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String emailStr});
}

/// @nodoc
class __$$EmailChangedImplCopyWithImpl<$Res>
    extends _$SignInEventCopyWithImpl<$Res, _$EmailChangedImpl>
    implements _$$EmailChangedImplCopyWith<$Res> {
  __$$EmailChangedImplCopyWithImpl(
      _$EmailChangedImpl _value, $Res Function(_$EmailChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailStr = null,
  }) {
    return _then(_$EmailChangedImpl(
      null == emailStr
          ? _value.emailStr
          : emailStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$EmailChangedImpl implements EmailChanged {
  const _$EmailChangedImpl(this.emailStr);

  @override
  final String emailStr;

  @override
  String toString() {
    return 'SignInEvent.emailChanged(emailStr: $emailStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmailChangedImpl &&
            (identical(other.emailStr, emailStr) ||
                other.emailStr == emailStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, emailStr);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmailChangedImplCopyWith<_$EmailChangedImpl> get copyWith =>
      __$$EmailChangedImplCopyWithImpl<_$EmailChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    return emailChanged(emailStr);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    return emailChanged?.call(emailStr);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (emailChanged != null) {
      return emailChanged(emailStr);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    return emailChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    return emailChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (emailChanged != null) {
      return emailChanged(this);
    }
    return orElse();
  }
}

abstract class EmailChanged implements SignInEvent {
  const factory EmailChanged(final String emailStr) = _$EmailChangedImpl;

  String get emailStr;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmailChangedImplCopyWith<_$EmailChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PasswordChangedImplCopyWith<$Res> {
  factory _$$PasswordChangedImplCopyWith(_$PasswordChangedImpl value,
          $Res Function(_$PasswordChangedImpl) then) =
      __$$PasswordChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String passwordStr});
}

/// @nodoc
class __$$PasswordChangedImplCopyWithImpl<$Res>
    extends _$SignInEventCopyWithImpl<$Res, _$PasswordChangedImpl>
    implements _$$PasswordChangedImplCopyWith<$Res> {
  __$$PasswordChangedImplCopyWithImpl(
      _$PasswordChangedImpl _value, $Res Function(_$PasswordChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? passwordStr = null,
  }) {
    return _then(_$PasswordChangedImpl(
      null == passwordStr
          ? _value.passwordStr
          : passwordStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PasswordChangedImpl implements PasswordChanged {
  const _$PasswordChangedImpl(this.passwordStr);

  @override
  final String passwordStr;

  @override
  String toString() {
    return 'SignInEvent.passwordChanged(passwordStr: $passwordStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PasswordChangedImpl &&
            (identical(other.passwordStr, passwordStr) ||
                other.passwordStr == passwordStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passwordStr);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PasswordChangedImplCopyWith<_$PasswordChangedImpl> get copyWith =>
      __$$PasswordChangedImplCopyWithImpl<_$PasswordChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    return passwordChanged(passwordStr);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    return passwordChanged?.call(passwordStr);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (passwordChanged != null) {
      return passwordChanged(passwordStr);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    return passwordChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    return passwordChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (passwordChanged != null) {
      return passwordChanged(this);
    }
    return orElse();
  }
}

abstract class PasswordChanged implements SignInEvent {
  const factory PasswordChanged(final String passwordStr) =
      _$PasswordChangedImpl;

  String get passwordStr;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PasswordChangedImplCopyWith<_$PasswordChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ToggleObscureTextImplCopyWith<$Res> {
  factory _$$ToggleObscureTextImplCopyWith(_$ToggleObscureTextImpl value,
          $Res Function(_$ToggleObscureTextImpl) then) =
      __$$ToggleObscureTextImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ToggleObscureTextImplCopyWithImpl<$Res>
    extends _$SignInEventCopyWithImpl<$Res, _$ToggleObscureTextImpl>
    implements _$$ToggleObscureTextImplCopyWith<$Res> {
  __$$ToggleObscureTextImplCopyWithImpl(_$ToggleObscureTextImpl _value,
      $Res Function(_$ToggleObscureTextImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ToggleObscureTextImpl implements ToggleObscureText {
  const _$ToggleObscureTextImpl();

  @override
  String toString() {
    return 'SignInEvent.toggleObscureText()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ToggleObscureTextImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    return toggleObscureText();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    return toggleObscureText?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (toggleObscureText != null) {
      return toggleObscureText();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    return toggleObscureText(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    return toggleObscureText?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (toggleObscureText != null) {
      return toggleObscureText(this);
    }
    return orElse();
  }
}

abstract class ToggleObscureText implements SignInEvent {
  const factory ToggleObscureText() = _$ToggleObscureTextImpl;
}

/// @nodoc
abstract class _$$ForgotPasswordImplCopyWith<$Res> {
  factory _$$ForgotPasswordImplCopyWith(_$ForgotPasswordImpl value,
          $Res Function(_$ForgotPasswordImpl) then) =
      __$$ForgotPasswordImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String email});
}

/// @nodoc
class __$$ForgotPasswordImplCopyWithImpl<$Res>
    extends _$SignInEventCopyWithImpl<$Res, _$ForgotPasswordImpl>
    implements _$$ForgotPasswordImplCopyWith<$Res> {
  __$$ForgotPasswordImplCopyWithImpl(
      _$ForgotPasswordImpl _value, $Res Function(_$ForgotPasswordImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
  }) {
    return _then(_$ForgotPasswordImpl(
      null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ForgotPasswordImpl implements ForgotPassword {
  const _$ForgotPasswordImpl(this.email);

  @override
  final String email;

  @override
  String toString() {
    return 'SignInEvent.forgotPassword(email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForgotPasswordImpl &&
            (identical(other.email, email) || other.email == email));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ForgotPasswordImplCopyWith<_$ForgotPasswordImpl> get copyWith =>
      __$$ForgotPasswordImplCopyWithImpl<_$ForgotPasswordImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    return forgotPassword(email);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    return forgotPassword?.call(email);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (forgotPassword != null) {
      return forgotPassword(email);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    return forgotPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    return forgotPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (forgotPassword != null) {
      return forgotPassword(this);
    }
    return orElse();
  }
}

abstract class ForgotPassword implements SignInEvent {
  const factory ForgotPassword(final String email) = _$ForgotPasswordImpl;

  String get email;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ForgotPasswordImplCopyWith<_$ForgotPasswordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignInWithGoogleImplCopyWith<$Res> {
  factory _$$SignInWithGoogleImplCopyWith(_$SignInWithGoogleImpl value,
          $Res Function(_$SignInWithGoogleImpl) then) =
      __$$SignInWithGoogleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SignInWithGoogleImplCopyWithImpl<$Res>
    extends _$SignInEventCopyWithImpl<$Res, _$SignInWithGoogleImpl>
    implements _$$SignInWithGoogleImplCopyWith<$Res> {
  __$$SignInWithGoogleImplCopyWithImpl(_$SignInWithGoogleImpl _value,
      $Res Function(_$SignInWithGoogleImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SignInWithGoogleImpl implements SignInWithGoogle {
  const _$SignInWithGoogleImpl();

  @override
  String toString() {
    return 'SignInEvent.signInWithGoogle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SignInWithGoogleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    return signInWithGoogle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    return signInWithGoogle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (signInWithGoogle != null) {
      return signInWithGoogle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    return signInWithGoogle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    return signInWithGoogle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (signInWithGoogle != null) {
      return signInWithGoogle(this);
    }
    return orElse();
  }
}

abstract class SignInWithGoogle implements SignInEvent {
  const factory SignInWithGoogle() = _$SignInWithGoogleImpl;
}

/// @nodoc
abstract class _$$SignInWithAppleImplCopyWith<$Res> {
  factory _$$SignInWithAppleImplCopyWith(_$SignInWithAppleImpl value,
          $Res Function(_$SignInWithAppleImpl) then) =
      __$$SignInWithAppleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SignInWithAppleImplCopyWithImpl<$Res>
    extends _$SignInEventCopyWithImpl<$Res, _$SignInWithAppleImpl>
    implements _$$SignInWithAppleImplCopyWith<$Res> {
  __$$SignInWithAppleImplCopyWithImpl(
      _$SignInWithAppleImpl _value, $Res Function(_$SignInWithAppleImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SignInWithAppleImpl implements SignInWithApple {
  const _$SignInWithAppleImpl();

  @override
  String toString() {
    return 'SignInEvent.signInWithApple()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SignInWithAppleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    return signInWithApple();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    return signInWithApple?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (signInWithApple != null) {
      return signInWithApple();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    return signInWithApple(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    return signInWithApple?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (signInWithApple != null) {
      return signInWithApple(this);
    }
    return orElse();
  }
}

abstract class SignInWithApple implements SignInEvent {
  const factory SignInWithApple() = _$SignInWithAppleImpl;
}

/// @nodoc
abstract class _$$SignInWithEmailImplCopyWith<$Res> {
  factory _$$SignInWithEmailImplCopyWith(_$SignInWithEmailImpl value,
          $Res Function(_$SignInWithEmailImpl) then) =
      __$$SignInWithEmailImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String email, String password});
}

/// @nodoc
class __$$SignInWithEmailImplCopyWithImpl<$Res>
    extends _$SignInEventCopyWithImpl<$Res, _$SignInWithEmailImpl>
    implements _$$SignInWithEmailImplCopyWith<$Res> {
  __$$SignInWithEmailImplCopyWithImpl(
      _$SignInWithEmailImpl _value, $Res Function(_$SignInWithEmailImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? password = null,
  }) {
    return _then(_$SignInWithEmailImpl(
      null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SignInWithEmailImpl implements SignInWithEmail {
  const _$SignInWithEmailImpl(this.email, this.password);

  @override
  final String email;
  @override
  final String password;

  @override
  String toString() {
    return 'SignInEvent.signInWithEmail(email: $email, password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignInWithEmailImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email, password);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignInWithEmailImplCopyWith<_$SignInWithEmailImpl> get copyWith =>
      __$$SignInWithEmailImplCopyWithImpl<_$SignInWithEmailImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    return signInWithEmail(email, password);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    return signInWithEmail?.call(email, password);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (signInWithEmail != null) {
      return signInWithEmail(email, password);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    return signInWithEmail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    return signInWithEmail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (signInWithEmail != null) {
      return signInWithEmail(this);
    }
    return orElse();
  }
}

abstract class SignInWithEmail implements SignInEvent {
  const factory SignInWithEmail(final String email, final String password) =
      _$SignInWithEmailImpl;

  String get email;
  String get password;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignInWithEmailImplCopyWith<_$SignInWithEmailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RegisterWithEmailImplCopyWith<$Res> {
  factory _$$RegisterWithEmailImplCopyWith(_$RegisterWithEmailImpl value,
          $Res Function(_$RegisterWithEmailImpl) then) =
      __$$RegisterWithEmailImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String email, String password});
}

/// @nodoc
class __$$RegisterWithEmailImplCopyWithImpl<$Res>
    extends _$SignInEventCopyWithImpl<$Res, _$RegisterWithEmailImpl>
    implements _$$RegisterWithEmailImplCopyWith<$Res> {
  __$$RegisterWithEmailImplCopyWithImpl(_$RegisterWithEmailImpl _value,
      $Res Function(_$RegisterWithEmailImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? password = null,
  }) {
    return _then(_$RegisterWithEmailImpl(
      null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RegisterWithEmailImpl implements RegisterWithEmail {
  const _$RegisterWithEmailImpl(this.email, this.password);

  @override
  final String email;
  @override
  final String password;

  @override
  String toString() {
    return 'SignInEvent.registerWithEmail(email: $email, password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterWithEmailImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email, password);

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterWithEmailImplCopyWith<_$RegisterWithEmailImpl> get copyWith =>
      __$$RegisterWithEmailImplCopyWithImpl<_$RegisterWithEmailImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    return registerWithEmail(email, password);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    return registerWithEmail?.call(email, password);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (registerWithEmail != null) {
      return registerWithEmail(email, password);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    return registerWithEmail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    return registerWithEmail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    if (registerWithEmail != null) {
      return registerWithEmail(this);
    }
    return orElse();
  }
}

abstract class RegisterWithEmail implements SignInEvent {
  const factory RegisterWithEmail(final String email, final String password) =
      _$RegisterWithEmailImpl;

  String get email;
  String get password;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterWithEmailImplCopyWith<_$RegisterWithEmailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SignInState {
  EmailAddress get emailAddress =>
      throw _privateConstructorUsedError; // The email address entered by the user
  Password get password =>
      throw _privateConstructorUsedError; // The password entered by the user
  bool get isSubmitting =>
      throw _privateConstructorUsedError; // Indicates if the authentication process is in progress
  bool get obscureText =>
      throw _privateConstructorUsedError; // Indicates if the password field should show as obscured or not
  Option<Either<AuthFailure, Unit>> get passwordResetOption =>
      throw _privateConstructorUsedError; // Option for handling password reset failures or successes
  Option<Either<AuthFailure, UserModel>> get authFailureOrSuccessOption =>
      throw _privateConstructorUsedError;

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SignInStateCopyWith<SignInState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignInStateCopyWith<$Res> {
  factory $SignInStateCopyWith(
          SignInState value, $Res Function(SignInState) then) =
      _$SignInStateCopyWithImpl<$Res, SignInState>;
  @useResult
  $Res call(
      {EmailAddress emailAddress,
      Password password,
      bool isSubmitting,
      bool obscureText,
      Option<Either<AuthFailure, Unit>> passwordResetOption,
      Option<Either<AuthFailure, UserModel>> authFailureOrSuccessOption});
}

/// @nodoc
class _$SignInStateCopyWithImpl<$Res, $Val extends SignInState>
    implements $SignInStateCopyWith<$Res> {
  _$SignInStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailAddress = null,
    Object? password = null,
    Object? isSubmitting = null,
    Object? obscureText = null,
    Object? passwordResetOption = null,
    Object? authFailureOrSuccessOption = null,
  }) {
    return _then(_value.copyWith(
      emailAddress: null == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as EmailAddress,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as Password,
      isSubmitting: null == isSubmitting
          ? _value.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
      obscureText: null == obscureText
          ? _value.obscureText
          : obscureText // ignore: cast_nullable_to_non_nullable
              as bool,
      passwordResetOption: null == passwordResetOption
          ? _value.passwordResetOption
          : passwordResetOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<AuthFailure, Unit>>,
      authFailureOrSuccessOption: null == authFailureOrSuccessOption
          ? _value.authFailureOrSuccessOption
          : authFailureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<AuthFailure, UserModel>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SignInStateImplCopyWith<$Res>
    implements $SignInStateCopyWith<$Res> {
  factory _$$SignInStateImplCopyWith(
          _$SignInStateImpl value, $Res Function(_$SignInStateImpl) then) =
      __$$SignInStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {EmailAddress emailAddress,
      Password password,
      bool isSubmitting,
      bool obscureText,
      Option<Either<AuthFailure, Unit>> passwordResetOption,
      Option<Either<AuthFailure, UserModel>> authFailureOrSuccessOption});
}

/// @nodoc
class __$$SignInStateImplCopyWithImpl<$Res>
    extends _$SignInStateCopyWithImpl<$Res, _$SignInStateImpl>
    implements _$$SignInStateImplCopyWith<$Res> {
  __$$SignInStateImplCopyWithImpl(
      _$SignInStateImpl _value, $Res Function(_$SignInStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailAddress = null,
    Object? password = null,
    Object? isSubmitting = null,
    Object? obscureText = null,
    Object? passwordResetOption = null,
    Object? authFailureOrSuccessOption = null,
  }) {
    return _then(_$SignInStateImpl(
      emailAddress: null == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as EmailAddress,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as Password,
      isSubmitting: null == isSubmitting
          ? _value.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
      obscureText: null == obscureText
          ? _value.obscureText
          : obscureText // ignore: cast_nullable_to_non_nullable
              as bool,
      passwordResetOption: null == passwordResetOption
          ? _value.passwordResetOption
          : passwordResetOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<AuthFailure, Unit>>,
      authFailureOrSuccessOption: null == authFailureOrSuccessOption
          ? _value.authFailureOrSuccessOption
          : authFailureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<AuthFailure, UserModel>>,
    ));
  }
}

/// @nodoc

class _$SignInStateImpl implements _SignInState {
  const _$SignInStateImpl(
      {required this.emailAddress,
      required this.password,
      required this.isSubmitting,
      required this.obscureText,
      required this.passwordResetOption,
      required this.authFailureOrSuccessOption});

  @override
  final EmailAddress emailAddress;
// The email address entered by the user
  @override
  final Password password;
// The password entered by the user
  @override
  final bool isSubmitting;
// Indicates if the authentication process is in progress
  @override
  final bool obscureText;
// Indicates if the password field should show as obscured or not
  @override
  final Option<Either<AuthFailure, Unit>> passwordResetOption;
// Option for handling password reset failures or successes
  @override
  final Option<Either<AuthFailure, UserModel>> authFailureOrSuccessOption;

  @override
  String toString() {
    return 'SignInState(emailAddress: $emailAddress, password: $password, isSubmitting: $isSubmitting, obscureText: $obscureText, passwordResetOption: $passwordResetOption, authFailureOrSuccessOption: $authFailureOrSuccessOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignInStateImpl &&
            (identical(other.emailAddress, emailAddress) ||
                other.emailAddress == emailAddress) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting) &&
            (identical(other.obscureText, obscureText) ||
                other.obscureText == obscureText) &&
            (identical(other.passwordResetOption, passwordResetOption) ||
                other.passwordResetOption == passwordResetOption) &&
            (identical(other.authFailureOrSuccessOption,
                    authFailureOrSuccessOption) ||
                other.authFailureOrSuccessOption ==
                    authFailureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      emailAddress,
      password,
      isSubmitting,
      obscureText,
      passwordResetOption,
      authFailureOrSuccessOption);

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignInStateImplCopyWith<_$SignInStateImpl> get copyWith =>
      __$$SignInStateImplCopyWithImpl<_$SignInStateImpl>(this, _$identity);
}

abstract class _SignInState implements SignInState {
  const factory _SignInState(
      {required final EmailAddress emailAddress,
      required final Password password,
      required final bool isSubmitting,
      required final bool obscureText,
      required final Option<Either<AuthFailure, Unit>> passwordResetOption,
      required final Option<Either<AuthFailure, UserModel>>
          authFailureOrSuccessOption}) = _$SignInStateImpl;

  @override
  EmailAddress get emailAddress; // The email address entered by the user
  @override
  Password get password; // The password entered by the user
  @override
  bool
      get isSubmitting; // Indicates if the authentication process is in progress
  @override
  bool
      get obscureText; // Indicates if the password field should show as obscured or not
  @override
  Option<Either<AuthFailure, Unit>>
      get passwordResetOption; // Option for handling password reset failures or successes
  @override
  Option<Either<AuthFailure, UserModel>> get authFailureOrSuccessOption;

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignInStateImplCopyWith<_$SignInStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
