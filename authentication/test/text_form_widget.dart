import 'package:authentication/widgets/text_form_feild.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MyTextFormField', () {
    // This test ensures that the MyTextFormField is created correctly with the provided values.
    testWidgets('MyTextForm<PERSON>ield creates correct widget', (WidgetTester tester) async {
      // Build the MyTextFormField widget
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: MyTextFormField(
            hintText: 'Test Hint',
            heading: 'Test Heading',
            isMandatory: true,
            isPassword: false,
            isEmail: false,
            isEnabled: true,
          ),
        ),
      ));

      // Verify that the hintText is displayed
      expect(find.text('Test Hint'), findsOneWidget);
      // Verify that the heading is displayed
      expect(find.text('Test Heading'), findsOneWidget);
    });
  });
}