import 'package:authentication/presentation/register_with_email_wrapper.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:authentication/application/bloc/sign_in/sign_in_bloc.dart';

class MockSignInBloc extends Mock implements SignInBloc {
  @override
  Future<void> close() async {
    return super.noSuchMethod(
      Invocation.method(#close, []),

    );
  }
}

class FakeSignInEvent extends Fake implements SignInEvent {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeSignInState());
    registerFallbackValue(FakeSignInEvent());
  });

  late MockSignInBloc mockSignInBloc;

  setUp(() {
    mockSignInBloc = MockSignInBloc();
    when(() => mockSignInBloc.state).thenReturn(SignInState.initial());
  });

  testWidgets('EmailRegisterWrapper triggers registration on form submission', (WidgetTester tester) async {
    when(() => mockSignInBloc.add(any())).thenReturn(null);
    whenListen(
        mockSignInBloc,
        Stream.fromIterable([SignInState.initial()]), // Mock the stream of states
        initialState: SignInState.initial()
    );
    var registerSuccessCalled = false;
    var registerFailureCalled = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: BlocProvider<SignInBloc>(
            create: (_) => mockSignInBloc,
            child: EmailRegisterWrapper(
              child: Text('Sign up'),
              onRegisterSuccess: (_) => registerSuccessCalled = true,
              onRegisterFailure: (_) => registerFailureCalled = true,
            ),
          ),
        ),
      ),
    );

    // Simulate form filling and submission
    await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
    await tester.enterText(find.byType(TextFormField).at(1), 'password123');
    await tester.tap(find.text('Sign up'));
    await tester.pump();

    // Assert that the SignInBloc received the correct event
    verify(() => mockSignInBloc.add(any(that: isA<RegisterWithEmail>()))).called(1);
    // Further checks can be added here based on expected behavior and state changes
  });
}

class FakeSignInState extends Fake implements SignInState {}
