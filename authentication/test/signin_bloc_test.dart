import 'package:authentication/domain/facade/i_auth_facade.dart';
import 'package:authentication/domain/failure/auth_failure.dart';
import 'package:authentication/domain/model/user_model.dart';
import 'package:authentication/domain/model/value_objects.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:authentication/authentication.dart';
import 'package:fpdart/fpdart.dart';
import 'package:mocktail/mocktail.dart';

class MockSignInBloc extends MockBloc<SignInEvent, SignInState> implements SignInBloc {}
class MockAuthFacade extends Mock implements IAuthFacade {}
class MockUserModel extends Mock implements UserModel {}

void main() {
  group('SignInBloc', () {
    late SignInBloc signInBloc;
    late MockAuthFacade mockAuthFacade;
    late UserModel mockUserModel;

    setUp(() {
      mockAuthFacade = MockAuthFacade();
      mockUserModel = MockUserModel();
      signInBloc = SignInBloc(mockAuthFacade);
      registerFallbackValue(EmailAddress(''));
      registerFallbackValue(Password(''));  // Register a fallback value for Password

    });
    // This test ensures that when no event is added to the SignInBloc, it does not emit any states.
    blocTest<SignInBloc, SignInState>(
      'emits [] when nothing is added',
      build: () => signInBloc,
      expect: () => [],
    );

    // This test ensures that when the emailChanged event is added to the SignInBloc,
    // the bloc emits a new SignInState with the updated email address.
    blocTest<SignInBloc, SignInState>(
      'emits [SignInState] when SignInEvent.emailChanged is added',
      build: () => signInBloc,
      act: (bloc) => bloc.add(SignInEvent.emailChanged('<EMAIL>')),
      expect: () =>
      [
        SignInState(
          emailAddress: EmailAddress('<EMAIL>'),
          password: Password(''),
          isSubmitting: false,
          obscureText: true,
          authFailureOrSuccessOption: None(),
          passwordResetOption: None(),
        ),

      ],
    );

    // This test ensures that when the passwordChanged event is added to the SignInBloc,
    // the bloc emits a new SignInState with the updated password.
    blocTest<SignInBloc, SignInState>(
      'emits [SignInState] when SignInEvent.passwordChanged is added',
      build: () => signInBloc,
      act: (bloc) => bloc.add(SignInEvent.passwordChanged('password123')),
      expect: () =>
      [
        SignInState(
          emailAddress: EmailAddress(''),
          password: Password('password123'),
          isSubmitting: false,
          obscureText: true,
          authFailureOrSuccessOption: None(),
          passwordResetOption: None(),
        ),
      ],
    );
    blocTest<SignInBloc, SignInState>(
      'emits [SignInState] when SignInEvent.SignInWithGoogle is added',
      build: () {
        when(() => mockAuthFacade.signInWithGoogle()).thenAnswer((_) async =>
            Right(mockUserModel));
        return signInBloc;
      },
      act: (bloc) => bloc.add(SignInEvent.signInWithGoogle()),
      expect: () =>
      [
        SignInState(
          emailAddress: EmailAddress(''),
          password: Password(''),
          isSubmitting: true,
          obscureText: true,
          authFailureOrSuccessOption: None(),
          passwordResetOption: None(),
        ),
        SignInState(
          emailAddress: EmailAddress(''),
          password: Password(''),
          isSubmitting: false,
          obscureText: true,
          authFailureOrSuccessOption: Some(Right(mockUserModel)),
          passwordResetOption: None(),
        ),


      ],
    );

    blocTest<SignInBloc, SignInState>(
      'emits [SignInState] when SignInEvent.SignInWithApple is added',
      build: () {
        when(() => mockAuthFacade.signInWithApple()).thenAnswer((_) async =>
            Right(mockUserModel));
        return signInBloc;
      },
      act: (bloc) => bloc.add(SignInEvent.signInWithApple()),
      expect: () =>
      [
        SignInState(
          emailAddress: EmailAddress(''),
          password: Password(''),
          isSubmitting: true,
          obscureText: true,
          authFailureOrSuccessOption: None(),
          passwordResetOption: None(),
        ),
        SignInState(
          emailAddress: EmailAddress(''),
          password: Password(''),
          isSubmitting: false,
          obscureText: true,
          authFailureOrSuccessOption: Some(Right(mockUserModel)),
          passwordResetOption: None(),
        ),
      ],
    );

    blocTest<SignInBloc, SignInState>(
      'emits [SignInState] when SignInEvent.RegisterWithEmail is added',
      build: () {
        when(() =>
            mockAuthFacade.registerWithEmailAndPassword(
              emailAddress: any(named: 'emailAddress'),
              password: any(named: 'password'),
            )).thenAnswer((_) async => Right(mockUserModel));
        return signInBloc;
      },
      act: (bloc) =>
          bloc.add(SignInEvent.registerWithEmail('<EMAIL>',
            'password123',)),
      expect: () =>
      [
        SignInState(
          emailAddress: EmailAddress(''),
          password: Password(''),
          isSubmitting: true,
          obscureText: true,
          authFailureOrSuccessOption: None(),
          passwordResetOption: None(),
        ),
        SignInState(
          emailAddress: EmailAddress(''),
          password: Password(''),
          isSubmitting: false,
          obscureText: true,
          authFailureOrSuccessOption: Some(Right(mockUserModel)),
          passwordResetOption: None(),
        ),
      ],
    );
blocTest<SignInBloc, SignInState>(
  'emits [SignInState] when SignInEvent.SignInWithEmail is added',
  build: () {
    when(() => mockAuthFacade.signInWithEmailAndPassword(
      emailAddress: any(named: 'emailAddress'),
      password: any(named: 'password'),
    )).thenAnswer((_) async => Right(mockUserModel));
    return signInBloc;
  },
  act: (bloc) => bloc.add(SignInEvent.signInWithEmail('<EMAIL>', 'password123')),
  expect: () => [
    SignInState(
      emailAddress: EmailAddress(''),
      password: Password(''),
      isSubmitting: true,
      obscureText: true,
      authFailureOrSuccessOption: None(),
      passwordResetOption: None(),
    ),
    SignInState(
      emailAddress: EmailAddress(''),
      password: Password(''),
      isSubmitting: false,
      obscureText: true,
      authFailureOrSuccessOption: Some(Right(mockUserModel)),
      passwordResetOption: None(),
    ),
  ],
);
blocTest<SignInBloc, SignInState>(
  'emits [SignInState] with failure when SignInEvent.SignInWithEmail is added and signInWithEmailAndPassword fails',
  build: () {
    when(() => mockAuthFacade.signInWithEmailAndPassword(
      emailAddress: any(named: 'emailAddress'),
      password: any(named: 'password'),
    )).thenAnswer((_) async => Left(AuthFailure.emailAlreadyInUse()));
    return signInBloc;
  },
  act: (bloc) => bloc.add(SignInEvent.signInWithEmail('<EMAIL>', 'password123')),
  expect: () => [
    SignInState(
      emailAddress: EmailAddress(''),
      password: Password(''),
      isSubmitting: true,
      obscureText: true,
      authFailureOrSuccessOption: None(),
      passwordResetOption: None(),
    ),
    SignInState(
      emailAddress: EmailAddress(''),
      password: Password(''),
      isSubmitting: false,
      obscureText: true,
      authFailureOrSuccessOption: Some(Left(AuthFailure.emailAlreadyInUse())),
      passwordResetOption: None(),
    ),
  ],
);
blocTest<SignInBloc, SignInState>(
  'emits [SignInState] when SignInEvent.ForgotPassword is added',
  build: () {
    when(() => mockAuthFacade.sendPasswordResetEmail(
      emailAddress: any(named: 'emailAddress'),
    )).thenAnswer((_) async => Right(unit));
    return signInBloc;
  },
  act: (bloc) => bloc.add(SignInEvent.forgotPassword('<EMAIL>')),
  expect: () => [
    SignInState(
      emailAddress: EmailAddress(''),
      password: Password(''),
      isSubmitting: true,
      obscureText: true,
      authFailureOrSuccessOption: None(),
      passwordResetOption: None(),
    ),
    SignInState(
      emailAddress: EmailAddress(''),
      password: Password(''),
      isSubmitting: false,
      obscureText: true,
      authFailureOrSuccessOption: None(),
      passwordResetOption: Some(Right(unit)),
    ),
  ],
);
    blocTest<SignInBloc, SignInState>(
      'emits [SignInState] when SignInEvent.ToggleObscureText is added',
      build: () => signInBloc,
      act: (bloc) => bloc.add(SignInEvent.toggleObscureText()),
      expect: () =>
      [
        SignInState(
          emailAddress: EmailAddress(''),
          password: Password(''),
          isSubmitting: false,
          obscureText: false,
          authFailureOrSuccessOption: None(),
          passwordResetOption: None(),
        ),
      ],
    );
  });


  group('SignInEvent', () {
    // This test ensures that the EmailChanged event is created correctly with the provided email string.
    test('EmailChanged creates correct event', () {
      final event = SignInEvent.emailChanged('<EMAIL>');
      final email = event.map(
        emailChanged: (event) => event.emailStr,
        passwordChanged: (_) => null,
        toggleObscureText: (_) => null,
        forgotPassword: (_) => null,
        signInWithGoogle: (_) => null,
        signInWithApple: (_) => null,
        signInWithEmail: (_) => null,
        registerWithEmail: (_) => null,
      );
      expect(email, '<EMAIL>');
    });

    // This test ensures that the PasswordChanged event is created correctly with the provided password string.
    test('PasswordChanged creates correct event', () {
      final event = SignInEvent.passwordChanged('password123');
      final password = event.map(
        emailChanged: (_) => null,
        passwordChanged: (event) => event.passwordStr,
        toggleObscureText: (_) => null,
        forgotPassword: (_) => null,
        signInWithGoogle: (_) => null,
        signInWithApple: (_) => null,
        signInWithEmail: (_) => null,
        registerWithEmail: (_) => null,
      );
      expect(password, 'password123');
    });

    // This test ensures that the ForgotPassword event is created correctly with the provided email string.
    test('ForgotPassword creates correct event', () {
      final event = SignInEvent.forgotPassword('<EMAIL>');
      final email = event.map(
        emailChanged: (_) => null,
        passwordChanged: (_) => null,
        toggleObscureText: (_) => null,
        forgotPassword: (event) => event.email,
        signInWithGoogle: (_) => null,
        signInWithApple: (_) => null,
        signInWithEmail: (_) => null,
        registerWithEmail: (_) => null,
      );
      expect(email, '<EMAIL>');
    });

    // This test ensures that the SignInWithEmail event is created correctly with the provided email and password strings.
    test('SignInWithEmail creates correct event', () {
      final event = SignInEvent.signInWithEmail('<EMAIL>', 'password123');
      final emailAndPassword = event.map(
        emailChanged: (_) => null,
        passwordChanged: (_) => null,
        toggleObscureText: (_) => null,
        forgotPassword: (_) => null,
        signInWithGoogle: (_) => null,
        signInWithApple: (_) => null,
        signInWithEmail: (event) => event.email,
        // Return the email string
        registerWithEmail: (event) => null,
      );
      expect(emailAndPassword, '<EMAIL>'); // Expect the email string
    });

    // This test ensures that the RegisterWithEmail event is created correctly with the provided email and password strings.
    test('RegisterWithEmail creates correct event', () {
      final event = SignInEvent.registerWithEmail(
          '<EMAIL>', 'password123');
      final emailAndPassword = event.map(
        emailChanged: (_) => null,
        passwordChanged: (_) => null,
        toggleObscureText: (_) => null,
        forgotPassword: (_) => null,
        signInWithGoogle: (_) => null,
        signInWithApple: (_) => null,
        signInWithEmail: (_) => null,
        registerWithEmail: (event) => event.email, // Return the email string
      );
      expect(emailAndPassword, '<EMAIL>'); // Expect the email string
    });
  });


  group('SignInState', () {
    // This test ensures that the SignInState.initial state is created correctly with the initial values.
    test('SignInState.initial creates correct initial state', () {
      final state = SignInState.initial();
      expect(state.emailAddress, EmailAddress(''));  // Initial email address is empty
      expect(state.password, Password(''));  // Initial password is empty
      expect(state.isSubmitting, false);  // Not submitting by default
      expect(state.obscureText, true);  // Default value for password fields is obscured
      expect(state.authFailureOrSuccessOption, None());  // No authentication failure or success initially
      expect(state.passwordResetOption, None());  // No password reset option initially
    });

    // This test ensures that the SignInState is created correctly with the provided values.
    test('SignInState creates correct state', () {
      final state = SignInState(
        emailAddress: EmailAddress('<EMAIL>'),
        password: Password('password123'),
        isSubmitting: true,
        obscureText: false,
        authFailureOrSuccessOption: Some(Right(UserModel())),
        passwordResetOption: Some(Right(unit)),
      );
      expect(state.emailAddress, EmailAddress('<EMAIL>'));
      expect(state.password, Password('password123'));
      expect(state.isSubmitting, true);
      expect(state.obscureText, false);
      expect(state.authFailureOrSuccessOption, isA<Some<Either<AuthFailure, UserModel>>>());
      expect(state.passwordResetOption, Some(Right(unit)));
    });
  });
}
