import 'package:authentication/domain/model/user_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('UserModel', () {
    // This test ensures that the UserModel is created correctly with the provided values.
    test('UserModel creates correct model', () {
      final userModel = UserModel(
        uid: '123',
        userName: 'Test User',
        userEmail: '<EMAIL>',
      );
      expect(userModel.uid, '123');
      expect(userModel.userName, 'Test User');
      expect(userModel.userEmail, '<EMAIL>');
    });

    // This test ensures that the UserModel can be correctly serialized to JSON.
    test('UserModel serializes to JSON correctly', () {
      final userModel = UserModel(
        uid: '123',
        userName: 'Test User',
        userEmail: '<EMAIL>',
      );
      expect(userModel.toJson(), {
        'uid': '123',
        'userName': 'Test User',
        'userEmail': '<EMAIL>',
      });
    });

    // This test ensures that the UserModel can be correctly created from JSON.
    test('UserModel is created from JSON correctly', () {
      final json = {
        'uid': '123',
        'userName': 'Test User',
        'userEmail': '<EMAIL>',
      };
      final userModel = UserModel.fromJson(json);
      expect(userModel.uid, '123');
      expect(userModel.userName, 'Test User');
      expect(userModel.userEmail, '<EMAIL>');
    });
  });
}