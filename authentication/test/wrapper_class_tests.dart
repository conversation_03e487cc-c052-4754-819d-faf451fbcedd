import 'package:authentication/presentation/apple_signin_wrapper.dart';
import 'package:authentication/presentation/google_signin_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:authentication/application/bloc/sign_in/sign_in_bloc.dart';

import 'signin_bloc_test.dart';

// Define a Fake class for SignInEvent if necessary
class FakeSignInEvent extends Fake implements SignInEvent {}

void main() {
  setUpAll(() {
    // Register the FakeSignInEvent as a fallback value for SignInEvent type
    registerFallbackValue(FakeSignInEvent());
  });

  late MockSignInBloc mockSignInBloc;

  setUp(() {
    mockSignInBloc = MockSignInBloc();
  });

  testWidgets('Apple sign-in button dispatches signInWithApple event', (WidgetTester tester) async {
    // Arrange
    when(() => mockSignInBloc.state).thenReturn(SignInState.initial());
    when(() => mockSignInBloc.add(any())).thenReturn(null);

    // Act
    await tester.pumpWidget(MaterialApp(
      home: BlocProvider<SignInBloc>(
        create: (_) => mockSignInBloc,
        child: AppleSignInButtonWrapper(
          buttonChild: Text('Sign in with Apple'),
          onSignInSuccess: (_) {},
          onSignInFailure: (_) {},
        ),
      ),
    ));

    await tester.tap(find.text('Sign in with Apple'));
    await tester.pump();

    // Assert
    verify(() => mockSignInBloc.add(any(that: isA<SignInWithApple>()))).called(1);
  });
  testWidgets('Google sign-in button dispatches signInWithGoogle event', (WidgetTester tester) async {
    // Arrange
    when(() => mockSignInBloc.state).thenReturn(SignInState.initial());
    when(() => mockSignInBloc.add(any())).thenReturn(null);

    var signInSuccessCalled = false;
    var signInFailureCalled = false;

    // Act
    await tester.pumpWidget(MaterialApp(
      home: BlocProvider<SignInBloc>(
        create: (_) => mockSignInBloc,
        child: GoogleSignInButtonWrapper(
          buttonChild: Text('Sign in with Google'),
          onSignInSuccess: (_) => signInSuccessCalled = true,
          onSignInFailure: (_) => signInFailureCalled = true,
        ),
      ),
    ));

    await tester.tap(find.text('Sign in with Google'));
    await tester.pump();

    // Assert
    verify(() => mockSignInBloc.add(any(that: isA<SignInWithGoogle>()))).called(1);
    // You can also add checks for `signInSuccessCalled` or `signInFailureCalled` if needed
  });
}
