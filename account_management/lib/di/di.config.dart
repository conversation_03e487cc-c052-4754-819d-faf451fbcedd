// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:account_management/application/account_management_bloc/account_management_bloc.dart'
    as _i357;
import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart'
    as _i1051;
import 'package:account_management/application/manage_medications_bloc/manage_medications_bloc.dart'
    as _i270;
import 'package:account_management/application/manage_period_tracking_bloc/manage_period_tracking_bloc.dart'
    as _i703;
import 'package:account_management/application/medication_form_bloc/medication_form_bloc.dart'
    as _i644;
import 'package:account_management/application/medication_watcher_bloc/medication_watcher_bloc.dart'
    as _i957;
import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart'
    as _i880;
import 'package:account_management/application/onboardin_form_bloc/onboarding_form_bloc.dart'
    as _i712;
import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart'
    as _i689;
import 'package:account_management/application/update_health_data_bloc/update_health_data_bloc.dart'
    as _i1038;
import 'package:account_management/domain/facade/account_management_facade.dart'
    as _i380;
import 'package:account_management/domain/facade/health_data_facade.dart'
    as _i698;
import 'package:account_management/domain/facade/medication_facade.dart'
    as _i717;
import 'package:account_management/domain/facade/period_tracking_facade.dart'
    as _i258;
import 'package:account_management/repository/account_management_repository.dart'
    as _i795;
import 'package:account_management/repository/health_data_repository.dart'
    as _i56;
import 'package:account_management/repository/medication_repository.dart'
    as _i467;
import 'package:account_management/repository/period_tracking_repository.dart'
    as _i201;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.lazySingleton<_i258.PeriodTrackingFacade>(
        () => _i201.PeriodTrackingRepository());
    gh.lazySingleton<_i380.AccountManagementFacade>(
        () => _i795.AccountManagementRepository());
    gh.lazySingleton<_i698.HealthDataFacade>(() => _i56.HealthDataRepository());
    gh.lazySingleton<_i717.MedicationFacade>(
        () => _i467.MedicationRepository());
    gh.factory<_i270.ManageMedicationsBloc>(
        () => _i270.ManageMedicationsBloc(gh<_i717.MedicationFacade>()));
    gh.factory<_i957.MedicationWatcherBloc>(
        () => _i957.MedicationWatcherBloc(gh<_i717.MedicationFacade>()));
    gh.factory<_i644.MedicationFormBloc>(
        () => _i644.MedicationFormBloc(gh<_i717.MedicationFacade>()));
    gh.factory<_i880.MenstrualCycleBloc>(
        () => _i880.MenstrualCycleBloc(gh<_i258.PeriodTrackingFacade>()));
    gh.factory<_i689.PeriodTrackingWatcherBloc>(() =>
        _i689.PeriodTrackingWatcherBloc(gh<_i258.PeriodTrackingFacade>()));
    gh.factory<_i703.ManagePeriodTrackingBloc>(
        () => _i703.ManagePeriodTrackingBloc(gh<_i258.PeriodTrackingFacade>()));
    gh.factory<_i1051.AccountWatcherBloc>(
        () => _i1051.AccountWatcherBloc(gh<_i380.AccountManagementFacade>()));
    gh.factory<_i712.OnboardingFormBloc>(
        () => _i712.OnboardingFormBloc(gh<_i380.AccountManagementFacade>()));
    gh.factory<_i357.AccountManagementBloc>(
        () => _i357.AccountManagementBloc(gh<_i380.AccountManagementFacade>()));
    gh.factory<_i1038.UpdateHealthDataBloc>(
        () => _i1038.UpdateHealthDataBloc(gh<_i698.HealthDataFacade>()));
    return this;
  }
}
