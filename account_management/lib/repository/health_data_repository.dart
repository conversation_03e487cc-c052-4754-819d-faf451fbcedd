import 'package:account_management/domain/failure/health_data_failure.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/src/effect.dart';
import 'package:fpdart/src/unit.dart';
import 'package:injectable/injectable.dart';
import 'package:account_management/domain/facade/health_data_facade.dart';

@LazySingleton(as: HealthDataFacade)
class HealthDataRepository implements HealthDataFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  @override
  Future<Either<HealthDataFailure, Unit>> updateContraceptionType(
      String contraceptionType) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc
          .update({'healthData.contraceptionType': contraceptionType}).then(
              (_) => const Right(unit));
    } on FirebaseException catch (e) {
      return Left(HealthDataFailure.contraceptionTypeFailure());
    }
  }

  @override
  Future<Either<HealthDataFailure, Unit>> updateCycleLength(
      int cycleLength) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({'healthData.cycleLength': cycleLength}).then(
          (_) => const Right(unit));
    } on FirebaseException catch (e) {
      return Left(HealthDataFailure.cycleLengthFailure());
    }
  }

  @override
  Future<Either<HealthDataFailure, Unit>> updateOvulationDate(
      DateTime ovulationDate) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({
        'healthData.ovulationDate': Timestamp.fromDate(ovulationDate)
      }).then((_) => const Right(unit));
    } on FirebaseException catch (e) {
      return Left(HealthDataFailure.ovulationDateFailure());
    }
  }

  @override
  Future<Either<HealthDataFailure, Unit>> updatePeriodLength(
      int periodLength) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({'healthData.periodLength': periodLength}).then(
          (_) => const Right(unit));
    } on FirebaseException catch (e) {
      return Left(HealthDataFailure.periodLengthFailure());
    }
  }

  @override
  Future<Either<HealthDataFailure, Unit>> updateNextPeriodStartDate(
      DateTime nextPeriodStartDate) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({
        'healthData.nextPeriodStartDate':
            Timestamp.fromDate(nextPeriodStartDate)
      }).then((_) => const Right(unit));
    } on FirebaseException catch (e) {
      return Left(HealthDataFailure.ovulationDateFailure());
    }
  }

  @override
  Future<Either<HealthDataFailure, Unit>> updateNextOvulationDate(
      DateTime nextOvulationDate) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({
        'healthData.nextOvulationDate': Timestamp.fromDate(nextOvulationDate)
      }).then((_) => const Right(unit));
    } on FirebaseException catch (e) {
      return Left(HealthDataFailure.ovulationDateFailure());
    }
  }
}
