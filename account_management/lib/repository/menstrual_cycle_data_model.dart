import 'package:freezed_annotation/freezed_annotation.dart';
part 'menstrual_cycle_data_model.freezed.dart';

@freezed
class MenstrualCycleData with _$MenstrualCycleData {
  const factory MenstrualCycleData({
    required int currentCycleDay,
    required int periodDays,
    required int cycleLength,
    required int ovulationDayStart,
    required int ovulationDaysLength,
  }) = _MenstrualCycleData;

  factory MenstrualCycleData.empty() => MenstrualCycleData(
    currentCycleDay: 1,
    periodDays: 6,
    cycleLength: 28,
    ovulationDayStart: 14,
    ovulationDaysLength: 7,
  );
}