import 'dart:convert';
import 'dart:io'as io;
import 'package:account_management/domain/facade/account_management_facade.dart';
import 'package:account_management/domain/failure/account_management_failures.dart';
import 'package:account_management/domain/model/account_details_model.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:account_management/domain/model/period_tracking_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:fpdart/src/effect.dart';
import 'package:fpdart/src/unit.dart';
import 'package:injectable/injectable.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

@LazySingleton(as: AccountManagementFacade)
class AccountManagementRepository implements AccountManagementFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final FirebaseStorage _firebaseStorage = FirebaseStorage.instance;


  @override
  Stream<Either<AccountManagementFailure, AccountDetailsModel>> getAccountDetails() async* {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      yield* userDoc.snapshots().map((snapshot) {
        if (snapshot.exists) {
          return Right(AccountDetailsModel.fromJson(snapshot.data()!));
        } else {
          return const Left(AccountManagementFailure.accountDetailsLoadFailure('usernotfound'));
        }
      });
    } on FirebaseException catch (e){
      yield  Left(AccountManagementFailure.accountDetailsLoadFailure(e.message??''));
    }
  }

  @override
  Future<Either<AccountManagementFailure, Unit>> updateAccountDetails(AccountDetailsModel accountDetails) async{
     try {
        final user = _firebaseAuth.currentUser;
        final userDoc = _firestore.collection('users').doc(user!.uid);
        return userDoc.update(accountDetails.toJson()).then((_) => const Right(unit));
      } on FirebaseException catch (e){
        return  Left(AccountManagementFailure.accountDetailsUpdateFailure(e.message??''));
      }
  }

  @override
  Future<Either<AccountManagementFailure, Unit>> updateAccountEmail(String email) async{
    try {
      final user = _firebaseAuth.currentUser;
      return user!.verifyBeforeUpdateEmail(email).then((_) => const Right(unit));
    } on FirebaseException catch (e){

      return  Left(AccountManagementFailure.accountDetailsUpdateFailure(e.message??''));
    }

  }

  @override
  Future<Either<AccountManagementFailure, Unit>> updateAccountPassword(String password)async {
    try {
      final user = _firebaseAuth.currentUser;
      return user!.updatePassword(password).then((_) => const Right(unit));
    } on FirebaseException catch (e){
      return  Left(AccountManagementFailure.accountDetailsUpdateFailure(e.message??''));
    }
  }

  @override
  Future<Either<AccountManagementFailure, Unit>> updateAccountPhoneNumber(String phoneNumber) async{
    try {
      final user = _firebaseAuth.currentUser;
      return  Right(unit);
    } on FirebaseException catch (e){
      return  Left(AccountManagementFailure.accountDetailsUpdateFailure(e.message??''));
    }
  }

@override
Future<Either<AccountManagementFailure, Unit>> updateProfilePicture(XFile? profilePicture) async {
  try {
    final user = _firebaseAuth.currentUser;
    if (user == null || profilePicture == null) {
      return  Left(AccountManagementFailure.accountDetailsUpdateFailure('usernotfound'));
    }
    // Step 1: Upload the profile picture
    TaskSnapshot uploadTask = await _firebaseStorage.ref('profile_pictures/user/${user.uid}').putFile(io.File(profilePicture.path));

    // Step 2: Get the download URL
    String downloadURL = await uploadTask.ref.getDownloadURL();

    // Step 3: Update the user document with the new picture URL
    await _firestore.collection('users').doc(user.uid).update({'photoURL': downloadURL});

    return const Right(unit);
  } on FirebaseException catch (e) {
    return  Left(AccountManagementFailure.accountDetailsUpdateFailure(e.message??''));
  }
}

  @override
  Future<Either<AccountManagementFailure, Unit>> deleteProfilePicture() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return  Left(AccountManagementFailure.accountDetailsUpdateFailure('usernotfound'));
      }
      // Step 1: Delete the profile picture
      await _firebaseStorage.ref('profile_pictures/user/${user.uid}').delete();

      // Step 2: Update the user document with the new picture URL
      await _firestore.collection('users').doc(user.uid).update({'photoURL': null});

      return const Right(unit);
    } on FirebaseException catch (e) {
      return  Left(AccountManagementFailure.accountDetailsUpdateFailure(e.message??''));
    }
  }

  @override
Future<Either<AccountManagementFailure, Unit>> updateOnboardingData(HealthDataModel healthData,DateTime dateOfBirth) async {
  try {
    final user = _firebaseAuth.currentUser;
    final sharedPreferences = await SharedPreferences.getInstance();
    if (user == null) {
      return const Left(AccountManagementFailure.accountDetailsUpdateFailure('usernotfound'));
    }
    // Step 1: Update the user document with the new health data
    await _firestore.collection('users').doc(user.uid).set({'dateOfBirth':Timestamp.fromDate(dateOfBirth),'healthData': healthData.toJson()}, SetOptions(merge: true));

    // Fetch the current list of onboarded user IDs
    final String? onboardedUserIdsJson = sharedPreferences.getString('onboardedUserIds');
    List<String> onboardedUserIds = [];
    if (onboardedUserIdsJson != null) {
      onboardedUserIds = List<String>.from(json.decode(onboardedUserIdsJson));
    }

    // Check if the current user's ID is already in the list, if not, add it
    if (!onboardedUserIds.contains(user.uid)) {
      onboardedUserIds.add(user.uid);
      // Save the updated list back to SharedPreferences
      await sharedPreferences.setString('onboardedUserIds', json.encode(onboardedUserIds));
    }

    await sharedPreferences.setBool('isOnboarded', true); // This line can be removed if you decide to solely rely on the list for onboarding status
    return const Right(unit);
  } on FirebaseException catch (e) {
    return  Left(AccountManagementFailure.accountDetailsUpdateFailure(e.message??''));
  }
}

}