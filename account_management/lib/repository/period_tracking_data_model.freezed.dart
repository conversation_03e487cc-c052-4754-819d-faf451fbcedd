// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'period_tracking_data_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PeriodTrackingData {
  DateTime get focusedDay => throw _privateConstructorUsedError;
  Set<DateTime> get selectedDays => throw _privateConstructorUsedError;
  PeriodTrackingModel get selectedPeriodTrackingDay =>
      throw _privateConstructorUsedError;
  List<PeriodTrackingModel> get periodTrackingDetails =>
      throw _privateConstructorUsedError;
  Set<DateTime> get ovulationDays => throw _privateConstructorUsedError;

  /// Create a copy of PeriodTrackingData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PeriodTrackingDataCopyWith<PeriodTrackingData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodTrackingDataCopyWith<$Res> {
  factory $PeriodTrackingDataCopyWith(
          PeriodTrackingData value, $Res Function(PeriodTrackingData) then) =
      _$PeriodTrackingDataCopyWithImpl<$Res, PeriodTrackingData>;
  @useResult
  $Res call(
      {DateTime focusedDay,
      Set<DateTime> selectedDays,
      PeriodTrackingModel selectedPeriodTrackingDay,
      List<PeriodTrackingModel> periodTrackingDetails,
      Set<DateTime> ovulationDays});
}

/// @nodoc
class _$PeriodTrackingDataCopyWithImpl<$Res, $Val extends PeriodTrackingData>
    implements $PeriodTrackingDataCopyWith<$Res> {
  _$PeriodTrackingDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodTrackingData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? focusedDay = null,
    Object? selectedDays = null,
    Object? selectedPeriodTrackingDay = null,
    Object? periodTrackingDetails = null,
    Object? ovulationDays = null,
  }) {
    return _then(_value.copyWith(
      focusedDay: null == focusedDay
          ? _value.focusedDay
          : focusedDay // ignore: cast_nullable_to_non_nullable
              as DateTime,
      selectedDays: null == selectedDays
          ? _value.selectedDays
          : selectedDays // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      selectedPeriodTrackingDay: null == selectedPeriodTrackingDay
          ? _value.selectedPeriodTrackingDay
          : selectedPeriodTrackingDay // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingModel,
      periodTrackingDetails: null == periodTrackingDetails
          ? _value.periodTrackingDetails
          : periodTrackingDetails // ignore: cast_nullable_to_non_nullable
              as List<PeriodTrackingModel>,
      ovulationDays: null == ovulationDays
          ? _value.ovulationDays
          : ovulationDays // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PeriodTrackingDataImplCopyWith<$Res>
    implements $PeriodTrackingDataCopyWith<$Res> {
  factory _$$PeriodTrackingDataImplCopyWith(_$PeriodTrackingDataImpl value,
          $Res Function(_$PeriodTrackingDataImpl) then) =
      __$$PeriodTrackingDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime focusedDay,
      Set<DateTime> selectedDays,
      PeriodTrackingModel selectedPeriodTrackingDay,
      List<PeriodTrackingModel> periodTrackingDetails,
      Set<DateTime> ovulationDays});
}

/// @nodoc
class __$$PeriodTrackingDataImplCopyWithImpl<$Res>
    extends _$PeriodTrackingDataCopyWithImpl<$Res, _$PeriodTrackingDataImpl>
    implements _$$PeriodTrackingDataImplCopyWith<$Res> {
  __$$PeriodTrackingDataImplCopyWithImpl(_$PeriodTrackingDataImpl _value,
      $Res Function(_$PeriodTrackingDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? focusedDay = null,
    Object? selectedDays = null,
    Object? selectedPeriodTrackingDay = null,
    Object? periodTrackingDetails = null,
    Object? ovulationDays = null,
  }) {
    return _then(_$PeriodTrackingDataImpl(
      focusedDay: null == focusedDay
          ? _value.focusedDay
          : focusedDay // ignore: cast_nullable_to_non_nullable
              as DateTime,
      selectedDays: null == selectedDays
          ? _value._selectedDays
          : selectedDays // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      selectedPeriodTrackingDay: null == selectedPeriodTrackingDay
          ? _value.selectedPeriodTrackingDay
          : selectedPeriodTrackingDay // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingModel,
      periodTrackingDetails: null == periodTrackingDetails
          ? _value._periodTrackingDetails
          : periodTrackingDetails // ignore: cast_nullable_to_non_nullable
              as List<PeriodTrackingModel>,
      ovulationDays: null == ovulationDays
          ? _value._ovulationDays
          : ovulationDays // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _$PeriodTrackingDataImpl implements _PeriodTrackingData {
  const _$PeriodTrackingDataImpl(
      {required this.focusedDay,
      required final Set<DateTime> selectedDays,
      required this.selectedPeriodTrackingDay,
      required final List<PeriodTrackingModel> periodTrackingDetails,
      required final Set<DateTime> ovulationDays})
      : _selectedDays = selectedDays,
        _periodTrackingDetails = periodTrackingDetails,
        _ovulationDays = ovulationDays;

  @override
  final DateTime focusedDay;
  final Set<DateTime> _selectedDays;
  @override
  Set<DateTime> get selectedDays {
    if (_selectedDays is EqualUnmodifiableSetView) return _selectedDays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedDays);
  }

  @override
  final PeriodTrackingModel selectedPeriodTrackingDay;
  final List<PeriodTrackingModel> _periodTrackingDetails;
  @override
  List<PeriodTrackingModel> get periodTrackingDetails {
    if (_periodTrackingDetails is EqualUnmodifiableListView)
      return _periodTrackingDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_periodTrackingDetails);
  }

  final Set<DateTime> _ovulationDays;
  @override
  Set<DateTime> get ovulationDays {
    if (_ovulationDays is EqualUnmodifiableSetView) return _ovulationDays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_ovulationDays);
  }

  @override
  String toString() {
    return 'PeriodTrackingData(focusedDay: $focusedDay, selectedDays: $selectedDays, selectedPeriodTrackingDay: $selectedPeriodTrackingDay, periodTrackingDetails: $periodTrackingDetails, ovulationDays: $ovulationDays)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodTrackingDataImpl &&
            (identical(other.focusedDay, focusedDay) ||
                other.focusedDay == focusedDay) &&
            const DeepCollectionEquality()
                .equals(other._selectedDays, _selectedDays) &&
            (identical(other.selectedPeriodTrackingDay,
                    selectedPeriodTrackingDay) ||
                other.selectedPeriodTrackingDay == selectedPeriodTrackingDay) &&
            const DeepCollectionEquality()
                .equals(other._periodTrackingDetails, _periodTrackingDetails) &&
            const DeepCollectionEquality()
                .equals(other._ovulationDays, _ovulationDays));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      focusedDay,
      const DeepCollectionEquality().hash(_selectedDays),
      selectedPeriodTrackingDay,
      const DeepCollectionEquality().hash(_periodTrackingDetails),
      const DeepCollectionEquality().hash(_ovulationDays));

  /// Create a copy of PeriodTrackingData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PeriodTrackingDataImplCopyWith<_$PeriodTrackingDataImpl> get copyWith =>
      __$$PeriodTrackingDataImplCopyWithImpl<_$PeriodTrackingDataImpl>(
          this, _$identity);
}

abstract class _PeriodTrackingData implements PeriodTrackingData {
  const factory _PeriodTrackingData(
      {required final DateTime focusedDay,
      required final Set<DateTime> selectedDays,
      required final PeriodTrackingModel selectedPeriodTrackingDay,
      required final List<PeriodTrackingModel> periodTrackingDetails,
      required final Set<DateTime> ovulationDays}) = _$PeriodTrackingDataImpl;

  @override
  DateTime get focusedDay;
  @override
  Set<DateTime> get selectedDays;
  @override
  PeriodTrackingModel get selectedPeriodTrackingDay;
  @override
  List<PeriodTrackingModel> get periodTrackingDetails;
  @override
  Set<DateTime> get ovulationDays;

  /// Create a copy of PeriodTrackingData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PeriodTrackingDataImplCopyWith<_$PeriodTrackingDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
