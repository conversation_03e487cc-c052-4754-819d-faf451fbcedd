import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/model/period_tracking_model.dart';
part 'period_tracking_data_model.freezed.dart';

@freezed
class PeriodTrackingData with _$PeriodTrackingData {
  const factory PeriodTrackingData({
    required DateTime focusedDay,
    required Set<DateTime> selectedDays,
    required PeriodTrackingModel selectedPeriodTrackingDay,
    required List<PeriodTrackingModel> periodTrackingDetails,
    required Set<DateTime> ovulationDays,
  }) = _PeriodTrackingData;
  //empty constructor
  factory PeriodTrackingData.empty() => PeriodTrackingData(
    focusedDay: DateTime.now(),
    selectedDays: {},
    ovulationDays: {},
    selectedPeriodTrackingDay: PeriodTrackingModel.empty(),
    periodTrackingDetails: [],

  );
}