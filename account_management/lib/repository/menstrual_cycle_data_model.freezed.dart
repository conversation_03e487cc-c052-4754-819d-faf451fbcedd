// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'menstrual_cycle_data_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MenstrualCycleData {
  int get currentCycleDay => throw _privateConstructorUsedError;
  int get periodDays => throw _privateConstructorUsedError;
  int get cycleLength => throw _privateConstructorUsedError;
  int get ovulationDayStart => throw _privateConstructorUsedError;
  int get ovulationDaysLength => throw _privateConstructorUsedError;

  /// Create a copy of MenstrualCycleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MenstrualCycleDataCopyWith<MenstrualCycleData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MenstrualCycleDataCopyWith<$Res> {
  factory $MenstrualCycleDataCopyWith(
          MenstrualCycleData value, $Res Function(MenstrualCycleData) then) =
      _$MenstrualCycleDataCopyWithImpl<$Res, MenstrualCycleData>;
  @useResult
  $Res call(
      {int currentCycleDay,
      int periodDays,
      int cycleLength,
      int ovulationDayStart,
      int ovulationDaysLength});
}

/// @nodoc
class _$MenstrualCycleDataCopyWithImpl<$Res, $Val extends MenstrualCycleData>
    implements $MenstrualCycleDataCopyWith<$Res> {
  _$MenstrualCycleDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MenstrualCycleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentCycleDay = null,
    Object? periodDays = null,
    Object? cycleLength = null,
    Object? ovulationDayStart = null,
    Object? ovulationDaysLength = null,
  }) {
    return _then(_value.copyWith(
      currentCycleDay: null == currentCycleDay
          ? _value.currentCycleDay
          : currentCycleDay // ignore: cast_nullable_to_non_nullable
              as int,
      periodDays: null == periodDays
          ? _value.periodDays
          : periodDays // ignore: cast_nullable_to_non_nullable
              as int,
      cycleLength: null == cycleLength
          ? _value.cycleLength
          : cycleLength // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDayStart: null == ovulationDayStart
          ? _value.ovulationDayStart
          : ovulationDayStart // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDaysLength: null == ovulationDaysLength
          ? _value.ovulationDaysLength
          : ovulationDaysLength // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MenstrualCycleDataImplCopyWith<$Res>
    implements $MenstrualCycleDataCopyWith<$Res> {
  factory _$$MenstrualCycleDataImplCopyWith(_$MenstrualCycleDataImpl value,
          $Res Function(_$MenstrualCycleDataImpl) then) =
      __$$MenstrualCycleDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentCycleDay,
      int periodDays,
      int cycleLength,
      int ovulationDayStart,
      int ovulationDaysLength});
}

/// @nodoc
class __$$MenstrualCycleDataImplCopyWithImpl<$Res>
    extends _$MenstrualCycleDataCopyWithImpl<$Res, _$MenstrualCycleDataImpl>
    implements _$$MenstrualCycleDataImplCopyWith<$Res> {
  __$$MenstrualCycleDataImplCopyWithImpl(_$MenstrualCycleDataImpl _value,
      $Res Function(_$MenstrualCycleDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenstrualCycleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentCycleDay = null,
    Object? periodDays = null,
    Object? cycleLength = null,
    Object? ovulationDayStart = null,
    Object? ovulationDaysLength = null,
  }) {
    return _then(_$MenstrualCycleDataImpl(
      currentCycleDay: null == currentCycleDay
          ? _value.currentCycleDay
          : currentCycleDay // ignore: cast_nullable_to_non_nullable
              as int,
      periodDays: null == periodDays
          ? _value.periodDays
          : periodDays // ignore: cast_nullable_to_non_nullable
              as int,
      cycleLength: null == cycleLength
          ? _value.cycleLength
          : cycleLength // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDayStart: null == ovulationDayStart
          ? _value.ovulationDayStart
          : ovulationDayStart // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDaysLength: null == ovulationDaysLength
          ? _value.ovulationDaysLength
          : ovulationDaysLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$MenstrualCycleDataImpl implements _MenstrualCycleData {
  const _$MenstrualCycleDataImpl(
      {required this.currentCycleDay,
      required this.periodDays,
      required this.cycleLength,
      required this.ovulationDayStart,
      required this.ovulationDaysLength});

  @override
  final int currentCycleDay;
  @override
  final int periodDays;
  @override
  final int cycleLength;
  @override
  final int ovulationDayStart;
  @override
  final int ovulationDaysLength;

  @override
  String toString() {
    return 'MenstrualCycleData(currentCycleDay: $currentCycleDay, periodDays: $periodDays, cycleLength: $cycleLength, ovulationDayStart: $ovulationDayStart, ovulationDaysLength: $ovulationDaysLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MenstrualCycleDataImpl &&
            (identical(other.currentCycleDay, currentCycleDay) ||
                other.currentCycleDay == currentCycleDay) &&
            (identical(other.periodDays, periodDays) ||
                other.periodDays == periodDays) &&
            (identical(other.cycleLength, cycleLength) ||
                other.cycleLength == cycleLength) &&
            (identical(other.ovulationDayStart, ovulationDayStart) ||
                other.ovulationDayStart == ovulationDayStart) &&
            (identical(other.ovulationDaysLength, ovulationDaysLength) ||
                other.ovulationDaysLength == ovulationDaysLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currentCycleDay, periodDays,
      cycleLength, ovulationDayStart, ovulationDaysLength);

  /// Create a copy of MenstrualCycleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MenstrualCycleDataImplCopyWith<_$MenstrualCycleDataImpl> get copyWith =>
      __$$MenstrualCycleDataImplCopyWithImpl<_$MenstrualCycleDataImpl>(
          this, _$identity);
}

abstract class _MenstrualCycleData implements MenstrualCycleData {
  const factory _MenstrualCycleData(
      {required final int currentCycleDay,
      required final int periodDays,
      required final int cycleLength,
      required final int ovulationDayStart,
      required final int ovulationDaysLength}) = _$MenstrualCycleDataImpl;

  @override
  int get currentCycleDay;
  @override
  int get periodDays;
  @override
  int get cycleLength;
  @override
  int get ovulationDayStart;
  @override
  int get ovulationDaysLength;

  /// Create a copy of MenstrualCycleData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MenstrualCycleDataImplCopyWith<_$MenstrualCycleDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
