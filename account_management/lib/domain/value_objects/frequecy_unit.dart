// frequency_unit.dart
class FrequencyUnit {
  final String value;

  const FrequencyUnit(this.value);

  static const List<FrequencyUnit> availableFrequencyUnits = [
    FrequencyUnit('daily'),
    FrequencyUnit('weekly'),
    FrequencyUnit('monthly'),
  ];

  @override
  String toString() => value;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is FrequencyUnit && other.value == value;
  }

  @override
  int get hashCode => value.hashCode;
}

