class DosageUnit {
  final String value;

  const DosageUnit(this.value);

  static const List<DosageUnit> availableWeightUnits = [
    DosageUnit('mcg'),
    DosageUnit('mg'),
    DosageUnit('g'),
    DosageUnit('kg'),
    DosageUnit('ml'),
  ];

  @override
  String toString() => value;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is DosageUnit && other.value == value;
  }

  @override
  int get hashCode => value.hashCode;
}
