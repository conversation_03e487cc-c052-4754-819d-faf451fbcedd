import 'package:freezed_annotation/freezed_annotation.dart';
part 'health_data_failure.freezed.dart';


@freezed
class HealthDataFailure with _$HealthDataFailure {
  const factory HealthDataFailure.periodLengthFailure() = PeriodLengthFailure;
  const factory HealthDataFailure.cycleLengthFailure() = CycleLengthFailure;
  const factory HealthDataFailure.ovulationDateFailure() = OvulationDateFailure;
  const factory HealthDataFailure.contraceptionTypeFailure() = ContraceptionTypeFailure;
  const factory HealthDataFailure.unexpected() = Unexpected;
}