import 'package:freezed_annotation/freezed_annotation.dart';

part 'medication_failure.freezed.dart';

@freezed
class MedicationFailure with _$MedicationFailure{
  const factory MedicationFailure.updateFailure() = UpdateFailure;
  const factory MedicationFailure.deleteFailure() = DeleteFailure;
  const factory MedicationFailure.createFailure() = CreateFailure;
  const factory MedicationFailure.unexpected() = Unexpected;
  const factory MedicationFailure.getMedicationsFailure() = GetMedicationsFailure;
}