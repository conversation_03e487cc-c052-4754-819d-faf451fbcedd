import 'package:freezed_annotation/freezed_annotation.dart';
part 'period_tracking_failure.freezed.dart';

@freezed
class PeriodTrackingFailure with _$PeriodTrackingFailure {
  const factory PeriodTrackingFailure.updateFailure() = UpdateFailure;
  const factory PeriodTrackingFailure.deleteFailure() = DeleteFailure;
  const factory PeriodTrackingFailure.createFailure() = CreateFailure;
  const factory PeriodTrackingFailure.unexpected() = Unexpected;
  const factory PeriodTrackingFailure.getPeriodTrackingsFailure() =
      GetPeriodTrackingsFailure;
  const factory PeriodTrackingFailure.notFound() = NotFound;
  const factory PeriodTrackingFailure.invalidData() = InvalidData;
  const factory PeriodTrackingFailure.unauthenticated() = Unauthenticated;
}
