// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'period_tracking_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PeriodTrackingFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodTrackingFailureCopyWith<$Res> {
  factory $PeriodTrackingFailureCopyWith(PeriodTrackingFailure value,
          $Res Function(PeriodTrackingFailure) then) =
      _$PeriodTrackingFailureCopyWithImpl<$Res, PeriodTrackingFailure>;
}

/// @nodoc
class _$PeriodTrackingFailureCopyWithImpl<$Res,
        $Val extends PeriodTrackingFailure>
    implements $PeriodTrackingFailureCopyWith<$Res> {
  _$PeriodTrackingFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$UpdateFailureImplCopyWith<$Res> {
  factory _$$UpdateFailureImplCopyWith(
          _$UpdateFailureImpl value, $Res Function(_$UpdateFailureImpl) then) =
      __$$UpdateFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateFailureImplCopyWithImpl<$Res>
    extends _$PeriodTrackingFailureCopyWithImpl<$Res, _$UpdateFailureImpl>
    implements _$$UpdateFailureImplCopyWith<$Res> {
  __$$UpdateFailureImplCopyWithImpl(
      _$UpdateFailureImpl _value, $Res Function(_$UpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateFailureImpl implements UpdateFailure {
  const _$UpdateFailureImpl();

  @override
  String toString() {
    return 'PeriodTrackingFailure.updateFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdateFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    return updateFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    return updateFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    if (updateFailure != null) {
      return updateFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    return updateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    return updateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    if (updateFailure != null) {
      return updateFailure(this);
    }
    return orElse();
  }
}

abstract class UpdateFailure implements PeriodTrackingFailure {
  const factory UpdateFailure() = _$UpdateFailureImpl;
}

/// @nodoc
abstract class _$$DeleteFailureImplCopyWith<$Res> {
  factory _$$DeleteFailureImplCopyWith(
          _$DeleteFailureImpl value, $Res Function(_$DeleteFailureImpl) then) =
      __$$DeleteFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteFailureImplCopyWithImpl<$Res>
    extends _$PeriodTrackingFailureCopyWithImpl<$Res, _$DeleteFailureImpl>
    implements _$$DeleteFailureImplCopyWith<$Res> {
  __$$DeleteFailureImplCopyWithImpl(
      _$DeleteFailureImpl _value, $Res Function(_$DeleteFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteFailureImpl implements DeleteFailure {
  const _$DeleteFailureImpl();

  @override
  String toString() {
    return 'PeriodTrackingFailure.deleteFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DeleteFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    return deleteFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    return deleteFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    if (deleteFailure != null) {
      return deleteFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    return deleteFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    return deleteFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    if (deleteFailure != null) {
      return deleteFailure(this);
    }
    return orElse();
  }
}

abstract class DeleteFailure implements PeriodTrackingFailure {
  const factory DeleteFailure() = _$DeleteFailureImpl;
}

/// @nodoc
abstract class _$$CreateFailureImplCopyWith<$Res> {
  factory _$$CreateFailureImplCopyWith(
          _$CreateFailureImpl value, $Res Function(_$CreateFailureImpl) then) =
      __$$CreateFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreateFailureImplCopyWithImpl<$Res>
    extends _$PeriodTrackingFailureCopyWithImpl<$Res, _$CreateFailureImpl>
    implements _$$CreateFailureImplCopyWith<$Res> {
  __$$CreateFailureImplCopyWithImpl(
      _$CreateFailureImpl _value, $Res Function(_$CreateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreateFailureImpl implements CreateFailure {
  const _$CreateFailureImpl();

  @override
  String toString() {
    return 'PeriodTrackingFailure.createFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CreateFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    return createFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    return createFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    if (createFailure != null) {
      return createFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    return createFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    return createFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    if (createFailure != null) {
      return createFailure(this);
    }
    return orElse();
  }
}

abstract class CreateFailure implements PeriodTrackingFailure {
  const factory CreateFailure() = _$CreateFailureImpl;
}

/// @nodoc
abstract class _$$UnexpectedImplCopyWith<$Res> {
  factory _$$UnexpectedImplCopyWith(
          _$UnexpectedImpl value, $Res Function(_$UnexpectedImpl) then) =
      __$$UnexpectedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedImplCopyWithImpl<$Res>
    extends _$PeriodTrackingFailureCopyWithImpl<$Res, _$UnexpectedImpl>
    implements _$$UnexpectedImplCopyWith<$Res> {
  __$$UnexpectedImplCopyWithImpl(
      _$UnexpectedImpl _value, $Res Function(_$UnexpectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnexpectedImpl implements Unexpected {
  const _$UnexpectedImpl();

  @override
  String toString() {
    return 'PeriodTrackingFailure.unexpected()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnexpectedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    return unexpected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    return unexpected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class Unexpected implements PeriodTrackingFailure {
  const factory Unexpected() = _$UnexpectedImpl;
}

/// @nodoc
abstract class _$$GetPeriodTrackingsFailureImplCopyWith<$Res> {
  factory _$$GetPeriodTrackingsFailureImplCopyWith(
          _$GetPeriodTrackingsFailureImpl value,
          $Res Function(_$GetPeriodTrackingsFailureImpl) then) =
      __$$GetPeriodTrackingsFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetPeriodTrackingsFailureImplCopyWithImpl<$Res>
    extends _$PeriodTrackingFailureCopyWithImpl<$Res,
        _$GetPeriodTrackingsFailureImpl>
    implements _$$GetPeriodTrackingsFailureImplCopyWith<$Res> {
  __$$GetPeriodTrackingsFailureImplCopyWithImpl(
      _$GetPeriodTrackingsFailureImpl _value,
      $Res Function(_$GetPeriodTrackingsFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetPeriodTrackingsFailureImpl implements GetPeriodTrackingsFailure {
  const _$GetPeriodTrackingsFailureImpl();

  @override
  String toString() {
    return 'PeriodTrackingFailure.getPeriodTrackingsFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetPeriodTrackingsFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    return getPeriodTrackingsFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    return getPeriodTrackingsFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    if (getPeriodTrackingsFailure != null) {
      return getPeriodTrackingsFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    return getPeriodTrackingsFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    return getPeriodTrackingsFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    if (getPeriodTrackingsFailure != null) {
      return getPeriodTrackingsFailure(this);
    }
    return orElse();
  }
}

abstract class GetPeriodTrackingsFailure implements PeriodTrackingFailure {
  const factory GetPeriodTrackingsFailure() = _$GetPeriodTrackingsFailureImpl;
}

/// @nodoc
abstract class _$$NotFoundImplCopyWith<$Res> {
  factory _$$NotFoundImplCopyWith(
          _$NotFoundImpl value, $Res Function(_$NotFoundImpl) then) =
      __$$NotFoundImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NotFoundImplCopyWithImpl<$Res>
    extends _$PeriodTrackingFailureCopyWithImpl<$Res, _$NotFoundImpl>
    implements _$$NotFoundImplCopyWith<$Res> {
  __$$NotFoundImplCopyWithImpl(
      _$NotFoundImpl _value, $Res Function(_$NotFoundImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NotFoundImpl implements NotFound {
  const _$NotFoundImpl();

  @override
  String toString() {
    return 'PeriodTrackingFailure.notFound()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NotFoundImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    return notFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    return notFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    return notFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    return notFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(this);
    }
    return orElse();
  }
}

abstract class NotFound implements PeriodTrackingFailure {
  const factory NotFound() = _$NotFoundImpl;
}

/// @nodoc
abstract class _$$InvalidDataImplCopyWith<$Res> {
  factory _$$InvalidDataImplCopyWith(
          _$InvalidDataImpl value, $Res Function(_$InvalidDataImpl) then) =
      __$$InvalidDataImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvalidDataImplCopyWithImpl<$Res>
    extends _$PeriodTrackingFailureCopyWithImpl<$Res, _$InvalidDataImpl>
    implements _$$InvalidDataImplCopyWith<$Res> {
  __$$InvalidDataImplCopyWithImpl(
      _$InvalidDataImpl _value, $Res Function(_$InvalidDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InvalidDataImpl implements InvalidData {
  const _$InvalidDataImpl();

  @override
  String toString() {
    return 'PeriodTrackingFailure.invalidData()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InvalidDataImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    return invalidData();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    return invalidData?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    if (invalidData != null) {
      return invalidData();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    return invalidData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    return invalidData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    if (invalidData != null) {
      return invalidData(this);
    }
    return orElse();
  }
}

abstract class InvalidData implements PeriodTrackingFailure {
  const factory InvalidData() = _$InvalidDataImpl;
}

/// @nodoc
abstract class _$$UnauthenticatedImplCopyWith<$Res> {
  factory _$$UnauthenticatedImplCopyWith(_$UnauthenticatedImpl value,
          $Res Function(_$UnauthenticatedImpl) then) =
      __$$UnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnauthenticatedImplCopyWithImpl<$Res>
    extends _$PeriodTrackingFailureCopyWithImpl<$Res, _$UnauthenticatedImpl>
    implements _$$UnauthenticatedImplCopyWith<$Res> {
  __$$UnauthenticatedImplCopyWithImpl(
      _$UnauthenticatedImpl _value, $Res Function(_$UnauthenticatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnauthenticatedImpl implements Unauthenticated {
  const _$UnauthenticatedImpl();

  @override
  String toString() {
    return 'PeriodTrackingFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class Unauthenticated implements PeriodTrackingFailure {
  const factory Unauthenticated() = _$UnauthenticatedImpl;
}
