// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medication_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MedicationFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getMedicationsFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getMedicationsFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getMedicationsFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetMedicationsFailure value)
        getMedicationsFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetMedicationsFailure value)? getMedicationsFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetMedicationsFailure value)? getMedicationsFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedicationFailureCopyWith<$Res> {
  factory $MedicationFailureCopyWith(
          MedicationFailure value, $Res Function(MedicationFailure) then) =
      _$MedicationFailureCopyWithImpl<$Res, MedicationFailure>;
}

/// @nodoc
class _$MedicationFailureCopyWithImpl<$Res, $Val extends MedicationFailure>
    implements $MedicationFailureCopyWith<$Res> {
  _$MedicationFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MedicationFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$UpdateFailureImplCopyWith<$Res> {
  factory _$$UpdateFailureImplCopyWith(
          _$UpdateFailureImpl value, $Res Function(_$UpdateFailureImpl) then) =
      __$$UpdateFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateFailureImplCopyWithImpl<$Res>
    extends _$MedicationFailureCopyWithImpl<$Res, _$UpdateFailureImpl>
    implements _$$UpdateFailureImplCopyWith<$Res> {
  __$$UpdateFailureImplCopyWithImpl(
      _$UpdateFailureImpl _value, $Res Function(_$UpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateFailureImpl implements UpdateFailure {
  const _$UpdateFailureImpl();

  @override
  String toString() {
    return 'MedicationFailure.updateFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdateFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getMedicationsFailure,
  }) {
    return updateFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getMedicationsFailure,
  }) {
    return updateFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (updateFailure != null) {
      return updateFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetMedicationsFailure value)
        getMedicationsFailure,
  }) {
    return updateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetMedicationsFailure value)? getMedicationsFailure,
  }) {
    return updateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetMedicationsFailure value)? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (updateFailure != null) {
      return updateFailure(this);
    }
    return orElse();
  }
}

abstract class UpdateFailure implements MedicationFailure {
  const factory UpdateFailure() = _$UpdateFailureImpl;
}

/// @nodoc
abstract class _$$DeleteFailureImplCopyWith<$Res> {
  factory _$$DeleteFailureImplCopyWith(
          _$DeleteFailureImpl value, $Res Function(_$DeleteFailureImpl) then) =
      __$$DeleteFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteFailureImplCopyWithImpl<$Res>
    extends _$MedicationFailureCopyWithImpl<$Res, _$DeleteFailureImpl>
    implements _$$DeleteFailureImplCopyWith<$Res> {
  __$$DeleteFailureImplCopyWithImpl(
      _$DeleteFailureImpl _value, $Res Function(_$DeleteFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteFailureImpl implements DeleteFailure {
  const _$DeleteFailureImpl();

  @override
  String toString() {
    return 'MedicationFailure.deleteFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DeleteFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getMedicationsFailure,
  }) {
    return deleteFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getMedicationsFailure,
  }) {
    return deleteFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (deleteFailure != null) {
      return deleteFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetMedicationsFailure value)
        getMedicationsFailure,
  }) {
    return deleteFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetMedicationsFailure value)? getMedicationsFailure,
  }) {
    return deleteFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetMedicationsFailure value)? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (deleteFailure != null) {
      return deleteFailure(this);
    }
    return orElse();
  }
}

abstract class DeleteFailure implements MedicationFailure {
  const factory DeleteFailure() = _$DeleteFailureImpl;
}

/// @nodoc
abstract class _$$CreateFailureImplCopyWith<$Res> {
  factory _$$CreateFailureImplCopyWith(
          _$CreateFailureImpl value, $Res Function(_$CreateFailureImpl) then) =
      __$$CreateFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreateFailureImplCopyWithImpl<$Res>
    extends _$MedicationFailureCopyWithImpl<$Res, _$CreateFailureImpl>
    implements _$$CreateFailureImplCopyWith<$Res> {
  __$$CreateFailureImplCopyWithImpl(
      _$CreateFailureImpl _value, $Res Function(_$CreateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreateFailureImpl implements CreateFailure {
  const _$CreateFailureImpl();

  @override
  String toString() {
    return 'MedicationFailure.createFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CreateFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getMedicationsFailure,
  }) {
    return createFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getMedicationsFailure,
  }) {
    return createFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (createFailure != null) {
      return createFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetMedicationsFailure value)
        getMedicationsFailure,
  }) {
    return createFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetMedicationsFailure value)? getMedicationsFailure,
  }) {
    return createFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetMedicationsFailure value)? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (createFailure != null) {
      return createFailure(this);
    }
    return orElse();
  }
}

abstract class CreateFailure implements MedicationFailure {
  const factory CreateFailure() = _$CreateFailureImpl;
}

/// @nodoc
abstract class _$$UnexpectedImplCopyWith<$Res> {
  factory _$$UnexpectedImplCopyWith(
          _$UnexpectedImpl value, $Res Function(_$UnexpectedImpl) then) =
      __$$UnexpectedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedImplCopyWithImpl<$Res>
    extends _$MedicationFailureCopyWithImpl<$Res, _$UnexpectedImpl>
    implements _$$UnexpectedImplCopyWith<$Res> {
  __$$UnexpectedImplCopyWithImpl(
      _$UnexpectedImpl _value, $Res Function(_$UnexpectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnexpectedImpl implements Unexpected {
  const _$UnexpectedImpl();

  @override
  String toString() {
    return 'MedicationFailure.unexpected()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnexpectedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getMedicationsFailure,
  }) {
    return unexpected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getMedicationsFailure,
  }) {
    return unexpected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetMedicationsFailure value)
        getMedicationsFailure,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetMedicationsFailure value)? getMedicationsFailure,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetMedicationsFailure value)? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class Unexpected implements MedicationFailure {
  const factory Unexpected() = _$UnexpectedImpl;
}

/// @nodoc
abstract class _$$GetMedicationsFailureImplCopyWith<$Res> {
  factory _$$GetMedicationsFailureImplCopyWith(
          _$GetMedicationsFailureImpl value,
          $Res Function(_$GetMedicationsFailureImpl) then) =
      __$$GetMedicationsFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetMedicationsFailureImplCopyWithImpl<$Res>
    extends _$MedicationFailureCopyWithImpl<$Res, _$GetMedicationsFailureImpl>
    implements _$$GetMedicationsFailureImplCopyWith<$Res> {
  __$$GetMedicationsFailureImplCopyWithImpl(_$GetMedicationsFailureImpl _value,
      $Res Function(_$GetMedicationsFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetMedicationsFailureImpl implements GetMedicationsFailure {
  const _$GetMedicationsFailureImpl();

  @override
  String toString() {
    return 'MedicationFailure.getMedicationsFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetMedicationsFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getMedicationsFailure,
  }) {
    return getMedicationsFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getMedicationsFailure,
  }) {
    return getMedicationsFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (getMedicationsFailure != null) {
      return getMedicationsFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetMedicationsFailure value)
        getMedicationsFailure,
  }) {
    return getMedicationsFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetMedicationsFailure value)? getMedicationsFailure,
  }) {
    return getMedicationsFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetMedicationsFailure value)? getMedicationsFailure,
    required TResult orElse(),
  }) {
    if (getMedicationsFailure != null) {
      return getMedicationsFailure(this);
    }
    return orElse();
  }
}

abstract class GetMedicationsFailure implements MedicationFailure {
  const factory GetMedicationsFailure() = _$GetMedicationsFailureImpl;
}
