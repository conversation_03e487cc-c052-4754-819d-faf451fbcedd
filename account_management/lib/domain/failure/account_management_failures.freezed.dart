// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_management_failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AccountManagementFailure {
  String get failure => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failure) accountDetailsLoadFailure,
    required TResult Function(String failure) accountDetailsUpdateFailure,
    required TResult Function(String failure) accountPasswordUpdateFailure,
    required TResult Function(String failure) accountEmailUpdateFailure,
    required TResult Function(String failure) accountPhoneNumberUpdateFailure,
    required TResult Function(String failure) onboardingDataAddFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failure)? accountDetailsLoadFailure,
    TResult? Function(String failure)? accountDetailsUpdateFailure,
    TResult? Function(String failure)? accountPasswordUpdateFailure,
    TResult? Function(String failure)? accountEmailUpdateFailure,
    TResult? Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult? Function(String failure)? onboardingDataAddFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failure)? accountDetailsLoadFailure,
    TResult Function(String failure)? accountDetailsUpdateFailure,
    TResult Function(String failure)? accountPasswordUpdateFailure,
    TResult Function(String failure)? accountEmailUpdateFailure,
    TResult Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult Function(String failure)? onboardingDataAddFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountDetailsLoadFailure value)
        accountDetailsLoadFailure,
    required TResult Function(AccountDetailsUpdateFailure value)
        accountDetailsUpdateFailure,
    required TResult Function(AccountPasswordUpdateFailure value)
        accountPasswordUpdateFailure,
    required TResult Function(AccountEmailUpdateFailure value)
        accountEmailUpdateFailure,
    required TResult Function(AccountPhoneNumberUpdateFailure value)
        accountPhoneNumberUpdateFailure,
    required TResult Function(OnboardingDataAddFailure value)
        onboardingDataAddFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult? Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult? Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult? Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult? Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult? Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountManagementFailureCopyWith<AccountManagementFailure> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountManagementFailureCopyWith<$Res> {
  factory $AccountManagementFailureCopyWith(AccountManagementFailure value,
          $Res Function(AccountManagementFailure) then) =
      _$AccountManagementFailureCopyWithImpl<$Res, AccountManagementFailure>;
  @useResult
  $Res call({String failure});
}

/// @nodoc
class _$AccountManagementFailureCopyWithImpl<$Res,
        $Val extends AccountManagementFailure>
    implements $AccountManagementFailureCopyWith<$Res> {
  _$AccountManagementFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_value.copyWith(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountDetailsLoadFailureImplCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory _$$AccountDetailsLoadFailureImplCopyWith(
          _$AccountDetailsLoadFailureImpl value,
          $Res Function(_$AccountDetailsLoadFailureImpl) then) =
      __$$AccountDetailsLoadFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class __$$AccountDetailsLoadFailureImplCopyWithImpl<$Res>
    extends _$AccountManagementFailureCopyWithImpl<$Res,
        _$AccountDetailsLoadFailureImpl>
    implements _$$AccountDetailsLoadFailureImplCopyWith<$Res> {
  __$$AccountDetailsLoadFailureImplCopyWithImpl(
      _$AccountDetailsLoadFailureImpl _value,
      $Res Function(_$AccountDetailsLoadFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$AccountDetailsLoadFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AccountDetailsLoadFailureImpl implements AccountDetailsLoadFailure {
  const _$AccountDetailsLoadFailureImpl(this.failure);

  @override
  final String failure;

  @override
  String toString() {
    return 'AccountManagementFailure.accountDetailsLoadFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountDetailsLoadFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountDetailsLoadFailureImplCopyWith<_$AccountDetailsLoadFailureImpl>
      get copyWith => __$$AccountDetailsLoadFailureImplCopyWithImpl<
          _$AccountDetailsLoadFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failure) accountDetailsLoadFailure,
    required TResult Function(String failure) accountDetailsUpdateFailure,
    required TResult Function(String failure) accountPasswordUpdateFailure,
    required TResult Function(String failure) accountEmailUpdateFailure,
    required TResult Function(String failure) accountPhoneNumberUpdateFailure,
    required TResult Function(String failure) onboardingDataAddFailure,
  }) {
    return accountDetailsLoadFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failure)? accountDetailsLoadFailure,
    TResult? Function(String failure)? accountDetailsUpdateFailure,
    TResult? Function(String failure)? accountPasswordUpdateFailure,
    TResult? Function(String failure)? accountEmailUpdateFailure,
    TResult? Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult? Function(String failure)? onboardingDataAddFailure,
  }) {
    return accountDetailsLoadFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failure)? accountDetailsLoadFailure,
    TResult Function(String failure)? accountDetailsUpdateFailure,
    TResult Function(String failure)? accountPasswordUpdateFailure,
    TResult Function(String failure)? accountEmailUpdateFailure,
    TResult Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult Function(String failure)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountDetailsLoadFailure != null) {
      return accountDetailsLoadFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountDetailsLoadFailure value)
        accountDetailsLoadFailure,
    required TResult Function(AccountDetailsUpdateFailure value)
        accountDetailsUpdateFailure,
    required TResult Function(AccountPasswordUpdateFailure value)
        accountPasswordUpdateFailure,
    required TResult Function(AccountEmailUpdateFailure value)
        accountEmailUpdateFailure,
    required TResult Function(AccountPhoneNumberUpdateFailure value)
        accountPhoneNumberUpdateFailure,
    required TResult Function(OnboardingDataAddFailure value)
        onboardingDataAddFailure,
  }) {
    return accountDetailsLoadFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult? Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult? Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult? Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult? Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult? Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
  }) {
    return accountDetailsLoadFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountDetailsLoadFailure != null) {
      return accountDetailsLoadFailure(this);
    }
    return orElse();
  }
}

abstract class AccountDetailsLoadFailure implements AccountManagementFailure {
  const factory AccountDetailsLoadFailure(final String failure) =
      _$AccountDetailsLoadFailureImpl;

  @override
  String get failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountDetailsLoadFailureImplCopyWith<_$AccountDetailsLoadFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AccountDetailsUpdateFailureImplCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory _$$AccountDetailsUpdateFailureImplCopyWith(
          _$AccountDetailsUpdateFailureImpl value,
          $Res Function(_$AccountDetailsUpdateFailureImpl) then) =
      __$$AccountDetailsUpdateFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class __$$AccountDetailsUpdateFailureImplCopyWithImpl<$Res>
    extends _$AccountManagementFailureCopyWithImpl<$Res,
        _$AccountDetailsUpdateFailureImpl>
    implements _$$AccountDetailsUpdateFailureImplCopyWith<$Res> {
  __$$AccountDetailsUpdateFailureImplCopyWithImpl(
      _$AccountDetailsUpdateFailureImpl _value,
      $Res Function(_$AccountDetailsUpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$AccountDetailsUpdateFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AccountDetailsUpdateFailureImpl implements AccountDetailsUpdateFailure {
  const _$AccountDetailsUpdateFailureImpl(this.failure);

  @override
  final String failure;

  @override
  String toString() {
    return 'AccountManagementFailure.accountDetailsUpdateFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountDetailsUpdateFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountDetailsUpdateFailureImplCopyWith<_$AccountDetailsUpdateFailureImpl>
      get copyWith => __$$AccountDetailsUpdateFailureImplCopyWithImpl<
          _$AccountDetailsUpdateFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failure) accountDetailsLoadFailure,
    required TResult Function(String failure) accountDetailsUpdateFailure,
    required TResult Function(String failure) accountPasswordUpdateFailure,
    required TResult Function(String failure) accountEmailUpdateFailure,
    required TResult Function(String failure) accountPhoneNumberUpdateFailure,
    required TResult Function(String failure) onboardingDataAddFailure,
  }) {
    return accountDetailsUpdateFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failure)? accountDetailsLoadFailure,
    TResult? Function(String failure)? accountDetailsUpdateFailure,
    TResult? Function(String failure)? accountPasswordUpdateFailure,
    TResult? Function(String failure)? accountEmailUpdateFailure,
    TResult? Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult? Function(String failure)? onboardingDataAddFailure,
  }) {
    return accountDetailsUpdateFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failure)? accountDetailsLoadFailure,
    TResult Function(String failure)? accountDetailsUpdateFailure,
    TResult Function(String failure)? accountPasswordUpdateFailure,
    TResult Function(String failure)? accountEmailUpdateFailure,
    TResult Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult Function(String failure)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountDetailsUpdateFailure != null) {
      return accountDetailsUpdateFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountDetailsLoadFailure value)
        accountDetailsLoadFailure,
    required TResult Function(AccountDetailsUpdateFailure value)
        accountDetailsUpdateFailure,
    required TResult Function(AccountPasswordUpdateFailure value)
        accountPasswordUpdateFailure,
    required TResult Function(AccountEmailUpdateFailure value)
        accountEmailUpdateFailure,
    required TResult Function(AccountPhoneNumberUpdateFailure value)
        accountPhoneNumberUpdateFailure,
    required TResult Function(OnboardingDataAddFailure value)
        onboardingDataAddFailure,
  }) {
    return accountDetailsUpdateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult? Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult? Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult? Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult? Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult? Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
  }) {
    return accountDetailsUpdateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountDetailsUpdateFailure != null) {
      return accountDetailsUpdateFailure(this);
    }
    return orElse();
  }
}

abstract class AccountDetailsUpdateFailure implements AccountManagementFailure {
  const factory AccountDetailsUpdateFailure(final String failure) =
      _$AccountDetailsUpdateFailureImpl;

  @override
  String get failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountDetailsUpdateFailureImplCopyWith<_$AccountDetailsUpdateFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AccountPasswordUpdateFailureImplCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory _$$AccountPasswordUpdateFailureImplCopyWith(
          _$AccountPasswordUpdateFailureImpl value,
          $Res Function(_$AccountPasswordUpdateFailureImpl) then) =
      __$$AccountPasswordUpdateFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class __$$AccountPasswordUpdateFailureImplCopyWithImpl<$Res>
    extends _$AccountManagementFailureCopyWithImpl<$Res,
        _$AccountPasswordUpdateFailureImpl>
    implements _$$AccountPasswordUpdateFailureImplCopyWith<$Res> {
  __$$AccountPasswordUpdateFailureImplCopyWithImpl(
      _$AccountPasswordUpdateFailureImpl _value,
      $Res Function(_$AccountPasswordUpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$AccountPasswordUpdateFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AccountPasswordUpdateFailureImpl
    implements AccountPasswordUpdateFailure {
  const _$AccountPasswordUpdateFailureImpl(this.failure);

  @override
  final String failure;

  @override
  String toString() {
    return 'AccountManagementFailure.accountPasswordUpdateFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountPasswordUpdateFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountPasswordUpdateFailureImplCopyWith<
          _$AccountPasswordUpdateFailureImpl>
      get copyWith => __$$AccountPasswordUpdateFailureImplCopyWithImpl<
          _$AccountPasswordUpdateFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failure) accountDetailsLoadFailure,
    required TResult Function(String failure) accountDetailsUpdateFailure,
    required TResult Function(String failure) accountPasswordUpdateFailure,
    required TResult Function(String failure) accountEmailUpdateFailure,
    required TResult Function(String failure) accountPhoneNumberUpdateFailure,
    required TResult Function(String failure) onboardingDataAddFailure,
  }) {
    return accountPasswordUpdateFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failure)? accountDetailsLoadFailure,
    TResult? Function(String failure)? accountDetailsUpdateFailure,
    TResult? Function(String failure)? accountPasswordUpdateFailure,
    TResult? Function(String failure)? accountEmailUpdateFailure,
    TResult? Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult? Function(String failure)? onboardingDataAddFailure,
  }) {
    return accountPasswordUpdateFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failure)? accountDetailsLoadFailure,
    TResult Function(String failure)? accountDetailsUpdateFailure,
    TResult Function(String failure)? accountPasswordUpdateFailure,
    TResult Function(String failure)? accountEmailUpdateFailure,
    TResult Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult Function(String failure)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountPasswordUpdateFailure != null) {
      return accountPasswordUpdateFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountDetailsLoadFailure value)
        accountDetailsLoadFailure,
    required TResult Function(AccountDetailsUpdateFailure value)
        accountDetailsUpdateFailure,
    required TResult Function(AccountPasswordUpdateFailure value)
        accountPasswordUpdateFailure,
    required TResult Function(AccountEmailUpdateFailure value)
        accountEmailUpdateFailure,
    required TResult Function(AccountPhoneNumberUpdateFailure value)
        accountPhoneNumberUpdateFailure,
    required TResult Function(OnboardingDataAddFailure value)
        onboardingDataAddFailure,
  }) {
    return accountPasswordUpdateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult? Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult? Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult? Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult? Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult? Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
  }) {
    return accountPasswordUpdateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountPasswordUpdateFailure != null) {
      return accountPasswordUpdateFailure(this);
    }
    return orElse();
  }
}

abstract class AccountPasswordUpdateFailure
    implements AccountManagementFailure {
  const factory AccountPasswordUpdateFailure(final String failure) =
      _$AccountPasswordUpdateFailureImpl;

  @override
  String get failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountPasswordUpdateFailureImplCopyWith<
          _$AccountPasswordUpdateFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AccountEmailUpdateFailureImplCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory _$$AccountEmailUpdateFailureImplCopyWith(
          _$AccountEmailUpdateFailureImpl value,
          $Res Function(_$AccountEmailUpdateFailureImpl) then) =
      __$$AccountEmailUpdateFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class __$$AccountEmailUpdateFailureImplCopyWithImpl<$Res>
    extends _$AccountManagementFailureCopyWithImpl<$Res,
        _$AccountEmailUpdateFailureImpl>
    implements _$$AccountEmailUpdateFailureImplCopyWith<$Res> {
  __$$AccountEmailUpdateFailureImplCopyWithImpl(
      _$AccountEmailUpdateFailureImpl _value,
      $Res Function(_$AccountEmailUpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$AccountEmailUpdateFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AccountEmailUpdateFailureImpl implements AccountEmailUpdateFailure {
  const _$AccountEmailUpdateFailureImpl(this.failure);

  @override
  final String failure;

  @override
  String toString() {
    return 'AccountManagementFailure.accountEmailUpdateFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountEmailUpdateFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountEmailUpdateFailureImplCopyWith<_$AccountEmailUpdateFailureImpl>
      get copyWith => __$$AccountEmailUpdateFailureImplCopyWithImpl<
          _$AccountEmailUpdateFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failure) accountDetailsLoadFailure,
    required TResult Function(String failure) accountDetailsUpdateFailure,
    required TResult Function(String failure) accountPasswordUpdateFailure,
    required TResult Function(String failure) accountEmailUpdateFailure,
    required TResult Function(String failure) accountPhoneNumberUpdateFailure,
    required TResult Function(String failure) onboardingDataAddFailure,
  }) {
    return accountEmailUpdateFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failure)? accountDetailsLoadFailure,
    TResult? Function(String failure)? accountDetailsUpdateFailure,
    TResult? Function(String failure)? accountPasswordUpdateFailure,
    TResult? Function(String failure)? accountEmailUpdateFailure,
    TResult? Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult? Function(String failure)? onboardingDataAddFailure,
  }) {
    return accountEmailUpdateFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failure)? accountDetailsLoadFailure,
    TResult Function(String failure)? accountDetailsUpdateFailure,
    TResult Function(String failure)? accountPasswordUpdateFailure,
    TResult Function(String failure)? accountEmailUpdateFailure,
    TResult Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult Function(String failure)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountEmailUpdateFailure != null) {
      return accountEmailUpdateFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountDetailsLoadFailure value)
        accountDetailsLoadFailure,
    required TResult Function(AccountDetailsUpdateFailure value)
        accountDetailsUpdateFailure,
    required TResult Function(AccountPasswordUpdateFailure value)
        accountPasswordUpdateFailure,
    required TResult Function(AccountEmailUpdateFailure value)
        accountEmailUpdateFailure,
    required TResult Function(AccountPhoneNumberUpdateFailure value)
        accountPhoneNumberUpdateFailure,
    required TResult Function(OnboardingDataAddFailure value)
        onboardingDataAddFailure,
  }) {
    return accountEmailUpdateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult? Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult? Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult? Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult? Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult? Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
  }) {
    return accountEmailUpdateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountEmailUpdateFailure != null) {
      return accountEmailUpdateFailure(this);
    }
    return orElse();
  }
}

abstract class AccountEmailUpdateFailure implements AccountManagementFailure {
  const factory AccountEmailUpdateFailure(final String failure) =
      _$AccountEmailUpdateFailureImpl;

  @override
  String get failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountEmailUpdateFailureImplCopyWith<_$AccountEmailUpdateFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AccountPhoneNumberUpdateFailureImplCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory _$$AccountPhoneNumberUpdateFailureImplCopyWith(
          _$AccountPhoneNumberUpdateFailureImpl value,
          $Res Function(_$AccountPhoneNumberUpdateFailureImpl) then) =
      __$$AccountPhoneNumberUpdateFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class __$$AccountPhoneNumberUpdateFailureImplCopyWithImpl<$Res>
    extends _$AccountManagementFailureCopyWithImpl<$Res,
        _$AccountPhoneNumberUpdateFailureImpl>
    implements _$$AccountPhoneNumberUpdateFailureImplCopyWith<$Res> {
  __$$AccountPhoneNumberUpdateFailureImplCopyWithImpl(
      _$AccountPhoneNumberUpdateFailureImpl _value,
      $Res Function(_$AccountPhoneNumberUpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$AccountPhoneNumberUpdateFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AccountPhoneNumberUpdateFailureImpl
    implements AccountPhoneNumberUpdateFailure {
  const _$AccountPhoneNumberUpdateFailureImpl(this.failure);

  @override
  final String failure;

  @override
  String toString() {
    return 'AccountManagementFailure.accountPhoneNumberUpdateFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountPhoneNumberUpdateFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountPhoneNumberUpdateFailureImplCopyWith<
          _$AccountPhoneNumberUpdateFailureImpl>
      get copyWith => __$$AccountPhoneNumberUpdateFailureImplCopyWithImpl<
          _$AccountPhoneNumberUpdateFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failure) accountDetailsLoadFailure,
    required TResult Function(String failure) accountDetailsUpdateFailure,
    required TResult Function(String failure) accountPasswordUpdateFailure,
    required TResult Function(String failure) accountEmailUpdateFailure,
    required TResult Function(String failure) accountPhoneNumberUpdateFailure,
    required TResult Function(String failure) onboardingDataAddFailure,
  }) {
    return accountPhoneNumberUpdateFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failure)? accountDetailsLoadFailure,
    TResult? Function(String failure)? accountDetailsUpdateFailure,
    TResult? Function(String failure)? accountPasswordUpdateFailure,
    TResult? Function(String failure)? accountEmailUpdateFailure,
    TResult? Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult? Function(String failure)? onboardingDataAddFailure,
  }) {
    return accountPhoneNumberUpdateFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failure)? accountDetailsLoadFailure,
    TResult Function(String failure)? accountDetailsUpdateFailure,
    TResult Function(String failure)? accountPasswordUpdateFailure,
    TResult Function(String failure)? accountEmailUpdateFailure,
    TResult Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult Function(String failure)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountPhoneNumberUpdateFailure != null) {
      return accountPhoneNumberUpdateFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountDetailsLoadFailure value)
        accountDetailsLoadFailure,
    required TResult Function(AccountDetailsUpdateFailure value)
        accountDetailsUpdateFailure,
    required TResult Function(AccountPasswordUpdateFailure value)
        accountPasswordUpdateFailure,
    required TResult Function(AccountEmailUpdateFailure value)
        accountEmailUpdateFailure,
    required TResult Function(AccountPhoneNumberUpdateFailure value)
        accountPhoneNumberUpdateFailure,
    required TResult Function(OnboardingDataAddFailure value)
        onboardingDataAddFailure,
  }) {
    return accountPhoneNumberUpdateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult? Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult? Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult? Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult? Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult? Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
  }) {
    return accountPhoneNumberUpdateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (accountPhoneNumberUpdateFailure != null) {
      return accountPhoneNumberUpdateFailure(this);
    }
    return orElse();
  }
}

abstract class AccountPhoneNumberUpdateFailure
    implements AccountManagementFailure {
  const factory AccountPhoneNumberUpdateFailure(final String failure) =
      _$AccountPhoneNumberUpdateFailureImpl;

  @override
  String get failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountPhoneNumberUpdateFailureImplCopyWith<
          _$AccountPhoneNumberUpdateFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnboardingDataAddFailureImplCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory _$$OnboardingDataAddFailureImplCopyWith(
          _$OnboardingDataAddFailureImpl value,
          $Res Function(_$OnboardingDataAddFailureImpl) then) =
      __$$OnboardingDataAddFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class __$$OnboardingDataAddFailureImplCopyWithImpl<$Res>
    extends _$AccountManagementFailureCopyWithImpl<$Res,
        _$OnboardingDataAddFailureImpl>
    implements _$$OnboardingDataAddFailureImplCopyWith<$Res> {
  __$$OnboardingDataAddFailureImplCopyWithImpl(
      _$OnboardingDataAddFailureImpl _value,
      $Res Function(_$OnboardingDataAddFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$OnboardingDataAddFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OnboardingDataAddFailureImpl implements OnboardingDataAddFailure {
  const _$OnboardingDataAddFailureImpl(this.failure);

  @override
  final String failure;

  @override
  String toString() {
    return 'AccountManagementFailure.onboardingDataAddFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnboardingDataAddFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OnboardingDataAddFailureImplCopyWith<_$OnboardingDataAddFailureImpl>
      get copyWith => __$$OnboardingDataAddFailureImplCopyWithImpl<
          _$OnboardingDataAddFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failure) accountDetailsLoadFailure,
    required TResult Function(String failure) accountDetailsUpdateFailure,
    required TResult Function(String failure) accountPasswordUpdateFailure,
    required TResult Function(String failure) accountEmailUpdateFailure,
    required TResult Function(String failure) accountPhoneNumberUpdateFailure,
    required TResult Function(String failure) onboardingDataAddFailure,
  }) {
    return onboardingDataAddFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failure)? accountDetailsLoadFailure,
    TResult? Function(String failure)? accountDetailsUpdateFailure,
    TResult? Function(String failure)? accountPasswordUpdateFailure,
    TResult? Function(String failure)? accountEmailUpdateFailure,
    TResult? Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult? Function(String failure)? onboardingDataAddFailure,
  }) {
    return onboardingDataAddFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failure)? accountDetailsLoadFailure,
    TResult Function(String failure)? accountDetailsUpdateFailure,
    TResult Function(String failure)? accountPasswordUpdateFailure,
    TResult Function(String failure)? accountEmailUpdateFailure,
    TResult Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult Function(String failure)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (onboardingDataAddFailure != null) {
      return onboardingDataAddFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountDetailsLoadFailure value)
        accountDetailsLoadFailure,
    required TResult Function(AccountDetailsUpdateFailure value)
        accountDetailsUpdateFailure,
    required TResult Function(AccountPasswordUpdateFailure value)
        accountPasswordUpdateFailure,
    required TResult Function(AccountEmailUpdateFailure value)
        accountEmailUpdateFailure,
    required TResult Function(AccountPhoneNumberUpdateFailure value)
        accountPhoneNumberUpdateFailure,
    required TResult Function(OnboardingDataAddFailure value)
        onboardingDataAddFailure,
  }) {
    return onboardingDataAddFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult? Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult? Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult? Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult? Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult? Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
  }) {
    return onboardingDataAddFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    if (onboardingDataAddFailure != null) {
      return onboardingDataAddFailure(this);
    }
    return orElse();
  }
}

abstract class OnboardingDataAddFailure implements AccountManagementFailure {
  const factory OnboardingDataAddFailure(final String failure) =
      _$OnboardingDataAddFailureImpl;

  @override
  String get failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OnboardingDataAddFailureImplCopyWith<_$OnboardingDataAddFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}
