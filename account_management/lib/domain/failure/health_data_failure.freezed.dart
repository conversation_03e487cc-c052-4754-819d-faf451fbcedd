// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'health_data_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HealthDataFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() periodLengthFailure,
    required TResult Function() cycleLengthFailure,
    required TResult Function() ovulationDateFailure,
    required TResult Function() contraceptionTypeFailure,
    required TResult Function() unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? periodLengthFailure,
    TResult? Function()? cycleLengthFailure,
    TResult? Function()? ovulationDateFailure,
    TResult? Function()? contraceptionTypeFailure,
    TResult? Function()? unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? periodLengthFailure,
    TResult Function()? cycleLengthFailure,
    TResult Function()? ovulationDateFailure,
    TResult Function()? contraceptionTypeFailure,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PeriodLengthFailure value) periodLengthFailure,
    required TResult Function(CycleLengthFailure value) cycleLengthFailure,
    required TResult Function(OvulationDateFailure value) ovulationDateFailure,
    required TResult Function(ContraceptionTypeFailure value)
        contraceptionTypeFailure,
    required TResult Function(Unexpected value) unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult? Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult? Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult? Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult? Function(Unexpected value)? unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HealthDataFailureCopyWith<$Res> {
  factory $HealthDataFailureCopyWith(
          HealthDataFailure value, $Res Function(HealthDataFailure) then) =
      _$HealthDataFailureCopyWithImpl<$Res, HealthDataFailure>;
}

/// @nodoc
class _$HealthDataFailureCopyWithImpl<$Res, $Val extends HealthDataFailure>
    implements $HealthDataFailureCopyWith<$Res> {
  _$HealthDataFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HealthDataFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PeriodLengthFailureImplCopyWith<$Res> {
  factory _$$PeriodLengthFailureImplCopyWith(_$PeriodLengthFailureImpl value,
          $Res Function(_$PeriodLengthFailureImpl) then) =
      __$$PeriodLengthFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PeriodLengthFailureImplCopyWithImpl<$Res>
    extends _$HealthDataFailureCopyWithImpl<$Res, _$PeriodLengthFailureImpl>
    implements _$$PeriodLengthFailureImplCopyWith<$Res> {
  __$$PeriodLengthFailureImplCopyWithImpl(_$PeriodLengthFailureImpl _value,
      $Res Function(_$PeriodLengthFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of HealthDataFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PeriodLengthFailureImpl implements PeriodLengthFailure {
  const _$PeriodLengthFailureImpl();

  @override
  String toString() {
    return 'HealthDataFailure.periodLengthFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodLengthFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() periodLengthFailure,
    required TResult Function() cycleLengthFailure,
    required TResult Function() ovulationDateFailure,
    required TResult Function() contraceptionTypeFailure,
    required TResult Function() unexpected,
  }) {
    return periodLengthFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? periodLengthFailure,
    TResult? Function()? cycleLengthFailure,
    TResult? Function()? ovulationDateFailure,
    TResult? Function()? contraceptionTypeFailure,
    TResult? Function()? unexpected,
  }) {
    return periodLengthFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? periodLengthFailure,
    TResult Function()? cycleLengthFailure,
    TResult Function()? ovulationDateFailure,
    TResult Function()? contraceptionTypeFailure,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (periodLengthFailure != null) {
      return periodLengthFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PeriodLengthFailure value) periodLengthFailure,
    required TResult Function(CycleLengthFailure value) cycleLengthFailure,
    required TResult Function(OvulationDateFailure value) ovulationDateFailure,
    required TResult Function(ContraceptionTypeFailure value)
        contraceptionTypeFailure,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return periodLengthFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult? Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult? Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult? Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return periodLengthFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (periodLengthFailure != null) {
      return periodLengthFailure(this);
    }
    return orElse();
  }
}

abstract class PeriodLengthFailure implements HealthDataFailure {
  const factory PeriodLengthFailure() = _$PeriodLengthFailureImpl;
}

/// @nodoc
abstract class _$$CycleLengthFailureImplCopyWith<$Res> {
  factory _$$CycleLengthFailureImplCopyWith(_$CycleLengthFailureImpl value,
          $Res Function(_$CycleLengthFailureImpl) then) =
      __$$CycleLengthFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CycleLengthFailureImplCopyWithImpl<$Res>
    extends _$HealthDataFailureCopyWithImpl<$Res, _$CycleLengthFailureImpl>
    implements _$$CycleLengthFailureImplCopyWith<$Res> {
  __$$CycleLengthFailureImplCopyWithImpl(_$CycleLengthFailureImpl _value,
      $Res Function(_$CycleLengthFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of HealthDataFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CycleLengthFailureImpl implements CycleLengthFailure {
  const _$CycleLengthFailureImpl();

  @override
  String toString() {
    return 'HealthDataFailure.cycleLengthFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CycleLengthFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() periodLengthFailure,
    required TResult Function() cycleLengthFailure,
    required TResult Function() ovulationDateFailure,
    required TResult Function() contraceptionTypeFailure,
    required TResult Function() unexpected,
  }) {
    return cycleLengthFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? periodLengthFailure,
    TResult? Function()? cycleLengthFailure,
    TResult? Function()? ovulationDateFailure,
    TResult? Function()? contraceptionTypeFailure,
    TResult? Function()? unexpected,
  }) {
    return cycleLengthFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? periodLengthFailure,
    TResult Function()? cycleLengthFailure,
    TResult Function()? ovulationDateFailure,
    TResult Function()? contraceptionTypeFailure,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (cycleLengthFailure != null) {
      return cycleLengthFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PeriodLengthFailure value) periodLengthFailure,
    required TResult Function(CycleLengthFailure value) cycleLengthFailure,
    required TResult Function(OvulationDateFailure value) ovulationDateFailure,
    required TResult Function(ContraceptionTypeFailure value)
        contraceptionTypeFailure,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return cycleLengthFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult? Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult? Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult? Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return cycleLengthFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (cycleLengthFailure != null) {
      return cycleLengthFailure(this);
    }
    return orElse();
  }
}

abstract class CycleLengthFailure implements HealthDataFailure {
  const factory CycleLengthFailure() = _$CycleLengthFailureImpl;
}

/// @nodoc
abstract class _$$OvulationDateFailureImplCopyWith<$Res> {
  factory _$$OvulationDateFailureImplCopyWith(_$OvulationDateFailureImpl value,
          $Res Function(_$OvulationDateFailureImpl) then) =
      __$$OvulationDateFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OvulationDateFailureImplCopyWithImpl<$Res>
    extends _$HealthDataFailureCopyWithImpl<$Res, _$OvulationDateFailureImpl>
    implements _$$OvulationDateFailureImplCopyWith<$Res> {
  __$$OvulationDateFailureImplCopyWithImpl(_$OvulationDateFailureImpl _value,
      $Res Function(_$OvulationDateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of HealthDataFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OvulationDateFailureImpl implements OvulationDateFailure {
  const _$OvulationDateFailureImpl();

  @override
  String toString() {
    return 'HealthDataFailure.ovulationDateFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OvulationDateFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() periodLengthFailure,
    required TResult Function() cycleLengthFailure,
    required TResult Function() ovulationDateFailure,
    required TResult Function() contraceptionTypeFailure,
    required TResult Function() unexpected,
  }) {
    return ovulationDateFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? periodLengthFailure,
    TResult? Function()? cycleLengthFailure,
    TResult? Function()? ovulationDateFailure,
    TResult? Function()? contraceptionTypeFailure,
    TResult? Function()? unexpected,
  }) {
    return ovulationDateFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? periodLengthFailure,
    TResult Function()? cycleLengthFailure,
    TResult Function()? ovulationDateFailure,
    TResult Function()? contraceptionTypeFailure,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (ovulationDateFailure != null) {
      return ovulationDateFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PeriodLengthFailure value) periodLengthFailure,
    required TResult Function(CycleLengthFailure value) cycleLengthFailure,
    required TResult Function(OvulationDateFailure value) ovulationDateFailure,
    required TResult Function(ContraceptionTypeFailure value)
        contraceptionTypeFailure,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return ovulationDateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult? Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult? Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult? Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return ovulationDateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (ovulationDateFailure != null) {
      return ovulationDateFailure(this);
    }
    return orElse();
  }
}

abstract class OvulationDateFailure implements HealthDataFailure {
  const factory OvulationDateFailure() = _$OvulationDateFailureImpl;
}

/// @nodoc
abstract class _$$ContraceptionTypeFailureImplCopyWith<$Res> {
  factory _$$ContraceptionTypeFailureImplCopyWith(
          _$ContraceptionTypeFailureImpl value,
          $Res Function(_$ContraceptionTypeFailureImpl) then) =
      __$$ContraceptionTypeFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ContraceptionTypeFailureImplCopyWithImpl<$Res>
    extends _$HealthDataFailureCopyWithImpl<$Res,
        _$ContraceptionTypeFailureImpl>
    implements _$$ContraceptionTypeFailureImplCopyWith<$Res> {
  __$$ContraceptionTypeFailureImplCopyWithImpl(
      _$ContraceptionTypeFailureImpl _value,
      $Res Function(_$ContraceptionTypeFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of HealthDataFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ContraceptionTypeFailureImpl implements ContraceptionTypeFailure {
  const _$ContraceptionTypeFailureImpl();

  @override
  String toString() {
    return 'HealthDataFailure.contraceptionTypeFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContraceptionTypeFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() periodLengthFailure,
    required TResult Function() cycleLengthFailure,
    required TResult Function() ovulationDateFailure,
    required TResult Function() contraceptionTypeFailure,
    required TResult Function() unexpected,
  }) {
    return contraceptionTypeFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? periodLengthFailure,
    TResult? Function()? cycleLengthFailure,
    TResult? Function()? ovulationDateFailure,
    TResult? Function()? contraceptionTypeFailure,
    TResult? Function()? unexpected,
  }) {
    return contraceptionTypeFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? periodLengthFailure,
    TResult Function()? cycleLengthFailure,
    TResult Function()? ovulationDateFailure,
    TResult Function()? contraceptionTypeFailure,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (contraceptionTypeFailure != null) {
      return contraceptionTypeFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PeriodLengthFailure value) periodLengthFailure,
    required TResult Function(CycleLengthFailure value) cycleLengthFailure,
    required TResult Function(OvulationDateFailure value) ovulationDateFailure,
    required TResult Function(ContraceptionTypeFailure value)
        contraceptionTypeFailure,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return contraceptionTypeFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult? Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult? Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult? Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return contraceptionTypeFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (contraceptionTypeFailure != null) {
      return contraceptionTypeFailure(this);
    }
    return orElse();
  }
}

abstract class ContraceptionTypeFailure implements HealthDataFailure {
  const factory ContraceptionTypeFailure() = _$ContraceptionTypeFailureImpl;
}

/// @nodoc
abstract class _$$UnexpectedImplCopyWith<$Res> {
  factory _$$UnexpectedImplCopyWith(
          _$UnexpectedImpl value, $Res Function(_$UnexpectedImpl) then) =
      __$$UnexpectedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedImplCopyWithImpl<$Res>
    extends _$HealthDataFailureCopyWithImpl<$Res, _$UnexpectedImpl>
    implements _$$UnexpectedImplCopyWith<$Res> {
  __$$UnexpectedImplCopyWithImpl(
      _$UnexpectedImpl _value, $Res Function(_$UnexpectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of HealthDataFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnexpectedImpl implements Unexpected {
  const _$UnexpectedImpl();

  @override
  String toString() {
    return 'HealthDataFailure.unexpected()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnexpectedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() periodLengthFailure,
    required TResult Function() cycleLengthFailure,
    required TResult Function() ovulationDateFailure,
    required TResult Function() contraceptionTypeFailure,
    required TResult Function() unexpected,
  }) {
    return unexpected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? periodLengthFailure,
    TResult? Function()? cycleLengthFailure,
    TResult? Function()? ovulationDateFailure,
    TResult? Function()? contraceptionTypeFailure,
    TResult? Function()? unexpected,
  }) {
    return unexpected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? periodLengthFailure,
    TResult Function()? cycleLengthFailure,
    TResult Function()? ovulationDateFailure,
    TResult Function()? contraceptionTypeFailure,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PeriodLengthFailure value) periodLengthFailure,
    required TResult Function(CycleLengthFailure value) cycleLengthFailure,
    required TResult Function(OvulationDateFailure value) ovulationDateFailure,
    required TResult Function(ContraceptionTypeFailure value)
        contraceptionTypeFailure,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult? Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult? Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult? Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class Unexpected implements HealthDataFailure {
  const factory Unexpected() = _$UnexpectedImpl;
}
