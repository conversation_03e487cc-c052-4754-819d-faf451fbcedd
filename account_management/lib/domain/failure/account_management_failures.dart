import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_management_failures.freezed.dart';

// This class represents different types of Account Management failures.
// It uses the Freezed package for union types.
@freezed
abstract class AccountManagementFailure with _$AccountManagementFailure {
  // Represents a failure where the account details could not be loaded.
  const factory AccountManagementFailure.accountDetailsLoadFailure(String failure) =
      AccountDetailsLoadFailure;

  // Represents a failure where the account details could not be updated.
  const factory AccountManagementFailure.accountDetailsUpdateFailure(String failure) =
      AccountDetailsUpdateFailure;

  // Represents a failure where the account password could not be updated.
  const factory AccountManagementFailure.accountPasswordUpdateFailure(String failure) =
      AccountPasswordUpdateFailure;

  // Represents a failure where the account email could not be updated.
  const factory AccountManagementFailure.accountEmailUpdateFailure(String failure) =
      AccountEmailUpdateFailure;

  // Represents a failure where the account phone number could not be updated.
  const factory AccountManagementFailure.accountPhoneNumberUpdateFailure(String failure) =
      AccountPhoneNumberUpdateFailure;
  // Represents a failure where the onboarding data could not be added.
  const factory AccountManagementFailure.onboardingDataAddFailure(String failure) =
      OnboardingDataAddFailure;
}