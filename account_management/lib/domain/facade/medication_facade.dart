import 'package:fpdart/fpdart.dart';
import '../failure/medication_failure.dart';
import '../model/medication_model.dart';


abstract class MedicationFacade {
  Future<Either<MedicationFailure, Unit>> create(MedicationModel medication);
  Future<Either<MedicationFailure, Unit>> update(MedicationModel medication);
  Future<Either<MedicationFailure, Unit>> delete(MedicationModel medication);
  Stream<Either<MedicationFailure, List<MedicationModel>>> getMedications();
}