import 'package:account_management/account_management.dart';
import 'package:account_management/repository/menstrual_cycle_data_model.dart';
import 'package:fpdart/fpdart.dart';
import '../../repository/period_tracking_data_model.dart';
import '../failure/period_tracking_failure.dart';
import '../model/period_tracking_model.dart';

abstract class PeriodTrackingFacade {
  // This method updates the period tracking details of the currently signed in user.
  Future<Either<PeriodTrackingFailure, Unit>> updatePeriodTrackingDetails(
      PeriodTrackingModel periodTrackingDetails);

  // This method deletes the period tracking details of the currently signed in user.
  Future<Either<PeriodTrackingFailure, Unit>> deletePeriodTrackingDetails(
      PeriodTrackingModel periodTrackingDetails);
  // This method creates the period tracking details of the currently signed in user.
  Future<Either<PeriodTrackingFailure, Unit>> createPeriodTrackingDetails(
      PeriodTrackingModel periodTrackingDetails);
  //select day
  Future<Either<PeriodTrackingFailure, Unit>> selectDay(
      DateTime day, bool format);

  Stream<Either<PeriodTrackingFailure, PeriodTrackingData>>
      watchAllPeriodTrackingData();
  //get all period tracking data
  Future<Either<PeriodTrackingFailure, Unit>> getPeriodTrackingDetails();
  //dispise
  Future<void> dispose();

  //toggle symptom
  Future<Either<PeriodTrackingFailure, Unit>> toggleSymptom(
      SymptomModel symptom);

  Future<Either<PeriodTrackingFailure, Unit>> changePainLevel(int painLevel);
  //flow level
  Future<Either<PeriodTrackingFailure, Unit>> changeFlowLevel(int flowLevel);
//calculate ovulation days
  Future<Either<PeriodTrackingFailure, Unit>> calculateOvulationDays();
  //get menstrual cycle
  Stream<Either<PeriodTrackingFailure, MenstrualCycleData>> getMenstrualCycle();

  //get all menstrual cycle data
  Future<Either<PeriodTrackingFailure, MenstrualCycleData>>
      getInitialMenstrualCycleData();

  // Save selected period dates and recalculate future periods and ovulation dates
  Future<Either<PeriodTrackingFailure, Unit>> savePeriodDatesAndRecalculate();

  // Helper method to check if a date is in the future
  bool isDateInFuture(DateTime date);
}
