import 'package:account_management/domain/failure/health_data_failure.dart';
import 'package:fpdart/fpdart.dart';

abstract class HealthDataFacade {
  Future<Either<HealthDataFailure, Unit>> updatePeriodLength(int periodLength);
  Future<Either<HealthDataFailure, Unit>> updateCycleLength(int cycleLength);
  Future<Either<HealthDataFailure, Unit>> updateContraceptionType(
      String contraceptionType);
  Future<Either<HealthDataFailure, Unit>> updateOvulationDate(
      DateTime ovulationDate);
  Future<Either<HealthDataFailure, Unit>> updateNextPeriodStartDate(
      DateTime nextPeriodStartDate);
  Future<Either<HealthDataFailure, Unit>> updateNextOvulationDate(
      DateTime nextOvulationDate);
}
