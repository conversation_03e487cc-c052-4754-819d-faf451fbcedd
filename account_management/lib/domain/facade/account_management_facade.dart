import 'package:account_management/domain/model/account_details_model.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:fpdart/fpdart.dart';
import 'package:image_picker/image_picker.dart';
import '../failure/account_management_failures.dart';

abstract class AccountManagementFacade {
  // This method loads the account details of the currently signed in user.
  Stream<Either<AccountManagementFailure, AccountDetailsModel>> getAccountDetails();

  // This method updates the account details of the currently signed in user.
  Future<Either<AccountManagementFailure, Unit>> updateAccountDetails(
      AccountDetailsModel accountDetails);

  // This method updates the password of the currently signed in user.
  Future<Either<AccountManagementFailure, Unit>> updateAccountPassword(
      String password);

  // This method updates the email of the currently signed in user.
  Future<Either<AccountManagementFailure, Unit>> updateAccountEmail(
      String email);

  // This method updates the phone number of the currently signed in user.
  Future<Either<AccountManagementFailure, Unit>> updateAccountPhoneNumber(
      String phoneNumber);

  Future<Either<AccountManagementFailure, Unit>> updateProfilePicture(
      XFile? profilePicture);
  //delete profile picture
  Future<Either<AccountManagementFailure, Unit>> deleteProfilePicture();
  // Onboarding data
  Future<Either<AccountManagementFailure, Unit>> updateOnboardingData(
      HealthDataModel healthData,DateTime dateOfBirth);
}
