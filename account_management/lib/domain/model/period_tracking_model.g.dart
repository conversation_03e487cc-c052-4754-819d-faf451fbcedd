// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'period_tracking_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PeriodTrackingModel _$PeriodTrackingModelFromJson(Map<String, dynamic> json) =>
    PeriodTrackingModel(
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      symptoms: (json['symptoms'] as List<dynamic>?)
          ?.map((e) => SymptomModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      painLevel: (json['painLevel'] as num?)?.toInt(),
      flowLevel: (json['flowLevel'] as num?)?.toInt(),
      lastUpdated: firestoreTimestampFromJson(json['lastUpdated']),
    );

Map<String, dynamic> _$PeriodTrackingModelToJson(
        PeriodTrackingModel instance) =>
    <String, dynamic>{
      'date': instance.date?.toIso8601String(),
      'symptoms': instance.symptoms?.map((e) => e.toJson()).toList(),
      'painLevel': instance.painLevel,
      'flowLevel': instance.flowLevel,
      'lastUpdated': firestoreTimestampToJson(instance.lastUpdated),
    };

LocalPeriodTrackingModel _$LocalPeriodTrackingModelFromJson(
        Map<String, dynamic> json) =>
    LocalPeriodTrackingModel(
      date: DateTime.parse(json['date'] as String),
      flowLevel: (json['flowLevel'] as num?)?.toInt(),
      painLevel: (json['painLevel'] as num?)?.toInt(),
      symptoms: (json['symptoms'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      synced: json['synced'] as bool? ?? false,
      lastModified: json['lastModified'] == null
          ? null
          : DateTime.parse(json['lastModified'] as String),
      version: (json['version'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$LocalPeriodTrackingModelToJson(
        LocalPeriodTrackingModel instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'flowLevel': instance.flowLevel,
      'painLevel': instance.painLevel,
      'symptoms': instance.symptoms,
      'synced': instance.synced,
      'lastModified': instance.lastModified?.toIso8601String(),
      'version': instance.version,
    };
