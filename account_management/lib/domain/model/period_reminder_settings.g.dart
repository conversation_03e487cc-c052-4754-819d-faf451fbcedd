// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'period_reminder_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PeriodReminderSettings _$PeriodReminderSettingsFromJson(
        Map<String, dynamic> json) =>
    PeriodReminderSettings(
      isPeriodReminderEnabled:
          json['isPeriodReminderEnabled'] as bool? ?? false,
      periodReminderDaysBefore:
          (json['periodReminderDaysBefore'] as num?)?.toInt() ?? 2,
      isOvulationReminderEnabled:
          json['isOvulationReminderEnabled'] as bool? ?? false,
      ovulationReminderDaysBefore:
          (json['ovulationReminderDaysBefore'] as num?)?.toInt() ?? 2,
      lastUpdated: firestoreTimestampFromJson(json['lastUpdated']),
    );

Map<String, dynamic> _$PeriodReminderSettingsToJson(
        PeriodReminderSettings instance) =>
    <String, dynamic>{
      'isPeriodReminderEnabled': instance.isPeriodReminderEnabled,
      'periodReminderDaysBefore': instance.periodReminderDaysBefore,
      'isOvulationReminderEnabled': instance.isOvulationReminderEnabled,
      'ovulationReminderDaysBefore': instance.ovulationReminderDaysBefore,
      'lastUpdated': firestoreTimestampToJson(instance.lastUpdated),
    };
