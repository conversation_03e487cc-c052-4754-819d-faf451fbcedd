// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AccountDetailsModel _$AccountDetailsModelFromJson(Map<String, dynamic> json) =>
    AccountDetailsModel(
      uid: json['uid'] as String?,
      userEmail: json['userEmail'] as String?,
      dateOfBirth: firestoreTimestampFromJson(json['dateOfBirth']),
      userName: json['userName'] as String?,
      photoURL: json['photoURL'] as String?,
      healthData: json['healthData'] == null
          ? null
          : HealthDataModel.fromJson(
              json['healthData'] as Map<String, dynamic>),
      providerData: (json['providerData'] as List<dynamic>?)
          ?.map((e) => ProviderData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AccountDetailsModelToJson(
        AccountDetailsModel instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'userEmail': instance.userEmail,
      'dateOfBirth': firestoreTimestampToJson(instance.dateOfBirth),
      'userName': instance.userName,
      'photoURL': instance.photoURL,
      'healthData': instance.healthData?.toJson(),
      'providerData': instance.providerData?.map((e) => e.toJson()).toList(),
    };
