import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'period_reminder_settings.g.dart';

@JsonSerializable(explicitToJson: true)
class PeriodReminderSettings {
  // Period reminder settings
  final bool isPeriodReminderEnabled;
  final int periodReminderDaysBefore; // 1-5 days

  // Ovulation reminder settings
  final bool isOvulationReminderEnabled;
  final int ovulationReminderDaysBefore; // 1-5 days

  // Timestamp for last update
  @JsonKey(fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  final Timestamp? lastUpdated;

  const PeriodReminderSettings({
    this.isPeriodReminderEnabled = false,
    this.periodReminderDaysBefore = 2,
    this.isOvulationReminderEnabled = false,
    this.ovulationReminderDaysBefore = 2,
    this.lastUpdated,
  });

  factory PeriodReminderSettings.empty() => const PeriodReminderSettings();

  factory PeriodReminderSettings.fromJson(Map<String, dynamic> json) =>
      _$PeriodReminderSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$PeriodReminderSettingsToJson(this);

  PeriodReminderSettings copyWith({
    bool? isPeriodReminderEnabled,
    int? periodReminderDaysBefore,
    bool? isOvulationReminderEnabled,
    int? ovulationReminderDaysBefore,
    Timestamp? lastUpdated,
  }) {
    return PeriodReminderSettings(
      isPeriodReminderEnabled: isPeriodReminderEnabled ?? this.isPeriodReminderEnabled,
      periodReminderDaysBefore: periodReminderDaysBefore ?? this.periodReminderDaysBefore,
      isOvulationReminderEnabled: isOvulationReminderEnabled ?? this.isOvulationReminderEnabled,
      ovulationReminderDaysBefore: ovulationReminderDaysBefore ?? this.ovulationReminderDaysBefore,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PeriodReminderSettings &&
        other.isPeriodReminderEnabled == isPeriodReminderEnabled &&
        other.periodReminderDaysBefore == periodReminderDaysBefore &&
        other.isOvulationReminderEnabled == isOvulationReminderEnabled &&
        other.ovulationReminderDaysBefore == ovulationReminderDaysBefore;
  }

  @override
  int get hashCode {
    return isPeriodReminderEnabled.hashCode ^
        periodReminderDaysBefore.hashCode ^
        isOvulationReminderEnabled.hashCode ^
        ovulationReminderDaysBefore.hashCode;
  }
}

// Helper functions for Firestore Timestamp serialization
Timestamp? firestoreTimestampFromJson(dynamic value) {
  return value;
}

dynamic firestoreTimestampToJson(dynamic value) => value;
