import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'health_data.dart';

part 'account_details_model.g.dart';

// This class represents a user account details model with JSON serialization
@JsonSerializable(explicitToJson: true)
class AccountDetailsModel {
  // User account ID
  String? uid;
  // User account email
  String? userEmail;
  // User Age
  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  Timestamp? dateOfBirth;
  // User account name
  String? userName;

  String? photoURL;
  HealthDataModel? healthData;
  List<ProviderData>? providerData;


  // Constructor for the  AccountDetailsModel class
  AccountDetailsModel({this.uid, this.userEmail, this.dateOfBirth, this.userName,this.photoURL, this.healthData,this.providerData});

  // Method to convert a AccountDetailsModel instance into a JSON map.
  Map<String, dynamic> toJson() => _$AccountDetailsModelToJson(this);

  // Factory constructor to create a AccountDetailsModel instance from a JSON map.
  factory AccountDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$AccountDetailsModelFromJson(json);
}// Convert a Firestore Timestamp from JSON to a Dart DateTime
Timestamp? firestoreTimestampFromJson(dynamic value) {
return value; //value != null ? Timestamp.fromMicrosecondsSinceEpoch(value) : value;
}


// Convert a Dart DateTime to a Firestore Timestamp for JSON serialization
/// This method only stores the "timestamp" data type back in the Firestore
dynamic firestoreTimestampToJson(dynamic value) => value;


class ProviderData {
  final String? email;
  final String? providerId;
  final String? uid;

  ProviderData({this.email, this.providerId, this.uid});

  // Method to convert a ProviderData instance into a JSON map.
  Map<String, dynamic> toJson() => {
        'email': email,
        'providerId': providerId,
        'uid': uid,
      };

  // Factory constructor to create a ProviderData instance from a JSON map.
  factory ProviderData.fromJson(Map<String, dynamic> json) => ProviderData(
        email: json['email'],
        providerId: json['providerId'],
        uid: json['uid'],
      );
}