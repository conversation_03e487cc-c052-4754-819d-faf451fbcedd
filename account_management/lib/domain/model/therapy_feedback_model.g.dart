// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'therapy_feedback_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TherapyFeedbackModel _$TherapyFeedbackModelFromJson(
        Map<String, dynamic> json) =>
    TherapyFeedbackModel(
      id: json['id'] as String,
      therapyId: json['therapyId'] as String,
      painLevelBefore: (json['painLevelBefore'] as num).toInt(),
      painLevelAfter: (json['painLevelAfter'] as num).toInt(),
      feedbackText: json['feedbackText'] as String?,
      satisfactionLevel: (json['satisfactionLevel'] as num?)?.toInt(),
      createdAt: firestoreTimestampFromJson(json['createdAt']),
    );

Map<String, dynamic> _$TherapyFeedbackModelToJson(
        TherapyFeedbackModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'therapyId': instance.therapyId,
      'painLevelBefore': instance.painLevelBefore,
      'painLevelAfter': instance.painLevelAfter,
      'feedbackText': instance.feedbackText,
      'satisfactionLevel': instance.satisfactionLevel,
      'createdAt': firestoreTimestampToJson(instance.createdAt),
    };
