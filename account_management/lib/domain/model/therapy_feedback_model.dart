import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'account_details_model.dart';

part 'therapy_feedback_model.g.dart';

@JsonSerializable()
class TherapyFeedbackModel {
  final String id;
  final String therapyId;
  final int painLevelBefore;
  final int painLevelAfter;
  final String? feedbackText;
  final int? satisfactionLevel; // 1-5 scale
  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  final Timestamp? createdAt;

  TherapyFeedbackModel({
    required this.id,
    required this.therapyId,
    required this.painLevelBefore,
    required this.painLevelAfter,
    this.feedbackText,
    this.satisfactionLevel,
    required this.createdAt,
  });

  String? get feedback => feedbackText;

  factory TherapyFeedbackModel.fromJson(Map<String, dynamic> json) =>
      _$TherapyFeedbackModelFromJson(json);

  Map<String, dynamic> toJson() => _$TherapyFeedbackModelToJson(this);
}
