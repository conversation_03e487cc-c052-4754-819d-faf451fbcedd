// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HealthDataModel _$HealthDataModelFromJson(Map<String, dynamic> json) =>
    HealthDataModel(
      periodLength: (json['periodLength'] as num?)?.toInt(),
      cycleLength: (json['cycleLength'] as num?)?.toInt(),
      contraceptionType: (json['contraceptionType'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      ovulationDate:
          firestoreTimestampFromJson(json['ovulationDate'] as Timestamp?),
      lastPeriodDate:
          firestoreTimestampFromJson(json['lastPeriodDate'] as Timestamp?),
    );

Map<String, dynamic> _$HealthDataModelToJson(HealthDataModel instance) =>
    <String, dynamic>{
      'periodLength': instance.periodLength,
      'cycleLength': instance.cycleLength,
      'contraceptionType': instance.contraceptionType,
      'lastPeriodDate': firestoreTimestampToJson(instance.lastPeriodDate),
      'ovulationDate': firestoreTimestampToJson(instance.ovulationDate),
    };
