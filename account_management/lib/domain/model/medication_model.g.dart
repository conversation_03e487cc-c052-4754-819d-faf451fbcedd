// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medication_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicationModel _$MedicationModelFromJson(Map<String, dynamic> json) =>
    MedicationModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      dosage: json['dosage'] as String?,
      frequency: json['frequency'] as String?,
      frequencyUnit: json['frequencyUnit'] as String?,
      dosageUnit: json['dosageUnit'] as String?,
      daystoBeNotified: (json['daystoBeNotified'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      timeofDay: (json['timeofDay'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isNotificationEnabled: json['isNotificationEnabled'] as bool?,
      notes: json['notes'] as String?,
      userId: json['userId'] as String?,
      monthlyDateToBeNotified: json['monthlyDateToBeNotified'] == null
          ? null
          : DateTime.parse(json['monthlyDateToBeNotified'] as String),
      loggedTimes: (json['loggedTimes'] as List<dynamic>?)
              ?.map((e) => DateTime.parse(e as String))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$MedicationModelToJson(MedicationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'dosage': instance.dosage,
      'frequency': instance.frequency,
      'frequencyUnit': instance.frequencyUnit,
      'dosageUnit': instance.dosageUnit,
      'daystoBeNotified': instance.daystoBeNotified,
      'timeofDay': instance.timeofDay,
      'monthlyDateToBeNotified':
          instance.monthlyDateToBeNotified?.toIso8601String(),
      'isNotificationEnabled': instance.isNotificationEnabled,
      'notes': instance.notes,
      'userId': instance.userId,
      'loggedTimes':
          instance.loggedTimes.map((e) => e.toIso8601String()).toList(),
    };
