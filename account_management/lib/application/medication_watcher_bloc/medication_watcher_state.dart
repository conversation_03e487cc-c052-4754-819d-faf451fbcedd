part of 'medication_watcher_bloc.dart';
// MedicationWatcherState is a freezed union class that will be used to represent the different states that can occur in the MedicationWatcherBloc.
@freezed
class MedicationWatcherState with _$MedicationWatcherState {
  // Represents the initial state of the Medication Watcher bloc
  const factory MedicationWatcherState.initial() = Initial;
  // Represents the state where the medications are being loaded
  const factory MedicationWatcherState.loading() = Loading;
  // Represents the state where the medications have been loaded successfully
  const factory MedicationWatcherState.loadSuccess(List<MedicationModel> medications) = Success;
  // Represents the state where the medications could not be loaded
  const factory MedicationWatcherState.loadFailure(MedicationFailure failure) = LoadFailure;
}
