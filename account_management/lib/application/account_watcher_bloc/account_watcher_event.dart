part of 'account_watcher_bloc.dart';
// AccountWatcherEvent is a freezed union class that will be used to represent the different events that can occur in the AccountWatcherBloc.
@freezed
class AccountWatcherEvent with _$AccountWatcherEvent {
  // Event representing the start of watching all accounts
  const factory AccountWatcherEvent.watchAllStarted() = _WatchAllStarted;
  // Event representing the accounts being received
  const factory AccountWatcherEvent.accountsReceived(
      Either<AccountManagementFailure, AccountDetailsModel> failureOrAccounts) = _AccountsReceived;
}
