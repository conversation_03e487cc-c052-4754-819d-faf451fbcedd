import 'dart:async';

import 'package:account_management/domain/failure/account_management_failures.dart';
import 'package:bloc/bloc.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../domain/facade/account_management_facade.dart';
import '../../domain/model/account_details_model.dart';
import 'package:injectable/injectable.dart';

part 'account_watcher_event.dart';
part 'account_watcher_state.dart';
part 'account_watcher_bloc.freezed.dart';

// Represents the Account Watcher Bloc
@injectable
class AccountWatcherBloc extends Bloc<AccountWatcherEvent, AccountWatcherState> {
  // Represents the account management facade
  final AccountManagementFacade _accountManagementFacade;
  // Represents the account stream subscription
  StreamSubscription<Either<AccountManagementFailure, AccountDetailsModel>>? _accountStreamSubscription;

  // Represents the Account Watcher Bloc
  AccountWatcherBloc(this._accountManagementFacade) : super(const AccountWatcherState.initial()) {
    on<_WatchAllStarted>(_onAccountWatcherEvent);
    on<_AccountsReceived>(_onAccountReceived);
  }

  // Represents the Account Watcher Event
  Future<void> _onAccountWatcherEvent(_WatchAllStarted event, Emitter<AccountWatcherState> emit) async {
    emit(const AccountWatcherState.loading());
    await _accountStreamSubscription?.cancel();
    // Represents the account stream subscription
    _accountStreamSubscription = _accountManagementFacade.getAccountDetails().listen(
      (failureOrAccounts) {
        if (isClosed) return; // Prevent adding events after close
        add(_AccountsReceived(failureOrAccounts));
      },
    );
  }

  // Represents the Account Watcher Event
  Future<void> _onAccountReceived(_AccountsReceived event, Emitter<AccountWatcherState> emit) async {
    event.failureOrAccounts.mapBoth(
      onLeft: (failure) => emit(AccountWatcherState.loadFailure(failure)),
      onRight: (accountDetails) => emit(AccountWatcherState.loadSuccess(accountDetails)),
    );
  }

  @override
  Future<void> close() async {
    // Represents the account stream subscription cancellation
    await _accountStreamSubscription?.cancel();
    return super.close();
  }
}