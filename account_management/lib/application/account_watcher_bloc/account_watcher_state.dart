// This part directive indicates that the file is a part of 'account_watcher_bloc.dart',
// meaning it shares context with it, such as imports and declarations.
part of 'account_watcher_bloc.dart';

// The AccountWatcherState class is defined using the freezed package to implement an immutable state management system.
// This class uses the mixin _$AccountWatcherState for code generation purposes, enabling pattern matching and copyWith methods.
@freezed
class AccountWatcherState with _$AccountWatcherState {
  // Represents the initial state of the account watcher, typically indicating that no action has been taken yet.
  const factory AccountWatcherState.initial() = _Initial;

  // Represents the loading state, indicating that the account details are currently being fetched.
  const factory AccountWatcherState.loading() = _Loading;

  // Represents the state of successful account details loading, containing the loaded account details.
  const factory AccountWatcherState.loadSuccess(AccountDetailsModel accountDetails) = _LoadSuccess;

  // Represents the failure state in loading account details, containing the error or failure information.
  const factory AccountWatcherState.loadFailure(AccountManagementFailure failure) = _LoadFailure;
}