// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_health_data_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$UpdateHealthDataEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int cycleLength) updateCycleLength,
    required TResult Function(int periodLength) updatePeriodLength,
    required TResult Function(DateTime ovulationDate) updateOvulationDate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int cycleLength)? updateCycleLength,
    TResult? Function(int periodLength)? updatePeriodLength,
    TResult? Function(DateTime ovulationDate)? updateOvulationDate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int cycleLength)? updateCycleLength,
    TResult Function(int periodLength)? updatePeriodLength,
    TResult Function(DateTime ovulationDate)? updateOvulationDate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateCycleLength value) updateCycleLength,
    required TResult Function(UpdatePeriodLength value) updatePeriodLength,
    required TResult Function(UpdateOvulationDate value) updateOvulationDate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateCycleLength value)? updateCycleLength,
    TResult? Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult? Function(UpdateOvulationDate value)? updateOvulationDate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateCycleLength value)? updateCycleLength,
    TResult Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult Function(UpdateOvulationDate value)? updateOvulationDate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateHealthDataEventCopyWith<$Res> {
  factory $UpdateHealthDataEventCopyWith(UpdateHealthDataEvent value,
          $Res Function(UpdateHealthDataEvent) then) =
      _$UpdateHealthDataEventCopyWithImpl<$Res, UpdateHealthDataEvent>;
}

/// @nodoc
class _$UpdateHealthDataEventCopyWithImpl<$Res,
        $Val extends UpdateHealthDataEvent>
    implements $UpdateHealthDataEventCopyWith<$Res> {
  _$UpdateHealthDataEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$UpdateCycleLengthImplCopyWith<$Res> {
  factory _$$UpdateCycleLengthImplCopyWith(_$UpdateCycleLengthImpl value,
          $Res Function(_$UpdateCycleLengthImpl) then) =
      __$$UpdateCycleLengthImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int cycleLength});
}

/// @nodoc
class __$$UpdateCycleLengthImplCopyWithImpl<$Res>
    extends _$UpdateHealthDataEventCopyWithImpl<$Res, _$UpdateCycleLengthImpl>
    implements _$$UpdateCycleLengthImplCopyWith<$Res> {
  __$$UpdateCycleLengthImplCopyWithImpl(_$UpdateCycleLengthImpl _value,
      $Res Function(_$UpdateCycleLengthImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cycleLength = null,
  }) {
    return _then(_$UpdateCycleLengthImpl(
      null == cycleLength
          ? _value.cycleLength
          : cycleLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$UpdateCycleLengthImpl implements UpdateCycleLength {
  const _$UpdateCycleLengthImpl(this.cycleLength);

  @override
  final int cycleLength;

  @override
  String toString() {
    return 'UpdateHealthDataEvent.updateCycleLength(cycleLength: $cycleLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateCycleLengthImpl &&
            (identical(other.cycleLength, cycleLength) ||
                other.cycleLength == cycleLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cycleLength);

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateCycleLengthImplCopyWith<_$UpdateCycleLengthImpl> get copyWith =>
      __$$UpdateCycleLengthImplCopyWithImpl<_$UpdateCycleLengthImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int cycleLength) updateCycleLength,
    required TResult Function(int periodLength) updatePeriodLength,
    required TResult Function(DateTime ovulationDate) updateOvulationDate,
  }) {
    return updateCycleLength(cycleLength);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int cycleLength)? updateCycleLength,
    TResult? Function(int periodLength)? updatePeriodLength,
    TResult? Function(DateTime ovulationDate)? updateOvulationDate,
  }) {
    return updateCycleLength?.call(cycleLength);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int cycleLength)? updateCycleLength,
    TResult Function(int periodLength)? updatePeriodLength,
    TResult Function(DateTime ovulationDate)? updateOvulationDate,
    required TResult orElse(),
  }) {
    if (updateCycleLength != null) {
      return updateCycleLength(cycleLength);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateCycleLength value) updateCycleLength,
    required TResult Function(UpdatePeriodLength value) updatePeriodLength,
    required TResult Function(UpdateOvulationDate value) updateOvulationDate,
  }) {
    return updateCycleLength(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateCycleLength value)? updateCycleLength,
    TResult? Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult? Function(UpdateOvulationDate value)? updateOvulationDate,
  }) {
    return updateCycleLength?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateCycleLength value)? updateCycleLength,
    TResult Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult Function(UpdateOvulationDate value)? updateOvulationDate,
    required TResult orElse(),
  }) {
    if (updateCycleLength != null) {
      return updateCycleLength(this);
    }
    return orElse();
  }
}

abstract class UpdateCycleLength implements UpdateHealthDataEvent {
  const factory UpdateCycleLength(final int cycleLength) =
      _$UpdateCycleLengthImpl;

  int get cycleLength;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateCycleLengthImplCopyWith<_$UpdateCycleLengthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatePeriodLengthImplCopyWith<$Res> {
  factory _$$UpdatePeriodLengthImplCopyWith(_$UpdatePeriodLengthImpl value,
          $Res Function(_$UpdatePeriodLengthImpl) then) =
      __$$UpdatePeriodLengthImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int periodLength});
}

/// @nodoc
class __$$UpdatePeriodLengthImplCopyWithImpl<$Res>
    extends _$UpdateHealthDataEventCopyWithImpl<$Res, _$UpdatePeriodLengthImpl>
    implements _$$UpdatePeriodLengthImplCopyWith<$Res> {
  __$$UpdatePeriodLengthImplCopyWithImpl(_$UpdatePeriodLengthImpl _value,
      $Res Function(_$UpdatePeriodLengthImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodLength = null,
  }) {
    return _then(_$UpdatePeriodLengthImpl(
      null == periodLength
          ? _value.periodLength
          : periodLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$UpdatePeriodLengthImpl implements UpdatePeriodLength {
  const _$UpdatePeriodLengthImpl(this.periodLength);

  @override
  final int periodLength;

  @override
  String toString() {
    return 'UpdateHealthDataEvent.updatePeriodLength(periodLength: $periodLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatePeriodLengthImpl &&
            (identical(other.periodLength, periodLength) ||
                other.periodLength == periodLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, periodLength);

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatePeriodLengthImplCopyWith<_$UpdatePeriodLengthImpl> get copyWith =>
      __$$UpdatePeriodLengthImplCopyWithImpl<_$UpdatePeriodLengthImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int cycleLength) updateCycleLength,
    required TResult Function(int periodLength) updatePeriodLength,
    required TResult Function(DateTime ovulationDate) updateOvulationDate,
  }) {
    return updatePeriodLength(periodLength);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int cycleLength)? updateCycleLength,
    TResult? Function(int periodLength)? updatePeriodLength,
    TResult? Function(DateTime ovulationDate)? updateOvulationDate,
  }) {
    return updatePeriodLength?.call(periodLength);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int cycleLength)? updateCycleLength,
    TResult Function(int periodLength)? updatePeriodLength,
    TResult Function(DateTime ovulationDate)? updateOvulationDate,
    required TResult orElse(),
  }) {
    if (updatePeriodLength != null) {
      return updatePeriodLength(periodLength);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateCycleLength value) updateCycleLength,
    required TResult Function(UpdatePeriodLength value) updatePeriodLength,
    required TResult Function(UpdateOvulationDate value) updateOvulationDate,
  }) {
    return updatePeriodLength(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateCycleLength value)? updateCycleLength,
    TResult? Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult? Function(UpdateOvulationDate value)? updateOvulationDate,
  }) {
    return updatePeriodLength?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateCycleLength value)? updateCycleLength,
    TResult Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult Function(UpdateOvulationDate value)? updateOvulationDate,
    required TResult orElse(),
  }) {
    if (updatePeriodLength != null) {
      return updatePeriodLength(this);
    }
    return orElse();
  }
}

abstract class UpdatePeriodLength implements UpdateHealthDataEvent {
  const factory UpdatePeriodLength(final int periodLength) =
      _$UpdatePeriodLengthImpl;

  int get periodLength;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatePeriodLengthImplCopyWith<_$UpdatePeriodLengthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateOvulationDateImplCopyWith<$Res> {
  factory _$$UpdateOvulationDateImplCopyWith(_$UpdateOvulationDateImpl value,
          $Res Function(_$UpdateOvulationDateImpl) then) =
      __$$UpdateOvulationDateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime ovulationDate});
}

/// @nodoc
class __$$UpdateOvulationDateImplCopyWithImpl<$Res>
    extends _$UpdateHealthDataEventCopyWithImpl<$Res, _$UpdateOvulationDateImpl>
    implements _$$UpdateOvulationDateImplCopyWith<$Res> {
  __$$UpdateOvulationDateImplCopyWithImpl(_$UpdateOvulationDateImpl _value,
      $Res Function(_$UpdateOvulationDateImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ovulationDate = null,
  }) {
    return _then(_$UpdateOvulationDateImpl(
      null == ovulationDate
          ? _value.ovulationDate
          : ovulationDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$UpdateOvulationDateImpl implements UpdateOvulationDate {
  const _$UpdateOvulationDateImpl(this.ovulationDate);

  @override
  final DateTime ovulationDate;

  @override
  String toString() {
    return 'UpdateHealthDataEvent.updateOvulationDate(ovulationDate: $ovulationDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateOvulationDateImpl &&
            (identical(other.ovulationDate, ovulationDate) ||
                other.ovulationDate == ovulationDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ovulationDate);

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateOvulationDateImplCopyWith<_$UpdateOvulationDateImpl> get copyWith =>
      __$$UpdateOvulationDateImplCopyWithImpl<_$UpdateOvulationDateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int cycleLength) updateCycleLength,
    required TResult Function(int periodLength) updatePeriodLength,
    required TResult Function(DateTime ovulationDate) updateOvulationDate,
  }) {
    return updateOvulationDate(ovulationDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int cycleLength)? updateCycleLength,
    TResult? Function(int periodLength)? updatePeriodLength,
    TResult? Function(DateTime ovulationDate)? updateOvulationDate,
  }) {
    return updateOvulationDate?.call(ovulationDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int cycleLength)? updateCycleLength,
    TResult Function(int periodLength)? updatePeriodLength,
    TResult Function(DateTime ovulationDate)? updateOvulationDate,
    required TResult orElse(),
  }) {
    if (updateOvulationDate != null) {
      return updateOvulationDate(ovulationDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateCycleLength value) updateCycleLength,
    required TResult Function(UpdatePeriodLength value) updatePeriodLength,
    required TResult Function(UpdateOvulationDate value) updateOvulationDate,
  }) {
    return updateOvulationDate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateCycleLength value)? updateCycleLength,
    TResult? Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult? Function(UpdateOvulationDate value)? updateOvulationDate,
  }) {
    return updateOvulationDate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateCycleLength value)? updateCycleLength,
    TResult Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult Function(UpdateOvulationDate value)? updateOvulationDate,
    required TResult orElse(),
  }) {
    if (updateOvulationDate != null) {
      return updateOvulationDate(this);
    }
    return orElse();
  }
}

abstract class UpdateOvulationDate implements UpdateHealthDataEvent {
  const factory UpdateOvulationDate(final DateTime ovulationDate) =
      _$UpdateOvulationDateImpl;

  DateTime get ovulationDate;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateOvulationDateImplCopyWith<_$UpdateOvulationDateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$UpdateHealthDataState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String type) updatedHealthData,
    required TResult Function(HealthDataFailure failure) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String type)? updatedHealthData,
    TResult? Function(HealthDataFailure failure)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String type)? updatedHealthData,
    TResult Function(HealthDataFailure failure)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_UpdatedHealthData value) updatedHealthData,
    required TResult Function(_Failure value) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_UpdatedHealthData value)? updatedHealthData,
    TResult? Function(_Failure value)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_UpdatedHealthData value)? updatedHealthData,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateHealthDataStateCopyWith<$Res> {
  factory $UpdateHealthDataStateCopyWith(UpdateHealthDataState value,
          $Res Function(UpdateHealthDataState) then) =
      _$UpdateHealthDataStateCopyWithImpl<$Res, UpdateHealthDataState>;
}

/// @nodoc
class _$UpdateHealthDataStateCopyWithImpl<$Res,
        $Val extends UpdateHealthDataState>
    implements $UpdateHealthDataStateCopyWith<$Res> {
  _$UpdateHealthDataStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$UpdateHealthDataStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'UpdateHealthDataState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String type) updatedHealthData,
    required TResult Function(HealthDataFailure failure) failure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String type)? updatedHealthData,
    TResult? Function(HealthDataFailure failure)? failure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String type)? updatedHealthData,
    TResult Function(HealthDataFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_UpdatedHealthData value) updatedHealthData,
    required TResult Function(_Failure value) failure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_UpdatedHealthData value)? updatedHealthData,
    TResult? Function(_Failure value)? failure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_UpdatedHealthData value)? updatedHealthData,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements UpdateHealthDataState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$UpdateHealthDataStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'UpdateHealthDataState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String type) updatedHealthData,
    required TResult Function(HealthDataFailure failure) failure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String type)? updatedHealthData,
    TResult? Function(HealthDataFailure failure)? failure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String type)? updatedHealthData,
    TResult Function(HealthDataFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_UpdatedHealthData value) updatedHealthData,
    required TResult Function(_Failure value) failure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_UpdatedHealthData value)? updatedHealthData,
    TResult? Function(_Failure value)? failure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_UpdatedHealthData value)? updatedHealthData,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements UpdateHealthDataState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$UpdatedHealthDataImplCopyWith<$Res> {
  factory _$$UpdatedHealthDataImplCopyWith(_$UpdatedHealthDataImpl value,
          $Res Function(_$UpdatedHealthDataImpl) then) =
      __$$UpdatedHealthDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String type});
}

/// @nodoc
class __$$UpdatedHealthDataImplCopyWithImpl<$Res>
    extends _$UpdateHealthDataStateCopyWithImpl<$Res, _$UpdatedHealthDataImpl>
    implements _$$UpdatedHealthDataImplCopyWith<$Res> {
  __$$UpdatedHealthDataImplCopyWithImpl(_$UpdatedHealthDataImpl _value,
      $Res Function(_$UpdatedHealthDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$UpdatedHealthDataImpl(
      null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdatedHealthDataImpl implements _UpdatedHealthData {
  const _$UpdatedHealthDataImpl(this.type);

  @override
  final String type;

  @override
  String toString() {
    return 'UpdateHealthDataState.updatedHealthData(type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedHealthDataImpl &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedHealthDataImplCopyWith<_$UpdatedHealthDataImpl> get copyWith =>
      __$$UpdatedHealthDataImplCopyWithImpl<_$UpdatedHealthDataImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String type) updatedHealthData,
    required TResult Function(HealthDataFailure failure) failure,
  }) {
    return updatedHealthData(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String type)? updatedHealthData,
    TResult? Function(HealthDataFailure failure)? failure,
  }) {
    return updatedHealthData?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String type)? updatedHealthData,
    TResult Function(HealthDataFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (updatedHealthData != null) {
      return updatedHealthData(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_UpdatedHealthData value) updatedHealthData,
    required TResult Function(_Failure value) failure,
  }) {
    return updatedHealthData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_UpdatedHealthData value)? updatedHealthData,
    TResult? Function(_Failure value)? failure,
  }) {
    return updatedHealthData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_UpdatedHealthData value)? updatedHealthData,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (updatedHealthData != null) {
      return updatedHealthData(this);
    }
    return orElse();
  }
}

abstract class _UpdatedHealthData implements UpdateHealthDataState {
  const factory _UpdatedHealthData(final String type) = _$UpdatedHealthDataImpl;

  String get type;

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedHealthDataImplCopyWith<_$UpdatedHealthDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureImplCopyWith<$Res> {
  factory _$$FailureImplCopyWith(
          _$FailureImpl value, $Res Function(_$FailureImpl) then) =
      __$$FailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HealthDataFailure failure});

  $HealthDataFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureImplCopyWithImpl<$Res>
    extends _$UpdateHealthDataStateCopyWithImpl<$Res, _$FailureImpl>
    implements _$$FailureImplCopyWith<$Res> {
  __$$FailureImplCopyWithImpl(
      _$FailureImpl _value, $Res Function(_$FailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HealthDataFailure,
    ));
  }

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $HealthDataFailureCopyWith<$Res> get failure {
    return $HealthDataFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureImpl implements _Failure {
  const _$FailureImpl(this.failure);

  @override
  final HealthDataFailure failure;

  @override
  String toString() {
    return 'UpdateHealthDataState.failure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      __$$FailureImplCopyWithImpl<_$FailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String type) updatedHealthData,
    required TResult Function(HealthDataFailure failure) failure,
  }) {
    return failure(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String type)? updatedHealthData,
    TResult? Function(HealthDataFailure failure)? failure,
  }) {
    return failure?.call(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String type)? updatedHealthData,
    TResult Function(HealthDataFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this.failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_UpdatedHealthData value) updatedHealthData,
    required TResult Function(_Failure value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_UpdatedHealthData value)? updatedHealthData,
    TResult? Function(_Failure value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_UpdatedHealthData value)? updatedHealthData,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class _Failure implements UpdateHealthDataState {
  const factory _Failure(final HealthDataFailure failure) = _$FailureImpl;

  HealthDataFailure get failure;

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
