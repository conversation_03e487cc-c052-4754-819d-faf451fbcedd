import 'package:account_management/domain/failure/health_data_failure.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/facade/health_data_facade.dart';
import 'package:injectable/injectable.dart';
part 'update_health_data_event.dart';
part 'update_health_data_state.dart';
part 'update_health_data_bloc.freezed.dart';

@injectable
class UpdateHealthDataBloc extends Bloc<UpdateHealthDataEvent, UpdateHealthDataState> {
   final HealthDataFacade _healthDataFacade;
  UpdateHealthDataBloc(this._healthDataFacade) : super(const UpdateHealthDataState.initial()) {
    on<UpdateCycleLength>(_onUpdateCycleLength);
    on<UpdatePeriodLength>(_onUpdatePeriodLength);
    on<UpdateOvulationDate>(_onUpdateOvulationDate);
  }

    Future<void> _onUpdateCycleLength(UpdateCycleLength event,
        Emitter<UpdateHealthDataState> emit) async {
      emit(const UpdateHealthDataState.loading());
      final result = await _healthDataFacade.updateCycleLength(
          event.cycleLength);
      result.mapBoth(onLeft: (failure) {
        emit(UpdateHealthDataState.failure(failure));
      }, onRight: (_) {
        emit(const UpdateHealthDataState.updatedHealthData('cycleLength'));
      });
    }
    Future<void> _onUpdatePeriodLength(UpdatePeriodLength event,
        Emitter<UpdateHealthDataState> emit) async {
      emit(const UpdateHealthDataState.loading());
      final result = await _healthDataFacade.updatePeriodLength(
          event.periodLength);
      result.mapBoth(onLeft: (failure) {
        emit(UpdateHealthDataState.failure(failure));
      }, onRight: (_) {
        emit(const UpdateHealthDataState.updatedHealthData('periodLength'));
      });
    }
    Future<void> _onUpdateOvulationDate(UpdateOvulationDate event,
        Emitter<UpdateHealthDataState> emit) async {
      emit(const UpdateHealthDataState.loading());
      final result = await _healthDataFacade.updateOvulationDate(
          event.ovulationDate);
      result.mapBoth(onLeft: (failure) {
        emit(UpdateHealthDataState.failure(failure));
      }, onRight: (_) {
        emit(const UpdateHealthDataState.updatedHealthData('ovulationDate'));
      });
    }
  }

