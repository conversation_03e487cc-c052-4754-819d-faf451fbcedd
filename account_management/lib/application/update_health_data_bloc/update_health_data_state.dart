part of 'update_health_data_bloc.dart';

@freezed
class UpdateHealthDataState with _$UpdateHealthDataState {
  const factory UpdateHealthDataState.initial() = _Initial;
  const factory UpdateHealthDataState.loading() = _Loading;
  const factory UpdateHealthDataState.updatedHealthData(String type) = _UpdatedHealthData;
  const factory UpdateHealthDataState.failure(HealthDataFailure failure) = _Failure;
}
