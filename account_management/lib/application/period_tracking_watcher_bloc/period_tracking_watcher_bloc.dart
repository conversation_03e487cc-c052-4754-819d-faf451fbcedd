import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
 import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
            import '../../domain/facade/period_tracking_facade.dart';
            import '../../domain/failure/period_tracking_failure.dart';
            import '../../domain/model/period_tracking_model.dart';
import '../../repository/period_tracking_data_model.dart';

            part 'period_tracking_watcher_event.dart';
            part 'period_tracking_watcher_state.dart';
            part 'period_tracking_watcher_bloc.freezed.dart';

            @injectable
            class PeriodTrackingWatcherBloc
                extends Bloc<PeriodTrackingWatcherEvent, PeriodTrackingWatcherState> {
              final PeriodTrackingFacade _periodTrackingFacade;
              StreamSubscription<Either<PeriodTrackingFailure, PeriodTrackingData>>? _periodTrackingStreamSubscription;

              PeriodTrackingWatcherBloc(this._periodTrackingFacade)
                  : super(const PeriodTrackingWatcherState.loading()) {
                on<_WatchAllStarted>(_onWatchAllStarted);
                on<_DataReceived>(_onDataReceived);
              }

              Future<void> _onWatchAllStarted(
                _WatchAllStarted event,
                Emitter<PeriodTrackingWatcherState> emit,
              ) async {
                try {
                  emit(const PeriodTrackingWatcherState.loading());

                  await _periodTrackingStreamSubscription?.cancel();
                  await _periodTrackingFacade.getPeriodTrackingDetails();

                  _periodTrackingStreamSubscription = _periodTrackingFacade
                      .watchAllPeriodTrackingData()
                      .listen(
                        (failureOrPeriodTrackingData) => add(
                          PeriodTrackingWatcherEvent.dataReceived(failureOrPeriodTrackingData),
                        ),
                      );
                } catch (e) {
                  emit(PeriodTrackingWatcherState.initial());
                }
              }

              Future<void> _onDataReceived(
                _DataReceived event,
                Emitter<PeriodTrackingWatcherState> emit,
              ) async {
                await event.failureOrPeriodTrackingData.mapBoth(
                  onLeft: (failure) async => emit(PeriodTrackingWatcherState.initial()),
                  onRight: (periodTrackingData) async {
                    final newState = PeriodTrackingWatcherState.data(
                      periodTrackingDetails: periodTrackingData.periodTrackingDetails,
                      focusedDay: periodTrackingData.focusedDay,
                      selectedDays: periodTrackingData.selectedDays,
                      selectedPeriodTrackingDay: periodTrackingData.selectedPeriodTrackingDay,
                      ovulationDays: periodTrackingData.ovulationDays,
                      updateCounter: state.maybeMap(
                        data: (s) => s.updateCounter + 1,
                        orElse: () => 0,
                      ),
                    );
                    emit(newState);
                  },
                );
              }

              @override
              Future<void> close() async {
                await _periodTrackingStreamSubscription?.cancel();
                return super.close();
              }
            }