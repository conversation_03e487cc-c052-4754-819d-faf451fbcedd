part of 'period_tracking_watcher_bloc.dart';

  @freezed
  class PeriodTrackingWatcherState with _$PeriodTrackingWatcherState {
    const PeriodTrackingWatcherState._();

    const factory PeriodTrackingWatcherState.loading() = _Loading;

    const factory PeriodTrackingWatcherState.data({
      required List<PeriodTrackingModel> periodTrackingDetails,
      required DateTime focusedDay,
      required Set<DateTime> selectedDays,
      required Set<DateTime> ovulationDays,
      required PeriodTrackingModel selectedPeriodTrackingDay,
      required int updateCounter,
    }) = _Data;

    factory PeriodTrackingWatcherState.initial() => PeriodTrackingWatcherState.data(
      periodTrackingDetails: const [],
      focusedDay: DateTime.now(),
      selectedDays: const {},
      ovulationDays: const {},
      selectedPeriodTrackingDay:  PeriodTrackingModel.empty(),
      updateCounter: 0,
    );

    @override
    bool operator ==(Object other) {
      if (identical(this, other)) return true;
      return other is PeriodTrackingWatcherState &&
          other.maybeMap(
            data: (state) =>
                listEquals(state.periodTrackingDetails, maybeMap(
                  data: (s) => s.periodTrackingDetails,
                  orElse: () => [],
                )) &&
                state.focusedDay.isAtSameMomentAsIgnoringMilliseconds(maybeMap(
                  data: (s) => s.focusedDay,
                  orElse: () => DateTime.now(),
                )) &&
                setEquals(state.selectedDays, maybeMap(
                  data: (s) => s.selectedDays,
                  orElse: () => {},
                )) &&
                state.selectedPeriodTrackingDay == maybeMap(
                  data: (s) => s.selectedPeriodTrackingDay,
                  orElse: () => PeriodTrackingModel.empty(),
                ) &&
                setEquals(state.ovulationDays, maybeMap(
                  data: (s) => s.ovulationDays,
                  orElse: () => {},
                )) &&
                state.updateCounter == maybeMap(
                  data: (s) => s.updateCounter,
                  orElse: () => 0,
                ),
            orElse: () => identical(this, other),
          );
    }

    @override
    int get hashCode => maybeMap(
      data: (state) => Object.hash(
        Object.hashAll(state.periodTrackingDetails),
        state.focusedDay.hashCodeIgnoringMilliseconds,
        Object.hashAll(state.selectedDays),
        state.selectedPeriodTrackingDay,
        Object.hashAll(state.ovulationDays),
        state.updateCounter,
      ),
      orElse: () => 0,
    );
  }

  extension DateTimeComparison on DateTime {
    bool isAtSameMomentAsIgnoringMilliseconds(DateTime other) {
      return year == other.year &&
          month == other.month &&
          day == other.day &&
          hour == other.hour &&
          minute == other.minute &&
          second == other.second;
    }

    int get hashCodeIgnoringMilliseconds {
      return year.hashCode ^
          month.hashCode ^
          day.hashCode ^
          hour.hashCode ^
          minute.hashCode ^
          second.hashCode;
    }
  }