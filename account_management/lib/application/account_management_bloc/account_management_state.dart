part of 'account_management_bloc.dart';


@freezed
class AccountManagementState with _$AccountManagementState {
  // Represents the initial state of the Account Management bloc
  const factory AccountManagementState.initial() = Initial;

  // Represents the state where the account details are being updated
  const factory AccountManagementState.updating() = Updating;

  // Represents the state where the account details have been updated successfully
  const factory AccountManagementState.updated() = Updated;

  // Represents the state where the account details could not be updated
  const factory AccountManagementState.updateFailure(AccountManagementFailure failure) = UpdateFailure;
}
