part of 'account_management_bloc.dart';


@freezed
class AccountManagementEvent with _$AccountManagementEvent {

  // Event representing a request to update the account details
  const factory AccountManagementEvent.updateAccountDetails(
      AccountDetailsModel accountDetails) = UpdateAccountDetails;

  // Event representing a request to update the account password
  const factory AccountManagementEvent.updateAccountPassword(
      String password) = UpdateAccountPassword;

  // Event representing a request to update the account email
  const factory AccountManagementEvent.updateAccountEmail(
      String email) = UpdateAccountEmail;
// Event representing a request to update the account phone number
  const factory AccountManagementEvent.updateAccountPhoneNumber(
      String phoneNumber) = UpdateAccountPhoneNumber;
  // Event representing a request to update the profile picture
  const factory AccountManagementEvent.updateProfilePicture(
      XFile? profilePicture) = UpdateProfilePicture;
  //delete profile picture
  const factory AccountManagementEvent.deleteProfilePicture() = DeleteProfilePicture;
  //delete account
  const factory AccountManagementEvent.deleteAccount() = DeleteAccount;
}