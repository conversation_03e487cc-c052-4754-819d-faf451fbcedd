// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_management_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AccountManagementEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountManagementEventCopyWith<$Res> {
  factory $AccountManagementEventCopyWith(AccountManagementEvent value,
          $Res Function(AccountManagementEvent) then) =
      _$AccountManagementEventCopyWithImpl<$Res, AccountManagementEvent>;
}

/// @nodoc
class _$AccountManagementEventCopyWithImpl<$Res,
        $Val extends AccountManagementEvent>
    implements $AccountManagementEventCopyWith<$Res> {
  _$AccountManagementEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$UpdateAccountDetailsImplCopyWith<$Res> {
  factory _$$UpdateAccountDetailsImplCopyWith(_$UpdateAccountDetailsImpl value,
          $Res Function(_$UpdateAccountDetailsImpl) then) =
      __$$UpdateAccountDetailsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AccountDetailsModel accountDetails});
}

/// @nodoc
class __$$UpdateAccountDetailsImplCopyWithImpl<$Res>
    extends _$AccountManagementEventCopyWithImpl<$Res,
        _$UpdateAccountDetailsImpl>
    implements _$$UpdateAccountDetailsImplCopyWith<$Res> {
  __$$UpdateAccountDetailsImplCopyWithImpl(_$UpdateAccountDetailsImpl _value,
      $Res Function(_$UpdateAccountDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountDetails = null,
  }) {
    return _then(_$UpdateAccountDetailsImpl(
      null == accountDetails
          ? _value.accountDetails
          : accountDetails // ignore: cast_nullable_to_non_nullable
              as AccountDetailsModel,
    ));
  }
}

/// @nodoc

class _$UpdateAccountDetailsImpl implements UpdateAccountDetails {
  const _$UpdateAccountDetailsImpl(this.accountDetails);

  @override
  final AccountDetailsModel accountDetails;

  @override
  String toString() {
    return 'AccountManagementEvent.updateAccountDetails(accountDetails: $accountDetails)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateAccountDetailsImpl &&
            (identical(other.accountDetails, accountDetails) ||
                other.accountDetails == accountDetails));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountDetails);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateAccountDetailsImplCopyWith<_$UpdateAccountDetailsImpl>
      get copyWith =>
          __$$UpdateAccountDetailsImplCopyWithImpl<_$UpdateAccountDetailsImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) {
    return updateAccountDetails(accountDetails);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) {
    return updateAccountDetails?.call(accountDetails);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateAccountDetails != null) {
      return updateAccountDetails(accountDetails);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) {
    return updateAccountDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) {
    return updateAccountDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateAccountDetails != null) {
      return updateAccountDetails(this);
    }
    return orElse();
  }
}

abstract class UpdateAccountDetails implements AccountManagementEvent {
  const factory UpdateAccountDetails(final AccountDetailsModel accountDetails) =
      _$UpdateAccountDetailsImpl;

  AccountDetailsModel get accountDetails;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateAccountDetailsImplCopyWith<_$UpdateAccountDetailsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateAccountPasswordImplCopyWith<$Res> {
  factory _$$UpdateAccountPasswordImplCopyWith(
          _$UpdateAccountPasswordImpl value,
          $Res Function(_$UpdateAccountPasswordImpl) then) =
      __$$UpdateAccountPasswordImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String password});
}

/// @nodoc
class __$$UpdateAccountPasswordImplCopyWithImpl<$Res>
    extends _$AccountManagementEventCopyWithImpl<$Res,
        _$UpdateAccountPasswordImpl>
    implements _$$UpdateAccountPasswordImplCopyWith<$Res> {
  __$$UpdateAccountPasswordImplCopyWithImpl(_$UpdateAccountPasswordImpl _value,
      $Res Function(_$UpdateAccountPasswordImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? password = null,
  }) {
    return _then(_$UpdateAccountPasswordImpl(
      null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateAccountPasswordImpl implements UpdateAccountPassword {
  const _$UpdateAccountPasswordImpl(this.password);

  @override
  final String password;

  @override
  String toString() {
    return 'AccountManagementEvent.updateAccountPassword(password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateAccountPasswordImpl &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, password);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateAccountPasswordImplCopyWith<_$UpdateAccountPasswordImpl>
      get copyWith => __$$UpdateAccountPasswordImplCopyWithImpl<
          _$UpdateAccountPasswordImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) {
    return updateAccountPassword(password);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) {
    return updateAccountPassword?.call(password);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateAccountPassword != null) {
      return updateAccountPassword(password);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) {
    return updateAccountPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) {
    return updateAccountPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateAccountPassword != null) {
      return updateAccountPassword(this);
    }
    return orElse();
  }
}

abstract class UpdateAccountPassword implements AccountManagementEvent {
  const factory UpdateAccountPassword(final String password) =
      _$UpdateAccountPasswordImpl;

  String get password;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateAccountPasswordImplCopyWith<_$UpdateAccountPasswordImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateAccountEmailImplCopyWith<$Res> {
  factory _$$UpdateAccountEmailImplCopyWith(_$UpdateAccountEmailImpl value,
          $Res Function(_$UpdateAccountEmailImpl) then) =
      __$$UpdateAccountEmailImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String email});
}

/// @nodoc
class __$$UpdateAccountEmailImplCopyWithImpl<$Res>
    extends _$AccountManagementEventCopyWithImpl<$Res, _$UpdateAccountEmailImpl>
    implements _$$UpdateAccountEmailImplCopyWith<$Res> {
  __$$UpdateAccountEmailImplCopyWithImpl(_$UpdateAccountEmailImpl _value,
      $Res Function(_$UpdateAccountEmailImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
  }) {
    return _then(_$UpdateAccountEmailImpl(
      null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateAccountEmailImpl implements UpdateAccountEmail {
  const _$UpdateAccountEmailImpl(this.email);

  @override
  final String email;

  @override
  String toString() {
    return 'AccountManagementEvent.updateAccountEmail(email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateAccountEmailImpl &&
            (identical(other.email, email) || other.email == email));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateAccountEmailImplCopyWith<_$UpdateAccountEmailImpl> get copyWith =>
      __$$UpdateAccountEmailImplCopyWithImpl<_$UpdateAccountEmailImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) {
    return updateAccountEmail(email);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) {
    return updateAccountEmail?.call(email);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateAccountEmail != null) {
      return updateAccountEmail(email);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) {
    return updateAccountEmail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) {
    return updateAccountEmail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateAccountEmail != null) {
      return updateAccountEmail(this);
    }
    return orElse();
  }
}

abstract class UpdateAccountEmail implements AccountManagementEvent {
  const factory UpdateAccountEmail(final String email) =
      _$UpdateAccountEmailImpl;

  String get email;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateAccountEmailImplCopyWith<_$UpdateAccountEmailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateAccountPhoneNumberImplCopyWith<$Res> {
  factory _$$UpdateAccountPhoneNumberImplCopyWith(
          _$UpdateAccountPhoneNumberImpl value,
          $Res Function(_$UpdateAccountPhoneNumberImpl) then) =
      __$$UpdateAccountPhoneNumberImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String phoneNumber});
}

/// @nodoc
class __$$UpdateAccountPhoneNumberImplCopyWithImpl<$Res>
    extends _$AccountManagementEventCopyWithImpl<$Res,
        _$UpdateAccountPhoneNumberImpl>
    implements _$$UpdateAccountPhoneNumberImplCopyWith<$Res> {
  __$$UpdateAccountPhoneNumberImplCopyWithImpl(
      _$UpdateAccountPhoneNumberImpl _value,
      $Res Function(_$UpdateAccountPhoneNumberImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_$UpdateAccountPhoneNumberImpl(
      null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateAccountPhoneNumberImpl implements UpdateAccountPhoneNumber {
  const _$UpdateAccountPhoneNumberImpl(this.phoneNumber);

  @override
  final String phoneNumber;

  @override
  String toString() {
    return 'AccountManagementEvent.updateAccountPhoneNumber(phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateAccountPhoneNumberImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateAccountPhoneNumberImplCopyWith<_$UpdateAccountPhoneNumberImpl>
      get copyWith => __$$UpdateAccountPhoneNumberImplCopyWithImpl<
          _$UpdateAccountPhoneNumberImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) {
    return updateAccountPhoneNumber(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) {
    return updateAccountPhoneNumber?.call(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateAccountPhoneNumber != null) {
      return updateAccountPhoneNumber(phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) {
    return updateAccountPhoneNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) {
    return updateAccountPhoneNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateAccountPhoneNumber != null) {
      return updateAccountPhoneNumber(this);
    }
    return orElse();
  }
}

abstract class UpdateAccountPhoneNumber implements AccountManagementEvent {
  const factory UpdateAccountPhoneNumber(final String phoneNumber) =
      _$UpdateAccountPhoneNumberImpl;

  String get phoneNumber;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateAccountPhoneNumberImplCopyWith<_$UpdateAccountPhoneNumberImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateProfilePictureImplCopyWith<$Res> {
  factory _$$UpdateProfilePictureImplCopyWith(_$UpdateProfilePictureImpl value,
          $Res Function(_$UpdateProfilePictureImpl) then) =
      __$$UpdateProfilePictureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({XFile? profilePicture});
}

/// @nodoc
class __$$UpdateProfilePictureImplCopyWithImpl<$Res>
    extends _$AccountManagementEventCopyWithImpl<$Res,
        _$UpdateProfilePictureImpl>
    implements _$$UpdateProfilePictureImplCopyWith<$Res> {
  __$$UpdateProfilePictureImplCopyWithImpl(_$UpdateProfilePictureImpl _value,
      $Res Function(_$UpdateProfilePictureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profilePicture = freezed,
  }) {
    return _then(_$UpdateProfilePictureImpl(
      freezed == profilePicture
          ? _value.profilePicture
          : profilePicture // ignore: cast_nullable_to_non_nullable
              as XFile?,
    ));
  }
}

/// @nodoc

class _$UpdateProfilePictureImpl implements UpdateProfilePicture {
  const _$UpdateProfilePictureImpl(this.profilePicture);

  @override
  final XFile? profilePicture;

  @override
  String toString() {
    return 'AccountManagementEvent.updateProfilePicture(profilePicture: $profilePicture)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateProfilePictureImpl &&
            (identical(other.profilePicture, profilePicture) ||
                other.profilePicture == profilePicture));
  }

  @override
  int get hashCode => Object.hash(runtimeType, profilePicture);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateProfilePictureImplCopyWith<_$UpdateProfilePictureImpl>
      get copyWith =>
          __$$UpdateProfilePictureImplCopyWithImpl<_$UpdateProfilePictureImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) {
    return updateProfilePicture(profilePicture);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) {
    return updateProfilePicture?.call(profilePicture);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateProfilePicture != null) {
      return updateProfilePicture(profilePicture);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) {
    return updateProfilePicture(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) {
    return updateProfilePicture?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) {
    if (updateProfilePicture != null) {
      return updateProfilePicture(this);
    }
    return orElse();
  }
}

abstract class UpdateProfilePicture implements AccountManagementEvent {
  const factory UpdateProfilePicture(final XFile? profilePicture) =
      _$UpdateProfilePictureImpl;

  XFile? get profilePicture;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateProfilePictureImplCopyWith<_$UpdateProfilePictureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteProfilePictureImplCopyWith<$Res> {
  factory _$$DeleteProfilePictureImplCopyWith(_$DeleteProfilePictureImpl value,
          $Res Function(_$DeleteProfilePictureImpl) then) =
      __$$DeleteProfilePictureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteProfilePictureImplCopyWithImpl<$Res>
    extends _$AccountManagementEventCopyWithImpl<$Res,
        _$DeleteProfilePictureImpl>
    implements _$$DeleteProfilePictureImplCopyWith<$Res> {
  __$$DeleteProfilePictureImplCopyWithImpl(_$DeleteProfilePictureImpl _value,
      $Res Function(_$DeleteProfilePictureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteProfilePictureImpl implements DeleteProfilePicture {
  const _$DeleteProfilePictureImpl();

  @override
  String toString() {
    return 'AccountManagementEvent.deleteProfilePicture()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteProfilePictureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) {
    return deleteProfilePicture();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) {
    return deleteProfilePicture?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) {
    if (deleteProfilePicture != null) {
      return deleteProfilePicture();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) {
    return deleteProfilePicture(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) {
    return deleteProfilePicture?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) {
    if (deleteProfilePicture != null) {
      return deleteProfilePicture(this);
    }
    return orElse();
  }
}

abstract class DeleteProfilePicture implements AccountManagementEvent {
  const factory DeleteProfilePicture() = _$DeleteProfilePictureImpl;
}

/// @nodoc
abstract class _$$DeleteAccountImplCopyWith<$Res> {
  factory _$$DeleteAccountImplCopyWith(
          _$DeleteAccountImpl value, $Res Function(_$DeleteAccountImpl) then) =
      __$$DeleteAccountImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteAccountImplCopyWithImpl<$Res>
    extends _$AccountManagementEventCopyWithImpl<$Res, _$DeleteAccountImpl>
    implements _$$DeleteAccountImplCopyWith<$Res> {
  __$$DeleteAccountImplCopyWithImpl(
      _$DeleteAccountImpl _value, $Res Function(_$DeleteAccountImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteAccountImpl implements DeleteAccount {
  const _$DeleteAccountImpl();

  @override
  String toString() {
    return 'AccountManagementEvent.deleteAccount()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DeleteAccountImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) {
    return deleteAccount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) {
    return deleteAccount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) {
    if (deleteAccount != null) {
      return deleteAccount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) {
    return deleteAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) {
    return deleteAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) {
    if (deleteAccount != null) {
      return deleteAccount(this);
    }
    return orElse();
  }
}

abstract class DeleteAccount implements AccountManagementEvent {
  const factory DeleteAccount() = _$DeleteAccountImpl;
}

/// @nodoc
mixin _$AccountManagementState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() updating,
    required TResult Function() updated,
    required TResult Function(AccountManagementFailure failure) updateFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? updating,
    TResult? Function()? updated,
    TResult? Function(AccountManagementFailure failure)? updateFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? updating,
    TResult Function()? updated,
    TResult Function(AccountManagementFailure failure)? updateFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Updating value) updating,
    required TResult Function(Updated value) updated,
    required TResult Function(UpdateFailure value) updateFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Updating value)? updating,
    TResult? Function(Updated value)? updated,
    TResult? Function(UpdateFailure value)? updateFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Updating value)? updating,
    TResult Function(Updated value)? updated,
    TResult Function(UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountManagementStateCopyWith<$Res> {
  factory $AccountManagementStateCopyWith(AccountManagementState value,
          $Res Function(AccountManagementState) then) =
      _$AccountManagementStateCopyWithImpl<$Res, AccountManagementState>;
}

/// @nodoc
class _$AccountManagementStateCopyWithImpl<$Res,
        $Val extends AccountManagementState>
    implements $AccountManagementStateCopyWith<$Res> {
  _$AccountManagementStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$AccountManagementStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'AccountManagementState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() updating,
    required TResult Function() updated,
    required TResult Function(AccountManagementFailure failure) updateFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? updating,
    TResult? Function()? updated,
    TResult? Function(AccountManagementFailure failure)? updateFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? updating,
    TResult Function()? updated,
    TResult Function(AccountManagementFailure failure)? updateFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Updating value) updating,
    required TResult Function(Updated value) updated,
    required TResult Function(UpdateFailure value) updateFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Updating value)? updating,
    TResult? Function(Updated value)? updated,
    TResult? Function(UpdateFailure value)? updateFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Updating value)? updating,
    TResult Function(Updated value)? updated,
    TResult Function(UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements AccountManagementState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$UpdatingImplCopyWith<$Res> {
  factory _$$UpdatingImplCopyWith(
          _$UpdatingImpl value, $Res Function(_$UpdatingImpl) then) =
      __$$UpdatingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdatingImplCopyWithImpl<$Res>
    extends _$AccountManagementStateCopyWithImpl<$Res, _$UpdatingImpl>
    implements _$$UpdatingImplCopyWith<$Res> {
  __$$UpdatingImplCopyWithImpl(
      _$UpdatingImpl _value, $Res Function(_$UpdatingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdatingImpl implements Updating {
  const _$UpdatingImpl();

  @override
  String toString() {
    return 'AccountManagementState.updating()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdatingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() updating,
    required TResult Function() updated,
    required TResult Function(AccountManagementFailure failure) updateFailure,
  }) {
    return updating();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? updating,
    TResult? Function()? updated,
    TResult? Function(AccountManagementFailure failure)? updateFailure,
  }) {
    return updating?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? updating,
    TResult Function()? updated,
    TResult Function(AccountManagementFailure failure)? updateFailure,
    required TResult orElse(),
  }) {
    if (updating != null) {
      return updating();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Updating value) updating,
    required TResult Function(Updated value) updated,
    required TResult Function(UpdateFailure value) updateFailure,
  }) {
    return updating(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Updating value)? updating,
    TResult? Function(Updated value)? updated,
    TResult? Function(UpdateFailure value)? updateFailure,
  }) {
    return updating?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Updating value)? updating,
    TResult Function(Updated value)? updated,
    TResult Function(UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    if (updating != null) {
      return updating(this);
    }
    return orElse();
  }
}

abstract class Updating implements AccountManagementState {
  const factory Updating() = _$UpdatingImpl;
}

/// @nodoc
abstract class _$$UpdatedImplCopyWith<$Res> {
  factory _$$UpdatedImplCopyWith(
          _$UpdatedImpl value, $Res Function(_$UpdatedImpl) then) =
      __$$UpdatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdatedImplCopyWithImpl<$Res>
    extends _$AccountManagementStateCopyWithImpl<$Res, _$UpdatedImpl>
    implements _$$UpdatedImplCopyWith<$Res> {
  __$$UpdatedImplCopyWithImpl(
      _$UpdatedImpl _value, $Res Function(_$UpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdatedImpl implements Updated {
  const _$UpdatedImpl();

  @override
  String toString() {
    return 'AccountManagementState.updated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() updating,
    required TResult Function() updated,
    required TResult Function(AccountManagementFailure failure) updateFailure,
  }) {
    return updated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? updating,
    TResult? Function()? updated,
    TResult? Function(AccountManagementFailure failure)? updateFailure,
  }) {
    return updated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? updating,
    TResult Function()? updated,
    TResult Function(AccountManagementFailure failure)? updateFailure,
    required TResult orElse(),
  }) {
    if (updated != null) {
      return updated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Updating value) updating,
    required TResult Function(Updated value) updated,
    required TResult Function(UpdateFailure value) updateFailure,
  }) {
    return updated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Updating value)? updating,
    TResult? Function(Updated value)? updated,
    TResult? Function(UpdateFailure value)? updateFailure,
  }) {
    return updated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Updating value)? updating,
    TResult Function(Updated value)? updated,
    TResult Function(UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    if (updated != null) {
      return updated(this);
    }
    return orElse();
  }
}

abstract class Updated implements AccountManagementState {
  const factory Updated() = _$UpdatedImpl;
}

/// @nodoc
abstract class _$$UpdateFailureImplCopyWith<$Res> {
  factory _$$UpdateFailureImplCopyWith(
          _$UpdateFailureImpl value, $Res Function(_$UpdateFailureImpl) then) =
      __$$UpdateFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AccountManagementFailure failure});

  $AccountManagementFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$UpdateFailureImplCopyWithImpl<$Res>
    extends _$AccountManagementStateCopyWithImpl<$Res, _$UpdateFailureImpl>
    implements _$$UpdateFailureImplCopyWith<$Res> {
  __$$UpdateFailureImplCopyWithImpl(
      _$UpdateFailureImpl _value, $Res Function(_$UpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$UpdateFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as AccountManagementFailure,
    ));
  }

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountManagementFailureCopyWith<$Res> get failure {
    return $AccountManagementFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$UpdateFailureImpl implements UpdateFailure {
  const _$UpdateFailureImpl(this.failure);

  @override
  final AccountManagementFailure failure;

  @override
  String toString() {
    return 'AccountManagementState.updateFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateFailureImplCopyWith<_$UpdateFailureImpl> get copyWith =>
      __$$UpdateFailureImplCopyWithImpl<_$UpdateFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() updating,
    required TResult Function() updated,
    required TResult Function(AccountManagementFailure failure) updateFailure,
  }) {
    return updateFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? updating,
    TResult? Function()? updated,
    TResult? Function(AccountManagementFailure failure)? updateFailure,
  }) {
    return updateFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? updating,
    TResult Function()? updated,
    TResult Function(AccountManagementFailure failure)? updateFailure,
    required TResult orElse(),
  }) {
    if (updateFailure != null) {
      return updateFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Updating value) updating,
    required TResult Function(Updated value) updated,
    required TResult Function(UpdateFailure value) updateFailure,
  }) {
    return updateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Updating value)? updating,
    TResult? Function(Updated value)? updated,
    TResult? Function(UpdateFailure value)? updateFailure,
  }) {
    return updateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Updating value)? updating,
    TResult Function(Updated value)? updated,
    TResult Function(UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    if (updateFailure != null) {
      return updateFailure(this);
    }
    return orElse();
  }
}

abstract class UpdateFailure implements AccountManagementState {
  const factory UpdateFailure(final AccountManagementFailure failure) =
      _$UpdateFailureImpl;

  AccountManagementFailure get failure;

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateFailureImplCopyWith<_$UpdateFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
