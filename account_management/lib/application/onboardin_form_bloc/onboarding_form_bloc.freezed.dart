// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_form_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OnboardingFormEvent {
  HealthDataModel get healthData => throw _privateConstructorUsedError;
  DateTime get dateOfBirth => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HealthDataModel healthData, DateTime dateOfBirth)
        updateOnboardingForm,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HealthDataModel healthData, DateTime dateOfBirth)?
        updateOnboardingForm,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HealthDataModel healthData, DateTime dateOfBirth)?
        updateOnboardingForm,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateOnboardingForm value) updateOnboardingForm,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateOnboardingForm value)? updateOnboardingForm,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateOnboardingForm value)? updateOnboardingForm,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OnboardingFormEventCopyWith<OnboardingFormEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingFormEventCopyWith<$Res> {
  factory $OnboardingFormEventCopyWith(
          OnboardingFormEvent value, $Res Function(OnboardingFormEvent) then) =
      _$OnboardingFormEventCopyWithImpl<$Res, OnboardingFormEvent>;
  @useResult
  $Res call({HealthDataModel healthData, DateTime dateOfBirth});
}

/// @nodoc
class _$OnboardingFormEventCopyWithImpl<$Res, $Val extends OnboardingFormEvent>
    implements $OnboardingFormEventCopyWith<$Res> {
  _$OnboardingFormEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? healthData = null,
    Object? dateOfBirth = null,
  }) {
    return _then(_value.copyWith(
      healthData: null == healthData
          ? _value.healthData
          : healthData // ignore: cast_nullable_to_non_nullable
              as HealthDataModel,
      dateOfBirth: null == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateOnboardingFormImplCopyWith<$Res>
    implements $OnboardingFormEventCopyWith<$Res> {
  factory _$$UpdateOnboardingFormImplCopyWith(_$UpdateOnboardingFormImpl value,
          $Res Function(_$UpdateOnboardingFormImpl) then) =
      __$$UpdateOnboardingFormImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({HealthDataModel healthData, DateTime dateOfBirth});
}

/// @nodoc
class __$$UpdateOnboardingFormImplCopyWithImpl<$Res>
    extends _$OnboardingFormEventCopyWithImpl<$Res, _$UpdateOnboardingFormImpl>
    implements _$$UpdateOnboardingFormImplCopyWith<$Res> {
  __$$UpdateOnboardingFormImplCopyWithImpl(_$UpdateOnboardingFormImpl _value,
      $Res Function(_$UpdateOnboardingFormImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? healthData = null,
    Object? dateOfBirth = null,
  }) {
    return _then(_$UpdateOnboardingFormImpl(
      null == healthData
          ? _value.healthData
          : healthData // ignore: cast_nullable_to_non_nullable
              as HealthDataModel,
      null == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$UpdateOnboardingFormImpl implements UpdateOnboardingForm {
  const _$UpdateOnboardingFormImpl(this.healthData, this.dateOfBirth);

  @override
  final HealthDataModel healthData;
  @override
  final DateTime dateOfBirth;

  @override
  String toString() {
    return 'OnboardingFormEvent.updateOnboardingForm(healthData: $healthData, dateOfBirth: $dateOfBirth)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateOnboardingFormImpl &&
            (identical(other.healthData, healthData) ||
                other.healthData == healthData) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth));
  }

  @override
  int get hashCode => Object.hash(runtimeType, healthData, dateOfBirth);

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateOnboardingFormImplCopyWith<_$UpdateOnboardingFormImpl>
      get copyWith =>
          __$$UpdateOnboardingFormImplCopyWithImpl<_$UpdateOnboardingFormImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HealthDataModel healthData, DateTime dateOfBirth)
        updateOnboardingForm,
  }) {
    return updateOnboardingForm(healthData, dateOfBirth);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HealthDataModel healthData, DateTime dateOfBirth)?
        updateOnboardingForm,
  }) {
    return updateOnboardingForm?.call(healthData, dateOfBirth);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HealthDataModel healthData, DateTime dateOfBirth)?
        updateOnboardingForm,
    required TResult orElse(),
  }) {
    if (updateOnboardingForm != null) {
      return updateOnboardingForm(healthData, dateOfBirth);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateOnboardingForm value) updateOnboardingForm,
  }) {
    return updateOnboardingForm(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateOnboardingForm value)? updateOnboardingForm,
  }) {
    return updateOnboardingForm?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateOnboardingForm value)? updateOnboardingForm,
    required TResult orElse(),
  }) {
    if (updateOnboardingForm != null) {
      return updateOnboardingForm(this);
    }
    return orElse();
  }
}

abstract class UpdateOnboardingForm implements OnboardingFormEvent {
  const factory UpdateOnboardingForm(
          final HealthDataModel healthData, final DateTime dateOfBirth) =
      _$UpdateOnboardingFormImpl;

  @override
  HealthDataModel get healthData;
  @override
  DateTime get dateOfBirth;

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateOnboardingFormImplCopyWith<_$UpdateOnboardingFormImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$OnboardingFormState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadInProgress,
    required TResult Function() updateSuccess,
    required TResult Function(AccountManagementFailure onboardingFormFailure)
        updateFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadInProgress,
    TResult? Function()? updateSuccess,
    TResult? Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadInProgress,
    TResult Function()? updateSuccess,
    TResult Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoadInProgress value) loadInProgress,
    required TResult Function(_UpdateSuccess value) updateSuccess,
    required TResult Function(_UpdateFailure value) updateFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoadInProgress value)? loadInProgress,
    TResult? Function(_UpdateSuccess value)? updateSuccess,
    TResult? Function(_UpdateFailure value)? updateFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoadInProgress value)? loadInProgress,
    TResult Function(_UpdateSuccess value)? updateSuccess,
    TResult Function(_UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingFormStateCopyWith<$Res> {
  factory $OnboardingFormStateCopyWith(
          OnboardingFormState value, $Res Function(OnboardingFormState) then) =
      _$OnboardingFormStateCopyWithImpl<$Res, OnboardingFormState>;
}

/// @nodoc
class _$OnboardingFormStateCopyWithImpl<$Res, $Val extends OnboardingFormState>
    implements $OnboardingFormStateCopyWith<$Res> {
  _$OnboardingFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$OnboardingFormStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'OnboardingFormState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadInProgress,
    required TResult Function() updateSuccess,
    required TResult Function(AccountManagementFailure onboardingFormFailure)
        updateFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadInProgress,
    TResult? Function()? updateSuccess,
    TResult? Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadInProgress,
    TResult Function()? updateSuccess,
    TResult Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoadInProgress value) loadInProgress,
    required TResult Function(_UpdateSuccess value) updateSuccess,
    required TResult Function(_UpdateFailure value) updateFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoadInProgress value)? loadInProgress,
    TResult? Function(_UpdateSuccess value)? updateSuccess,
    TResult? Function(_UpdateFailure value)? updateFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoadInProgress value)? loadInProgress,
    TResult Function(_UpdateSuccess value)? updateSuccess,
    TResult Function(_UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements OnboardingFormState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadInProgressImplCopyWith<$Res> {
  factory _$$LoadInProgressImplCopyWith(_$LoadInProgressImpl value,
          $Res Function(_$LoadInProgressImpl) then) =
      __$$LoadInProgressImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadInProgressImplCopyWithImpl<$Res>
    extends _$OnboardingFormStateCopyWithImpl<$Res, _$LoadInProgressImpl>
    implements _$$LoadInProgressImplCopyWith<$Res> {
  __$$LoadInProgressImplCopyWithImpl(
      _$LoadInProgressImpl _value, $Res Function(_$LoadInProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadInProgressImpl implements _LoadInProgress {
  const _$LoadInProgressImpl();

  @override
  String toString() {
    return 'OnboardingFormState.loadInProgress()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadInProgressImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadInProgress,
    required TResult Function() updateSuccess,
    required TResult Function(AccountManagementFailure onboardingFormFailure)
        updateFailure,
  }) {
    return loadInProgress();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadInProgress,
    TResult? Function()? updateSuccess,
    TResult? Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
  }) {
    return loadInProgress?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadInProgress,
    TResult Function()? updateSuccess,
    TResult Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
    required TResult orElse(),
  }) {
    if (loadInProgress != null) {
      return loadInProgress();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoadInProgress value) loadInProgress,
    required TResult Function(_UpdateSuccess value) updateSuccess,
    required TResult Function(_UpdateFailure value) updateFailure,
  }) {
    return loadInProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoadInProgress value)? loadInProgress,
    TResult? Function(_UpdateSuccess value)? updateSuccess,
    TResult? Function(_UpdateFailure value)? updateFailure,
  }) {
    return loadInProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoadInProgress value)? loadInProgress,
    TResult Function(_UpdateSuccess value)? updateSuccess,
    TResult Function(_UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    if (loadInProgress != null) {
      return loadInProgress(this);
    }
    return orElse();
  }
}

abstract class _LoadInProgress implements OnboardingFormState {
  const factory _LoadInProgress() = _$LoadInProgressImpl;
}

/// @nodoc
abstract class _$$UpdateSuccessImplCopyWith<$Res> {
  factory _$$UpdateSuccessImplCopyWith(
          _$UpdateSuccessImpl value, $Res Function(_$UpdateSuccessImpl) then) =
      __$$UpdateSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateSuccessImplCopyWithImpl<$Res>
    extends _$OnboardingFormStateCopyWithImpl<$Res, _$UpdateSuccessImpl>
    implements _$$UpdateSuccessImplCopyWith<$Res> {
  __$$UpdateSuccessImplCopyWithImpl(
      _$UpdateSuccessImpl _value, $Res Function(_$UpdateSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateSuccessImpl implements _UpdateSuccess {
  const _$UpdateSuccessImpl();

  @override
  String toString() {
    return 'OnboardingFormState.updateSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdateSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadInProgress,
    required TResult Function() updateSuccess,
    required TResult Function(AccountManagementFailure onboardingFormFailure)
        updateFailure,
  }) {
    return updateSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadInProgress,
    TResult? Function()? updateSuccess,
    TResult? Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
  }) {
    return updateSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadInProgress,
    TResult Function()? updateSuccess,
    TResult Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
    required TResult orElse(),
  }) {
    if (updateSuccess != null) {
      return updateSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoadInProgress value) loadInProgress,
    required TResult Function(_UpdateSuccess value) updateSuccess,
    required TResult Function(_UpdateFailure value) updateFailure,
  }) {
    return updateSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoadInProgress value)? loadInProgress,
    TResult? Function(_UpdateSuccess value)? updateSuccess,
    TResult? Function(_UpdateFailure value)? updateFailure,
  }) {
    return updateSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoadInProgress value)? loadInProgress,
    TResult Function(_UpdateSuccess value)? updateSuccess,
    TResult Function(_UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    if (updateSuccess != null) {
      return updateSuccess(this);
    }
    return orElse();
  }
}

abstract class _UpdateSuccess implements OnboardingFormState {
  const factory _UpdateSuccess() = _$UpdateSuccessImpl;
}

/// @nodoc
abstract class _$$UpdateFailureImplCopyWith<$Res> {
  factory _$$UpdateFailureImplCopyWith(
          _$UpdateFailureImpl value, $Res Function(_$UpdateFailureImpl) then) =
      __$$UpdateFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AccountManagementFailure onboardingFormFailure});

  $AccountManagementFailureCopyWith<$Res> get onboardingFormFailure;
}

/// @nodoc
class __$$UpdateFailureImplCopyWithImpl<$Res>
    extends _$OnboardingFormStateCopyWithImpl<$Res, _$UpdateFailureImpl>
    implements _$$UpdateFailureImplCopyWith<$Res> {
  __$$UpdateFailureImplCopyWithImpl(
      _$UpdateFailureImpl _value, $Res Function(_$UpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? onboardingFormFailure = null,
  }) {
    return _then(_$UpdateFailureImpl(
      null == onboardingFormFailure
          ? _value.onboardingFormFailure
          : onboardingFormFailure // ignore: cast_nullable_to_non_nullable
              as AccountManagementFailure,
    ));
  }

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountManagementFailureCopyWith<$Res> get onboardingFormFailure {
    return $AccountManagementFailureCopyWith<$Res>(_value.onboardingFormFailure,
        (value) {
      return _then(_value.copyWith(onboardingFormFailure: value));
    });
  }
}

/// @nodoc

class _$UpdateFailureImpl implements _UpdateFailure {
  const _$UpdateFailureImpl(this.onboardingFormFailure);

  @override
  final AccountManagementFailure onboardingFormFailure;

  @override
  String toString() {
    return 'OnboardingFormState.updateFailure(onboardingFormFailure: $onboardingFormFailure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateFailureImpl &&
            (identical(other.onboardingFormFailure, onboardingFormFailure) ||
                other.onboardingFormFailure == onboardingFormFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, onboardingFormFailure);

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateFailureImplCopyWith<_$UpdateFailureImpl> get copyWith =>
      __$$UpdateFailureImplCopyWithImpl<_$UpdateFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadInProgress,
    required TResult Function() updateSuccess,
    required TResult Function(AccountManagementFailure onboardingFormFailure)
        updateFailure,
  }) {
    return updateFailure(onboardingFormFailure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadInProgress,
    TResult? Function()? updateSuccess,
    TResult? Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
  }) {
    return updateFailure?.call(onboardingFormFailure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadInProgress,
    TResult Function()? updateSuccess,
    TResult Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
    required TResult orElse(),
  }) {
    if (updateFailure != null) {
      return updateFailure(onboardingFormFailure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoadInProgress value) loadInProgress,
    required TResult Function(_UpdateSuccess value) updateSuccess,
    required TResult Function(_UpdateFailure value) updateFailure,
  }) {
    return updateFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoadInProgress value)? loadInProgress,
    TResult? Function(_UpdateSuccess value)? updateSuccess,
    TResult? Function(_UpdateFailure value)? updateFailure,
  }) {
    return updateFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoadInProgress value)? loadInProgress,
    TResult Function(_UpdateSuccess value)? updateSuccess,
    TResult Function(_UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    if (updateFailure != null) {
      return updateFailure(this);
    }
    return orElse();
  }
}

abstract class _UpdateFailure implements OnboardingFormState {
  const factory _UpdateFailure(
          final AccountManagementFailure onboardingFormFailure) =
      _$UpdateFailureImpl;

  AccountManagementFailure get onboardingFormFailure;

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateFailureImplCopyWith<_$UpdateFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
