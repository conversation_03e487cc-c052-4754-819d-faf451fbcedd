part of 'onboarding_form_bloc.dart';
// OnboardingFormState is a freezed union class that will be used to represent the different states that can occur in the OnboardingFormBloc.
@freezed
class OnboardingFormState with _$OnboardingFormState {
  const factory OnboardingFormState.initial() = _Initial;
  const factory OnboardingFormState.loadInProgress() = _LoadInProgress;
  const factory OnboardingFormState.updateSuccess() = _UpdateSuccess;
  const factory OnboardingFormState.updateFailure(AccountManagementFailure onboardingFormFailure) = _UpdateFailure;
}
