// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'menstrual_cycle_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MenstrualCycleEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getInitialData,
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)
        dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getInitialData,
    TResult? Function()? watchAllStarted,
    TResult? Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getInitialData,
    TResult Function()? watchAllStarted,
    TResult Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetInitialData value) getInitialData,
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetInitialData value)? getInitialData,
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetInitialData value)? getInitialData,
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MenstrualCycleEventCopyWith<$Res> {
  factory $MenstrualCycleEventCopyWith(
          MenstrualCycleEvent value, $Res Function(MenstrualCycleEvent) then) =
      _$MenstrualCycleEventCopyWithImpl<$Res, MenstrualCycleEvent>;
}

/// @nodoc
class _$MenstrualCycleEventCopyWithImpl<$Res, $Val extends MenstrualCycleEvent>
    implements $MenstrualCycleEventCopyWith<$Res> {
  _$MenstrualCycleEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MenstrualCycleEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$GetInitialDataImplCopyWith<$Res> {
  factory _$$GetInitialDataImplCopyWith(_$GetInitialDataImpl value,
          $Res Function(_$GetInitialDataImpl) then) =
      __$$GetInitialDataImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetInitialDataImplCopyWithImpl<$Res>
    extends _$MenstrualCycleEventCopyWithImpl<$Res, _$GetInitialDataImpl>
    implements _$$GetInitialDataImplCopyWith<$Res> {
  __$$GetInitialDataImplCopyWithImpl(
      _$GetInitialDataImpl _value, $Res Function(_$GetInitialDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenstrualCycleEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetInitialDataImpl implements _GetInitialData {
  const _$GetInitialDataImpl();

  @override
  String toString() {
    return 'MenstrualCycleEvent.getInitialData()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetInitialDataImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getInitialData,
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)
        dataReceived,
  }) {
    return getInitialData();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getInitialData,
    TResult? Function()? watchAllStarted,
    TResult? Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
  }) {
    return getInitialData?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getInitialData,
    TResult Function()? watchAllStarted,
    TResult Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
    required TResult orElse(),
  }) {
    if (getInitialData != null) {
      return getInitialData();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetInitialData value) getInitialData,
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    return getInitialData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetInitialData value)? getInitialData,
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    return getInitialData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetInitialData value)? getInitialData,
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    if (getInitialData != null) {
      return getInitialData(this);
    }
    return orElse();
  }
}

abstract class _GetInitialData implements MenstrualCycleEvent {
  const factory _GetInitialData() = _$GetInitialDataImpl;
}

/// @nodoc
abstract class _$$WatchAllStartedImplCopyWith<$Res> {
  factory _$$WatchAllStartedImplCopyWith(_$WatchAllStartedImpl value,
          $Res Function(_$WatchAllStartedImpl) then) =
      __$$WatchAllStartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WatchAllStartedImplCopyWithImpl<$Res>
    extends _$MenstrualCycleEventCopyWithImpl<$Res, _$WatchAllStartedImpl>
    implements _$$WatchAllStartedImplCopyWith<$Res> {
  __$$WatchAllStartedImplCopyWithImpl(
      _$WatchAllStartedImpl _value, $Res Function(_$WatchAllStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenstrualCycleEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WatchAllStartedImpl implements _WatchAllStarted {
  const _$WatchAllStartedImpl();

  @override
  String toString() {
    return 'MenstrualCycleEvent.watchAllStarted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WatchAllStartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getInitialData,
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)
        dataReceived,
  }) {
    return watchAllStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getInitialData,
    TResult? Function()? watchAllStarted,
    TResult? Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
  }) {
    return watchAllStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getInitialData,
    TResult Function()? watchAllStarted,
    TResult Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetInitialData value) getInitialData,
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    return watchAllStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetInitialData value)? getInitialData,
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    return watchAllStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetInitialData value)? getInitialData,
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchAllStarted implements MenstrualCycleEvent {
  const factory _WatchAllStarted() = _$WatchAllStartedImpl;
}

/// @nodoc
abstract class _$$DataReceivedImplCopyWith<$Res> {
  factory _$$DataReceivedImplCopyWith(
          _$DataReceivedImpl value, $Res Function(_$DataReceivedImpl) then) =
      __$$DataReceivedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Either<PeriodTrackingFailure, MenstrualCycleData>
          failureOrMenstrualCycleData});
}

/// @nodoc
class __$$DataReceivedImplCopyWithImpl<$Res>
    extends _$MenstrualCycleEventCopyWithImpl<$Res, _$DataReceivedImpl>
    implements _$$DataReceivedImplCopyWith<$Res> {
  __$$DataReceivedImplCopyWithImpl(
      _$DataReceivedImpl _value, $Res Function(_$DataReceivedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenstrualCycleEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrMenstrualCycleData = null,
  }) {
    return _then(_$DataReceivedImpl(
      null == failureOrMenstrualCycleData
          ? _value.failureOrMenstrualCycleData
          : failureOrMenstrualCycleData // ignore: cast_nullable_to_non_nullable
              as Either<PeriodTrackingFailure, MenstrualCycleData>,
    ));
  }
}

/// @nodoc

class _$DataReceivedImpl implements _DataReceived {
  const _$DataReceivedImpl(this.failureOrMenstrualCycleData);

  @override
  final Either<PeriodTrackingFailure, MenstrualCycleData>
      failureOrMenstrualCycleData;

  @override
  String toString() {
    return 'MenstrualCycleEvent.dataReceived(failureOrMenstrualCycleData: $failureOrMenstrualCycleData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DataReceivedImpl &&
            (identical(other.failureOrMenstrualCycleData,
                    failureOrMenstrualCycleData) ||
                other.failureOrMenstrualCycleData ==
                    failureOrMenstrualCycleData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrMenstrualCycleData);

  /// Create a copy of MenstrualCycleEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DataReceivedImplCopyWith<_$DataReceivedImpl> get copyWith =>
      __$$DataReceivedImplCopyWithImpl<_$DataReceivedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getInitialData,
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)
        dataReceived,
  }) {
    return dataReceived(failureOrMenstrualCycleData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getInitialData,
    TResult? Function()? watchAllStarted,
    TResult? Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
  }) {
    return dataReceived?.call(failureOrMenstrualCycleData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getInitialData,
    TResult Function()? watchAllStarted,
    TResult Function(
            Either<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
    required TResult orElse(),
  }) {
    if (dataReceived != null) {
      return dataReceived(failureOrMenstrualCycleData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetInitialData value) getInitialData,
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    return dataReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetInitialData value)? getInitialData,
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    return dataReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetInitialData value)? getInitialData,
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    if (dataReceived != null) {
      return dataReceived(this);
    }
    return orElse();
  }
}

abstract class _DataReceived implements MenstrualCycleEvent {
  const factory _DataReceived(
      final Either<PeriodTrackingFailure, MenstrualCycleData>
          failureOrMenstrualCycleData) = _$DataReceivedImpl;

  Either<PeriodTrackingFailure, MenstrualCycleData>
      get failureOrMenstrualCycleData;

  /// Create a copy of MenstrualCycleEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DataReceivedImplCopyWith<_$DataReceivedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MenstrualCycleState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(int currentCycleDay, int periodDays,
            int cycleLength, int ovulationDayStart, int ovulationDaysLength)
        data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(int currentCycleDay, int periodDays, int cycleLength,
            int ovulationDayStart, int ovulationDaysLength)?
        data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(int currentCycleDay, int periodDays, int cycleLength,
            int ovulationDayStart, int ovulationDaysLength)?
        data,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Loading value) loading,
    required TResult Function(_Data value) data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Data value)? data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Loading value)? loading,
    TResult Function(_Data value)? data,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MenstrualCycleStateCopyWith<$Res> {
  factory $MenstrualCycleStateCopyWith(
          MenstrualCycleState value, $Res Function(MenstrualCycleState) then) =
      _$MenstrualCycleStateCopyWithImpl<$Res, MenstrualCycleState>;
}

/// @nodoc
class _$MenstrualCycleStateCopyWithImpl<$Res, $Val extends MenstrualCycleState>
    implements $MenstrualCycleStateCopyWith<$Res> {
  _$MenstrualCycleStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MenstrualCycleState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$MenstrualCycleStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenstrualCycleState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl extends _Loading {
  const _$LoadingImpl() : super._();

  @override
  String toString() {
    return 'MenstrualCycleState.loading()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(int currentCycleDay, int periodDays,
            int cycleLength, int ovulationDayStart, int ovulationDaysLength)
        data,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(int currentCycleDay, int periodDays, int cycleLength,
            int ovulationDayStart, int ovulationDaysLength)?
        data,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(int currentCycleDay, int periodDays, int cycleLength,
            int ovulationDayStart, int ovulationDaysLength)?
        data,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Loading value) loading,
    required TResult Function(_Data value) data,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Data value)? data,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Loading value)? loading,
    TResult Function(_Data value)? data,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading extends MenstrualCycleState {
  const factory _Loading() = _$LoadingImpl;
  const _Loading._() : super._();
}

/// @nodoc
abstract class _$$DataImplCopyWith<$Res> {
  factory _$$DataImplCopyWith(
          _$DataImpl value, $Res Function(_$DataImpl) then) =
      __$$DataImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {int currentCycleDay,
      int periodDays,
      int cycleLength,
      int ovulationDayStart,
      int ovulationDaysLength});
}

/// @nodoc
class __$$DataImplCopyWithImpl<$Res>
    extends _$MenstrualCycleStateCopyWithImpl<$Res, _$DataImpl>
    implements _$$DataImplCopyWith<$Res> {
  __$$DataImplCopyWithImpl(_$DataImpl _value, $Res Function(_$DataImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenstrualCycleState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentCycleDay = null,
    Object? periodDays = null,
    Object? cycleLength = null,
    Object? ovulationDayStart = null,
    Object? ovulationDaysLength = null,
  }) {
    return _then(_$DataImpl(
      currentCycleDay: null == currentCycleDay
          ? _value.currentCycleDay
          : currentCycleDay // ignore: cast_nullable_to_non_nullable
              as int,
      periodDays: null == periodDays
          ? _value.periodDays
          : periodDays // ignore: cast_nullable_to_non_nullable
              as int,
      cycleLength: null == cycleLength
          ? _value.cycleLength
          : cycleLength // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDayStart: null == ovulationDayStart
          ? _value.ovulationDayStart
          : ovulationDayStart // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDaysLength: null == ovulationDaysLength
          ? _value.ovulationDaysLength
          : ovulationDaysLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$DataImpl extends _Data {
  const _$DataImpl(
      {required this.currentCycleDay,
      required this.periodDays,
      required this.cycleLength,
      required this.ovulationDayStart,
      required this.ovulationDaysLength})
      : super._();

  @override
  final int currentCycleDay;
  @override
  final int periodDays;
  @override
  final int cycleLength;
  @override
  final int ovulationDayStart;
  @override
  final int ovulationDaysLength;

  @override
  String toString() {
    return 'MenstrualCycleState.data(currentCycleDay: $currentCycleDay, periodDays: $periodDays, cycleLength: $cycleLength, ovulationDayStart: $ovulationDayStart, ovulationDaysLength: $ovulationDaysLength)';
  }

  /// Create a copy of MenstrualCycleState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DataImplCopyWith<_$DataImpl> get copyWith =>
      __$$DataImplCopyWithImpl<_$DataImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(int currentCycleDay, int periodDays,
            int cycleLength, int ovulationDayStart, int ovulationDaysLength)
        data,
  }) {
    return data(currentCycleDay, periodDays, cycleLength, ovulationDayStart,
        ovulationDaysLength);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(int currentCycleDay, int periodDays, int cycleLength,
            int ovulationDayStart, int ovulationDaysLength)?
        data,
  }) {
    return data?.call(currentCycleDay, periodDays, cycleLength,
        ovulationDayStart, ovulationDaysLength);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(int currentCycleDay, int periodDays, int cycleLength,
            int ovulationDayStart, int ovulationDaysLength)?
        data,
    required TResult orElse(),
  }) {
    if (data != null) {
      return data(currentCycleDay, periodDays, cycleLength, ovulationDayStart,
          ovulationDaysLength);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Loading value) loading,
    required TResult Function(_Data value) data,
  }) {
    return data(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Data value)? data,
  }) {
    return data?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Loading value)? loading,
    TResult Function(_Data value)? data,
    required TResult orElse(),
  }) {
    if (data != null) {
      return data(this);
    }
    return orElse();
  }
}

abstract class _Data extends MenstrualCycleState {
  const factory _Data(
      {required final int currentCycleDay,
      required final int periodDays,
      required final int cycleLength,
      required final int ovulationDayStart,
      required final int ovulationDaysLength}) = _$DataImpl;
  const _Data._() : super._();

  int get currentCycleDay;
  int get periodDays;
  int get cycleLength;
  int get ovulationDayStart;
  int get ovulationDaysLength;

  /// Create a copy of MenstrualCycleState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DataImplCopyWith<_$DataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
