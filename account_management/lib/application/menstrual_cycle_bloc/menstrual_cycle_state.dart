part of 'menstrual_cycle_bloc.dart';

    @freezed
    class MenstrualCycleState with _$MenstrualCycleState {
      const MenstrualCycleState._();

      const factory MenstrualCycleState.loading() = _Loading;

      const factory MenstrualCycleState.data({
        required int currentCycleDay,
        required int periodDays,
        required int cycleLength,
        required int ovulationDayStart,
        required int ovulationDaysLength,
      }) = _Data;

      factory MenstrualCycleState.initial() => const MenstrualCycleState.data(
        currentCycleDay: 1,
        periodDays: 6,
        cycleLength: 28,
        ovulationDayStart: 14,
        ovulationDaysLength: 7,
      );

      @override
      bool operator ==(Object other) {
        if (identical(this, other)) return true;
        return other is MenstrualCycleState &&
            other.maybeMap(
              data: (dataState) => maybeMap(
                data: (thisState) =>
                    dataState.currentCycleDay == thisState.currentCycleDay &&
                    dataState.periodDays == thisState.periodDays &&
                    dataState.cycleLength == thisState.cycleLength &&
                    dataState.ovulationDayStart == thisState.ovulationDayStart &&
                    dataState.ovulationDaysLength == thisState.ovulationDaysLength,
                orElse: () => false,
              ),
              orElse: () => identical(this, other),
            );
      }

      @override
      int get hashCode => maybeMap(
        data: (state) => Object.hash(
          state.currentCycleDay,
          state.periodDays,
          state.cycleLength,
          state.ovulationDayStart,
          state.ovulationDaysLength,
        ),
        orElse: () => 0,
      );
    }