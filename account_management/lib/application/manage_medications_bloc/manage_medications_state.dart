part of 'manage_medications_bloc.dart';
// ManageMedicationsState is a freezed union class that will be used to represent the different states that can occur in the ManageMedicationsBloc.
@freezed
class ManageMedicationsState with _$ManageMedicationsState {
  // Represents the initial state of the Manage Medications bloc
  const factory ManageMedicationsState.initial() = _Initial;
  // Represents the state where an action is in progress
  const factory ManageMedicationsState.actionInProgress() = _ActionInProgress;

// Represents the state where a medication has been deleted successfully
  const factory ManageMedicationsState.medicationDeleted() = _MedicationDeleted;
// Represents the state where a medication operation has failed
  const factory ManageMedicationsState.medicationFailure(
      MedicationFailure failure) = _MedicationFailure;
}