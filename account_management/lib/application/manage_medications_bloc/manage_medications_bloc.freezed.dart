// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_medications_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManageMedicationsEvent {
  MedicationModel get medication => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MedicationModel medication) deleteMedication,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MedicationModel medication)? deleteMedication,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MedicationModel medication)? deleteMedication,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeleteMedication value) deleteMedication,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeleteMedication value)? deleteMedication,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeleteMedication value)? deleteMedication,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ManageMedicationsEventCopyWith<ManageMedicationsEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageMedicationsEventCopyWith<$Res> {
  factory $ManageMedicationsEventCopyWith(ManageMedicationsEvent value,
          $Res Function(ManageMedicationsEvent) then) =
      _$ManageMedicationsEventCopyWithImpl<$Res, ManageMedicationsEvent>;
  @useResult
  $Res call({MedicationModel medication});
}

/// @nodoc
class _$ManageMedicationsEventCopyWithImpl<$Res,
        $Val extends ManageMedicationsEvent>
    implements $ManageMedicationsEventCopyWith<$Res> {
  _$ManageMedicationsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medication = null,
  }) {
    return _then(_value.copyWith(
      medication: null == medication
          ? _value.medication
          : medication // ignore: cast_nullable_to_non_nullable
              as MedicationModel,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeleteMedicationImplCopyWith<$Res>
    implements $ManageMedicationsEventCopyWith<$Res> {
  factory _$$DeleteMedicationImplCopyWith(_$DeleteMedicationImpl value,
          $Res Function(_$DeleteMedicationImpl) then) =
      __$$DeleteMedicationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({MedicationModel medication});
}

/// @nodoc
class __$$DeleteMedicationImplCopyWithImpl<$Res>
    extends _$ManageMedicationsEventCopyWithImpl<$Res, _$DeleteMedicationImpl>
    implements _$$DeleteMedicationImplCopyWith<$Res> {
  __$$DeleteMedicationImplCopyWithImpl(_$DeleteMedicationImpl _value,
      $Res Function(_$DeleteMedicationImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medication = null,
  }) {
    return _then(_$DeleteMedicationImpl(
      null == medication
          ? _value.medication
          : medication // ignore: cast_nullable_to_non_nullable
              as MedicationModel,
    ));
  }
}

/// @nodoc

class _$DeleteMedicationImpl implements _DeleteMedication {
  const _$DeleteMedicationImpl(this.medication);

  @override
  final MedicationModel medication;

  @override
  String toString() {
    return 'ManageMedicationsEvent.deleteMedication(medication: $medication)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteMedicationImpl &&
            (identical(other.medication, medication) ||
                other.medication == medication));
  }

  @override
  int get hashCode => Object.hash(runtimeType, medication);

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteMedicationImplCopyWith<_$DeleteMedicationImpl> get copyWith =>
      __$$DeleteMedicationImplCopyWithImpl<_$DeleteMedicationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MedicationModel medication) deleteMedication,
  }) {
    return deleteMedication(medication);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MedicationModel medication)? deleteMedication,
  }) {
    return deleteMedication?.call(medication);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MedicationModel medication)? deleteMedication,
    required TResult orElse(),
  }) {
    if (deleteMedication != null) {
      return deleteMedication(medication);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeleteMedication value) deleteMedication,
  }) {
    return deleteMedication(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeleteMedication value)? deleteMedication,
  }) {
    return deleteMedication?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeleteMedication value)? deleteMedication,
    required TResult orElse(),
  }) {
    if (deleteMedication != null) {
      return deleteMedication(this);
    }
    return orElse();
  }
}

abstract class _DeleteMedication implements ManageMedicationsEvent {
  const factory _DeleteMedication(final MedicationModel medication) =
      _$DeleteMedicationImpl;

  @override
  MedicationModel get medication;

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteMedicationImplCopyWith<_$DeleteMedicationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ManageMedicationsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionInProgress,
    required TResult Function() medicationDeleted,
    required TResult Function(MedicationFailure failure) medicationFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionInProgress,
    TResult? Function()? medicationDeleted,
    TResult? Function(MedicationFailure failure)? medicationFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionInProgress,
    TResult Function()? medicationDeleted,
    TResult Function(MedicationFailure failure)? medicationFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_ActionInProgress value) actionInProgress,
    required TResult Function(_MedicationDeleted value) medicationDeleted,
    required TResult Function(_MedicationFailure value) medicationFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_ActionInProgress value)? actionInProgress,
    TResult? Function(_MedicationDeleted value)? medicationDeleted,
    TResult? Function(_MedicationFailure value)? medicationFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_ActionInProgress value)? actionInProgress,
    TResult Function(_MedicationDeleted value)? medicationDeleted,
    TResult Function(_MedicationFailure value)? medicationFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageMedicationsStateCopyWith<$Res> {
  factory $ManageMedicationsStateCopyWith(ManageMedicationsState value,
          $Res Function(ManageMedicationsState) then) =
      _$ManageMedicationsStateCopyWithImpl<$Res, ManageMedicationsState>;
}

/// @nodoc
class _$ManageMedicationsStateCopyWithImpl<$Res,
        $Val extends ManageMedicationsState>
    implements $ManageMedicationsStateCopyWith<$Res> {
  _$ManageMedicationsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ManageMedicationsStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'ManageMedicationsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionInProgress,
    required TResult Function() medicationDeleted,
    required TResult Function(MedicationFailure failure) medicationFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionInProgress,
    TResult? Function()? medicationDeleted,
    TResult? Function(MedicationFailure failure)? medicationFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionInProgress,
    TResult Function()? medicationDeleted,
    TResult Function(MedicationFailure failure)? medicationFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_ActionInProgress value) actionInProgress,
    required TResult Function(_MedicationDeleted value) medicationDeleted,
    required TResult Function(_MedicationFailure value) medicationFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_ActionInProgress value)? actionInProgress,
    TResult? Function(_MedicationDeleted value)? medicationDeleted,
    TResult? Function(_MedicationFailure value)? medicationFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_ActionInProgress value)? actionInProgress,
    TResult Function(_MedicationDeleted value)? medicationDeleted,
    TResult Function(_MedicationFailure value)? medicationFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements ManageMedicationsState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$ActionInProgressImplCopyWith<$Res> {
  factory _$$ActionInProgressImplCopyWith(_$ActionInProgressImpl value,
          $Res Function(_$ActionInProgressImpl) then) =
      __$$ActionInProgressImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ActionInProgressImplCopyWithImpl<$Res>
    extends _$ManageMedicationsStateCopyWithImpl<$Res, _$ActionInProgressImpl>
    implements _$$ActionInProgressImplCopyWith<$Res> {
  __$$ActionInProgressImplCopyWithImpl(_$ActionInProgressImpl _value,
      $Res Function(_$ActionInProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ActionInProgressImpl implements _ActionInProgress {
  const _$ActionInProgressImpl();

  @override
  String toString() {
    return 'ManageMedicationsState.actionInProgress()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ActionInProgressImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionInProgress,
    required TResult Function() medicationDeleted,
    required TResult Function(MedicationFailure failure) medicationFailure,
  }) {
    return actionInProgress();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionInProgress,
    TResult? Function()? medicationDeleted,
    TResult? Function(MedicationFailure failure)? medicationFailure,
  }) {
    return actionInProgress?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionInProgress,
    TResult Function()? medicationDeleted,
    TResult Function(MedicationFailure failure)? medicationFailure,
    required TResult orElse(),
  }) {
    if (actionInProgress != null) {
      return actionInProgress();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_ActionInProgress value) actionInProgress,
    required TResult Function(_MedicationDeleted value) medicationDeleted,
    required TResult Function(_MedicationFailure value) medicationFailure,
  }) {
    return actionInProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_ActionInProgress value)? actionInProgress,
    TResult? Function(_MedicationDeleted value)? medicationDeleted,
    TResult? Function(_MedicationFailure value)? medicationFailure,
  }) {
    return actionInProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_ActionInProgress value)? actionInProgress,
    TResult Function(_MedicationDeleted value)? medicationDeleted,
    TResult Function(_MedicationFailure value)? medicationFailure,
    required TResult orElse(),
  }) {
    if (actionInProgress != null) {
      return actionInProgress(this);
    }
    return orElse();
  }
}

abstract class _ActionInProgress implements ManageMedicationsState {
  const factory _ActionInProgress() = _$ActionInProgressImpl;
}

/// @nodoc
abstract class _$$MedicationDeletedImplCopyWith<$Res> {
  factory _$$MedicationDeletedImplCopyWith(_$MedicationDeletedImpl value,
          $Res Function(_$MedicationDeletedImpl) then) =
      __$$MedicationDeletedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MedicationDeletedImplCopyWithImpl<$Res>
    extends _$ManageMedicationsStateCopyWithImpl<$Res, _$MedicationDeletedImpl>
    implements _$$MedicationDeletedImplCopyWith<$Res> {
  __$$MedicationDeletedImplCopyWithImpl(_$MedicationDeletedImpl _value,
      $Res Function(_$MedicationDeletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$MedicationDeletedImpl implements _MedicationDeleted {
  const _$MedicationDeletedImpl();

  @override
  String toString() {
    return 'ManageMedicationsState.medicationDeleted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$MedicationDeletedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionInProgress,
    required TResult Function() medicationDeleted,
    required TResult Function(MedicationFailure failure) medicationFailure,
  }) {
    return medicationDeleted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionInProgress,
    TResult? Function()? medicationDeleted,
    TResult? Function(MedicationFailure failure)? medicationFailure,
  }) {
    return medicationDeleted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionInProgress,
    TResult Function()? medicationDeleted,
    TResult Function(MedicationFailure failure)? medicationFailure,
    required TResult orElse(),
  }) {
    if (medicationDeleted != null) {
      return medicationDeleted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_ActionInProgress value) actionInProgress,
    required TResult Function(_MedicationDeleted value) medicationDeleted,
    required TResult Function(_MedicationFailure value) medicationFailure,
  }) {
    return medicationDeleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_ActionInProgress value)? actionInProgress,
    TResult? Function(_MedicationDeleted value)? medicationDeleted,
    TResult? Function(_MedicationFailure value)? medicationFailure,
  }) {
    return medicationDeleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_ActionInProgress value)? actionInProgress,
    TResult Function(_MedicationDeleted value)? medicationDeleted,
    TResult Function(_MedicationFailure value)? medicationFailure,
    required TResult orElse(),
  }) {
    if (medicationDeleted != null) {
      return medicationDeleted(this);
    }
    return orElse();
  }
}

abstract class _MedicationDeleted implements ManageMedicationsState {
  const factory _MedicationDeleted() = _$MedicationDeletedImpl;
}

/// @nodoc
abstract class _$$MedicationFailureImplCopyWith<$Res> {
  factory _$$MedicationFailureImplCopyWith(_$MedicationFailureImpl value,
          $Res Function(_$MedicationFailureImpl) then) =
      __$$MedicationFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MedicationFailure failure});

  $MedicationFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$MedicationFailureImplCopyWithImpl<$Res>
    extends _$ManageMedicationsStateCopyWithImpl<$Res, _$MedicationFailureImpl>
    implements _$$MedicationFailureImplCopyWith<$Res> {
  __$$MedicationFailureImplCopyWithImpl(_$MedicationFailureImpl _value,
      $Res Function(_$MedicationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$MedicationFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as MedicationFailure,
    ));
  }

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MedicationFailureCopyWith<$Res> get failure {
    return $MedicationFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$MedicationFailureImpl implements _MedicationFailure {
  const _$MedicationFailureImpl(this.failure);

  @override
  final MedicationFailure failure;

  @override
  String toString() {
    return 'ManageMedicationsState.medicationFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MedicationFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MedicationFailureImplCopyWith<_$MedicationFailureImpl> get copyWith =>
      __$$MedicationFailureImplCopyWithImpl<_$MedicationFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionInProgress,
    required TResult Function() medicationDeleted,
    required TResult Function(MedicationFailure failure) medicationFailure,
  }) {
    return medicationFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionInProgress,
    TResult? Function()? medicationDeleted,
    TResult? Function(MedicationFailure failure)? medicationFailure,
  }) {
    return medicationFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionInProgress,
    TResult Function()? medicationDeleted,
    TResult Function(MedicationFailure failure)? medicationFailure,
    required TResult orElse(),
  }) {
    if (medicationFailure != null) {
      return medicationFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_ActionInProgress value) actionInProgress,
    required TResult Function(_MedicationDeleted value) medicationDeleted,
    required TResult Function(_MedicationFailure value) medicationFailure,
  }) {
    return medicationFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_ActionInProgress value)? actionInProgress,
    TResult? Function(_MedicationDeleted value)? medicationDeleted,
    TResult? Function(_MedicationFailure value)? medicationFailure,
  }) {
    return medicationFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_ActionInProgress value)? actionInProgress,
    TResult Function(_MedicationDeleted value)? medicationDeleted,
    TResult Function(_MedicationFailure value)? medicationFailure,
    required TResult orElse(),
  }) {
    if (medicationFailure != null) {
      return medicationFailure(this);
    }
    return orElse();
  }
}

abstract class _MedicationFailure implements ManageMedicationsState {
  const factory _MedicationFailure(final MedicationFailure failure) =
      _$MedicationFailureImpl;

  MedicationFailure get failure;

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MedicationFailureImplCopyWith<_$MedicationFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
