// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'period_reminder_settings_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PeriodReminderSettingsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSettings,
    required TResult Function(bool enabled) togglePeriodReminder,
    required TResult Function(int daysBefore) updatePeriodReminderDays,
    required TResult Function(bool enabled) toggleOvulationReminder,
    required TResult Function(int daysBefore) updateOvulationReminderDays,
    required TResult Function() saveSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSettings,
    TResult? Function(bool enabled)? togglePeriodReminder,
    TResult? Function(int daysBefore)? updatePeriodReminderDays,
    TResult? Function(bool enabled)? toggleOvulationReminder,
    TResult? Function(int daysBefore)? updateOvulationReminderDays,
    TResult? Function()? saveSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSettings,
    TResult Function(bool enabled)? togglePeriodReminder,
    TResult Function(int daysBefore)? updatePeriodReminderDays,
    TResult Function(bool enabled)? toggleOvulationReminder,
    TResult Function(int daysBefore)? updateOvulationReminderDays,
    TResult Function()? saveSettings,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSettings value) loadSettings,
    required TResult Function(_TogglePeriodReminder value) togglePeriodReminder,
    required TResult Function(_UpdatePeriodReminderDays value)
        updatePeriodReminderDays,
    required TResult Function(_ToggleOvulationReminder value)
        toggleOvulationReminder,
    required TResult Function(_UpdateOvulationReminderDays value)
        updateOvulationReminderDays,
    required TResult Function(_SaveSettings value) saveSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSettings value)? loadSettings,
    TResult? Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult? Function(_UpdatePeriodReminderDays value)?
        updatePeriodReminderDays,
    TResult? Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult? Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult? Function(_SaveSettings value)? saveSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSettings value)? loadSettings,
    TResult Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult Function(_UpdatePeriodReminderDays value)? updatePeriodReminderDays,
    TResult Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult Function(_SaveSettings value)? saveSettings,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodReminderSettingsEventCopyWith<$Res> {
  factory $PeriodReminderSettingsEventCopyWith(
          PeriodReminderSettingsEvent value,
          $Res Function(PeriodReminderSettingsEvent) then) =
      _$PeriodReminderSettingsEventCopyWithImpl<$Res,
          PeriodReminderSettingsEvent>;
}

/// @nodoc
class _$PeriodReminderSettingsEventCopyWithImpl<$Res,
        $Val extends PeriodReminderSettingsEvent>
    implements $PeriodReminderSettingsEventCopyWith<$Res> {
  _$PeriodReminderSettingsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadSettingsImplCopyWith<$Res> {
  factory _$$LoadSettingsImplCopyWith(
          _$LoadSettingsImpl value, $Res Function(_$LoadSettingsImpl) then) =
      __$$LoadSettingsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadSettingsImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsEventCopyWithImpl<$Res, _$LoadSettingsImpl>
    implements _$$LoadSettingsImplCopyWith<$Res> {
  __$$LoadSettingsImplCopyWithImpl(
      _$LoadSettingsImpl _value, $Res Function(_$LoadSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadSettingsImpl implements _LoadSettings {
  const _$LoadSettingsImpl();

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.loadSettings()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadSettingsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSettings,
    required TResult Function(bool enabled) togglePeriodReminder,
    required TResult Function(int daysBefore) updatePeriodReminderDays,
    required TResult Function(bool enabled) toggleOvulationReminder,
    required TResult Function(int daysBefore) updateOvulationReminderDays,
    required TResult Function() saveSettings,
  }) {
    return loadSettings();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSettings,
    TResult? Function(bool enabled)? togglePeriodReminder,
    TResult? Function(int daysBefore)? updatePeriodReminderDays,
    TResult? Function(bool enabled)? toggleOvulationReminder,
    TResult? Function(int daysBefore)? updateOvulationReminderDays,
    TResult? Function()? saveSettings,
  }) {
    return loadSettings?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSettings,
    TResult Function(bool enabled)? togglePeriodReminder,
    TResult Function(int daysBefore)? updatePeriodReminderDays,
    TResult Function(bool enabled)? toggleOvulationReminder,
    TResult Function(int daysBefore)? updateOvulationReminderDays,
    TResult Function()? saveSettings,
    required TResult orElse(),
  }) {
    if (loadSettings != null) {
      return loadSettings();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSettings value) loadSettings,
    required TResult Function(_TogglePeriodReminder value) togglePeriodReminder,
    required TResult Function(_UpdatePeriodReminderDays value)
        updatePeriodReminderDays,
    required TResult Function(_ToggleOvulationReminder value)
        toggleOvulationReminder,
    required TResult Function(_UpdateOvulationReminderDays value)
        updateOvulationReminderDays,
    required TResult Function(_SaveSettings value) saveSettings,
  }) {
    return loadSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSettings value)? loadSettings,
    TResult? Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult? Function(_UpdatePeriodReminderDays value)?
        updatePeriodReminderDays,
    TResult? Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult? Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult? Function(_SaveSettings value)? saveSettings,
  }) {
    return loadSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSettings value)? loadSettings,
    TResult Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult Function(_UpdatePeriodReminderDays value)? updatePeriodReminderDays,
    TResult Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult Function(_SaveSettings value)? saveSettings,
    required TResult orElse(),
  }) {
    if (loadSettings != null) {
      return loadSettings(this);
    }
    return orElse();
  }
}

abstract class _LoadSettings implements PeriodReminderSettingsEvent {
  const factory _LoadSettings() = _$LoadSettingsImpl;
}

/// @nodoc
abstract class _$$TogglePeriodReminderImplCopyWith<$Res> {
  factory _$$TogglePeriodReminderImplCopyWith(_$TogglePeriodReminderImpl value,
          $Res Function(_$TogglePeriodReminderImpl) then) =
      __$$TogglePeriodReminderImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool enabled});
}

/// @nodoc
class __$$TogglePeriodReminderImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsEventCopyWithImpl<$Res,
        _$TogglePeriodReminderImpl>
    implements _$$TogglePeriodReminderImplCopyWith<$Res> {
  __$$TogglePeriodReminderImplCopyWithImpl(_$TogglePeriodReminderImpl _value,
      $Res Function(_$TogglePeriodReminderImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
  }) {
    return _then(_$TogglePeriodReminderImpl(
      null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$TogglePeriodReminderImpl implements _TogglePeriodReminder {
  const _$TogglePeriodReminderImpl(this.enabled);

  @override
  final bool enabled;

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.togglePeriodReminder(enabled: $enabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TogglePeriodReminderImpl &&
            (identical(other.enabled, enabled) || other.enabled == enabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, enabled);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TogglePeriodReminderImplCopyWith<_$TogglePeriodReminderImpl>
      get copyWith =>
          __$$TogglePeriodReminderImplCopyWithImpl<_$TogglePeriodReminderImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSettings,
    required TResult Function(bool enabled) togglePeriodReminder,
    required TResult Function(int daysBefore) updatePeriodReminderDays,
    required TResult Function(bool enabled) toggleOvulationReminder,
    required TResult Function(int daysBefore) updateOvulationReminderDays,
    required TResult Function() saveSettings,
  }) {
    return togglePeriodReminder(enabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSettings,
    TResult? Function(bool enabled)? togglePeriodReminder,
    TResult? Function(int daysBefore)? updatePeriodReminderDays,
    TResult? Function(bool enabled)? toggleOvulationReminder,
    TResult? Function(int daysBefore)? updateOvulationReminderDays,
    TResult? Function()? saveSettings,
  }) {
    return togglePeriodReminder?.call(enabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSettings,
    TResult Function(bool enabled)? togglePeriodReminder,
    TResult Function(int daysBefore)? updatePeriodReminderDays,
    TResult Function(bool enabled)? toggleOvulationReminder,
    TResult Function(int daysBefore)? updateOvulationReminderDays,
    TResult Function()? saveSettings,
    required TResult orElse(),
  }) {
    if (togglePeriodReminder != null) {
      return togglePeriodReminder(enabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSettings value) loadSettings,
    required TResult Function(_TogglePeriodReminder value) togglePeriodReminder,
    required TResult Function(_UpdatePeriodReminderDays value)
        updatePeriodReminderDays,
    required TResult Function(_ToggleOvulationReminder value)
        toggleOvulationReminder,
    required TResult Function(_UpdateOvulationReminderDays value)
        updateOvulationReminderDays,
    required TResult Function(_SaveSettings value) saveSettings,
  }) {
    return togglePeriodReminder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSettings value)? loadSettings,
    TResult? Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult? Function(_UpdatePeriodReminderDays value)?
        updatePeriodReminderDays,
    TResult? Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult? Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult? Function(_SaveSettings value)? saveSettings,
  }) {
    return togglePeriodReminder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSettings value)? loadSettings,
    TResult Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult Function(_UpdatePeriodReminderDays value)? updatePeriodReminderDays,
    TResult Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult Function(_SaveSettings value)? saveSettings,
    required TResult orElse(),
  }) {
    if (togglePeriodReminder != null) {
      return togglePeriodReminder(this);
    }
    return orElse();
  }
}

abstract class _TogglePeriodReminder implements PeriodReminderSettingsEvent {
  const factory _TogglePeriodReminder(final bool enabled) =
      _$TogglePeriodReminderImpl;

  bool get enabled;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TogglePeriodReminderImplCopyWith<_$TogglePeriodReminderImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatePeriodReminderDaysImplCopyWith<$Res> {
  factory _$$UpdatePeriodReminderDaysImplCopyWith(
          _$UpdatePeriodReminderDaysImpl value,
          $Res Function(_$UpdatePeriodReminderDaysImpl) then) =
      __$$UpdatePeriodReminderDaysImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int daysBefore});
}

/// @nodoc
class __$$UpdatePeriodReminderDaysImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsEventCopyWithImpl<$Res,
        _$UpdatePeriodReminderDaysImpl>
    implements _$$UpdatePeriodReminderDaysImplCopyWith<$Res> {
  __$$UpdatePeriodReminderDaysImplCopyWithImpl(
      _$UpdatePeriodReminderDaysImpl _value,
      $Res Function(_$UpdatePeriodReminderDaysImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? daysBefore = null,
  }) {
    return _then(_$UpdatePeriodReminderDaysImpl(
      null == daysBefore
          ? _value.daysBefore
          : daysBefore // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$UpdatePeriodReminderDaysImpl implements _UpdatePeriodReminderDays {
  const _$UpdatePeriodReminderDaysImpl(this.daysBefore);

  @override
  final int daysBefore;

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.updatePeriodReminderDays(daysBefore: $daysBefore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatePeriodReminderDaysImpl &&
            (identical(other.daysBefore, daysBefore) ||
                other.daysBefore == daysBefore));
  }

  @override
  int get hashCode => Object.hash(runtimeType, daysBefore);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatePeriodReminderDaysImplCopyWith<_$UpdatePeriodReminderDaysImpl>
      get copyWith => __$$UpdatePeriodReminderDaysImplCopyWithImpl<
          _$UpdatePeriodReminderDaysImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSettings,
    required TResult Function(bool enabled) togglePeriodReminder,
    required TResult Function(int daysBefore) updatePeriodReminderDays,
    required TResult Function(bool enabled) toggleOvulationReminder,
    required TResult Function(int daysBefore) updateOvulationReminderDays,
    required TResult Function() saveSettings,
  }) {
    return updatePeriodReminderDays(daysBefore);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSettings,
    TResult? Function(bool enabled)? togglePeriodReminder,
    TResult? Function(int daysBefore)? updatePeriodReminderDays,
    TResult? Function(bool enabled)? toggleOvulationReminder,
    TResult? Function(int daysBefore)? updateOvulationReminderDays,
    TResult? Function()? saveSettings,
  }) {
    return updatePeriodReminderDays?.call(daysBefore);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSettings,
    TResult Function(bool enabled)? togglePeriodReminder,
    TResult Function(int daysBefore)? updatePeriodReminderDays,
    TResult Function(bool enabled)? toggleOvulationReminder,
    TResult Function(int daysBefore)? updateOvulationReminderDays,
    TResult Function()? saveSettings,
    required TResult orElse(),
  }) {
    if (updatePeriodReminderDays != null) {
      return updatePeriodReminderDays(daysBefore);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSettings value) loadSettings,
    required TResult Function(_TogglePeriodReminder value) togglePeriodReminder,
    required TResult Function(_UpdatePeriodReminderDays value)
        updatePeriodReminderDays,
    required TResult Function(_ToggleOvulationReminder value)
        toggleOvulationReminder,
    required TResult Function(_UpdateOvulationReminderDays value)
        updateOvulationReminderDays,
    required TResult Function(_SaveSettings value) saveSettings,
  }) {
    return updatePeriodReminderDays(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSettings value)? loadSettings,
    TResult? Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult? Function(_UpdatePeriodReminderDays value)?
        updatePeriodReminderDays,
    TResult? Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult? Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult? Function(_SaveSettings value)? saveSettings,
  }) {
    return updatePeriodReminderDays?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSettings value)? loadSettings,
    TResult Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult Function(_UpdatePeriodReminderDays value)? updatePeriodReminderDays,
    TResult Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult Function(_SaveSettings value)? saveSettings,
    required TResult orElse(),
  }) {
    if (updatePeriodReminderDays != null) {
      return updatePeriodReminderDays(this);
    }
    return orElse();
  }
}

abstract class _UpdatePeriodReminderDays
    implements PeriodReminderSettingsEvent {
  const factory _UpdatePeriodReminderDays(final int daysBefore) =
      _$UpdatePeriodReminderDaysImpl;

  int get daysBefore;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatePeriodReminderDaysImplCopyWith<_$UpdatePeriodReminderDaysImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ToggleOvulationReminderImplCopyWith<$Res> {
  factory _$$ToggleOvulationReminderImplCopyWith(
          _$ToggleOvulationReminderImpl value,
          $Res Function(_$ToggleOvulationReminderImpl) then) =
      __$$ToggleOvulationReminderImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool enabled});
}

/// @nodoc
class __$$ToggleOvulationReminderImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsEventCopyWithImpl<$Res,
        _$ToggleOvulationReminderImpl>
    implements _$$ToggleOvulationReminderImplCopyWith<$Res> {
  __$$ToggleOvulationReminderImplCopyWithImpl(
      _$ToggleOvulationReminderImpl _value,
      $Res Function(_$ToggleOvulationReminderImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
  }) {
    return _then(_$ToggleOvulationReminderImpl(
      null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ToggleOvulationReminderImpl implements _ToggleOvulationReminder {
  const _$ToggleOvulationReminderImpl(this.enabled);

  @override
  final bool enabled;

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.toggleOvulationReminder(enabled: $enabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ToggleOvulationReminderImpl &&
            (identical(other.enabled, enabled) || other.enabled == enabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, enabled);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ToggleOvulationReminderImplCopyWith<_$ToggleOvulationReminderImpl>
      get copyWith => __$$ToggleOvulationReminderImplCopyWithImpl<
          _$ToggleOvulationReminderImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSettings,
    required TResult Function(bool enabled) togglePeriodReminder,
    required TResult Function(int daysBefore) updatePeriodReminderDays,
    required TResult Function(bool enabled) toggleOvulationReminder,
    required TResult Function(int daysBefore) updateOvulationReminderDays,
    required TResult Function() saveSettings,
  }) {
    return toggleOvulationReminder(enabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSettings,
    TResult? Function(bool enabled)? togglePeriodReminder,
    TResult? Function(int daysBefore)? updatePeriodReminderDays,
    TResult? Function(bool enabled)? toggleOvulationReminder,
    TResult? Function(int daysBefore)? updateOvulationReminderDays,
    TResult? Function()? saveSettings,
  }) {
    return toggleOvulationReminder?.call(enabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSettings,
    TResult Function(bool enabled)? togglePeriodReminder,
    TResult Function(int daysBefore)? updatePeriodReminderDays,
    TResult Function(bool enabled)? toggleOvulationReminder,
    TResult Function(int daysBefore)? updateOvulationReminderDays,
    TResult Function()? saveSettings,
    required TResult orElse(),
  }) {
    if (toggleOvulationReminder != null) {
      return toggleOvulationReminder(enabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSettings value) loadSettings,
    required TResult Function(_TogglePeriodReminder value) togglePeriodReminder,
    required TResult Function(_UpdatePeriodReminderDays value)
        updatePeriodReminderDays,
    required TResult Function(_ToggleOvulationReminder value)
        toggleOvulationReminder,
    required TResult Function(_UpdateOvulationReminderDays value)
        updateOvulationReminderDays,
    required TResult Function(_SaveSettings value) saveSettings,
  }) {
    return toggleOvulationReminder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSettings value)? loadSettings,
    TResult? Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult? Function(_UpdatePeriodReminderDays value)?
        updatePeriodReminderDays,
    TResult? Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult? Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult? Function(_SaveSettings value)? saveSettings,
  }) {
    return toggleOvulationReminder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSettings value)? loadSettings,
    TResult Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult Function(_UpdatePeriodReminderDays value)? updatePeriodReminderDays,
    TResult Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult Function(_SaveSettings value)? saveSettings,
    required TResult orElse(),
  }) {
    if (toggleOvulationReminder != null) {
      return toggleOvulationReminder(this);
    }
    return orElse();
  }
}

abstract class _ToggleOvulationReminder implements PeriodReminderSettingsEvent {
  const factory _ToggleOvulationReminder(final bool enabled) =
      _$ToggleOvulationReminderImpl;

  bool get enabled;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ToggleOvulationReminderImplCopyWith<_$ToggleOvulationReminderImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateOvulationReminderDaysImplCopyWith<$Res> {
  factory _$$UpdateOvulationReminderDaysImplCopyWith(
          _$UpdateOvulationReminderDaysImpl value,
          $Res Function(_$UpdateOvulationReminderDaysImpl) then) =
      __$$UpdateOvulationReminderDaysImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int daysBefore});
}

/// @nodoc
class __$$UpdateOvulationReminderDaysImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsEventCopyWithImpl<$Res,
        _$UpdateOvulationReminderDaysImpl>
    implements _$$UpdateOvulationReminderDaysImplCopyWith<$Res> {
  __$$UpdateOvulationReminderDaysImplCopyWithImpl(
      _$UpdateOvulationReminderDaysImpl _value,
      $Res Function(_$UpdateOvulationReminderDaysImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? daysBefore = null,
  }) {
    return _then(_$UpdateOvulationReminderDaysImpl(
      null == daysBefore
          ? _value.daysBefore
          : daysBefore // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$UpdateOvulationReminderDaysImpl
    implements _UpdateOvulationReminderDays {
  const _$UpdateOvulationReminderDaysImpl(this.daysBefore);

  @override
  final int daysBefore;

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.updateOvulationReminderDays(daysBefore: $daysBefore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateOvulationReminderDaysImpl &&
            (identical(other.daysBefore, daysBefore) ||
                other.daysBefore == daysBefore));
  }

  @override
  int get hashCode => Object.hash(runtimeType, daysBefore);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateOvulationReminderDaysImplCopyWith<_$UpdateOvulationReminderDaysImpl>
      get copyWith => __$$UpdateOvulationReminderDaysImplCopyWithImpl<
          _$UpdateOvulationReminderDaysImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSettings,
    required TResult Function(bool enabled) togglePeriodReminder,
    required TResult Function(int daysBefore) updatePeriodReminderDays,
    required TResult Function(bool enabled) toggleOvulationReminder,
    required TResult Function(int daysBefore) updateOvulationReminderDays,
    required TResult Function() saveSettings,
  }) {
    return updateOvulationReminderDays(daysBefore);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSettings,
    TResult? Function(bool enabled)? togglePeriodReminder,
    TResult? Function(int daysBefore)? updatePeriodReminderDays,
    TResult? Function(bool enabled)? toggleOvulationReminder,
    TResult? Function(int daysBefore)? updateOvulationReminderDays,
    TResult? Function()? saveSettings,
  }) {
    return updateOvulationReminderDays?.call(daysBefore);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSettings,
    TResult Function(bool enabled)? togglePeriodReminder,
    TResult Function(int daysBefore)? updatePeriodReminderDays,
    TResult Function(bool enabled)? toggleOvulationReminder,
    TResult Function(int daysBefore)? updateOvulationReminderDays,
    TResult Function()? saveSettings,
    required TResult orElse(),
  }) {
    if (updateOvulationReminderDays != null) {
      return updateOvulationReminderDays(daysBefore);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSettings value) loadSettings,
    required TResult Function(_TogglePeriodReminder value) togglePeriodReminder,
    required TResult Function(_UpdatePeriodReminderDays value)
        updatePeriodReminderDays,
    required TResult Function(_ToggleOvulationReminder value)
        toggleOvulationReminder,
    required TResult Function(_UpdateOvulationReminderDays value)
        updateOvulationReminderDays,
    required TResult Function(_SaveSettings value) saveSettings,
  }) {
    return updateOvulationReminderDays(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSettings value)? loadSettings,
    TResult? Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult? Function(_UpdatePeriodReminderDays value)?
        updatePeriodReminderDays,
    TResult? Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult? Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult? Function(_SaveSettings value)? saveSettings,
  }) {
    return updateOvulationReminderDays?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSettings value)? loadSettings,
    TResult Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult Function(_UpdatePeriodReminderDays value)? updatePeriodReminderDays,
    TResult Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult Function(_SaveSettings value)? saveSettings,
    required TResult orElse(),
  }) {
    if (updateOvulationReminderDays != null) {
      return updateOvulationReminderDays(this);
    }
    return orElse();
  }
}

abstract class _UpdateOvulationReminderDays
    implements PeriodReminderSettingsEvent {
  const factory _UpdateOvulationReminderDays(final int daysBefore) =
      _$UpdateOvulationReminderDaysImpl;

  int get daysBefore;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateOvulationReminderDaysImplCopyWith<_$UpdateOvulationReminderDaysImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SaveSettingsImplCopyWith<$Res> {
  factory _$$SaveSettingsImplCopyWith(
          _$SaveSettingsImpl value, $Res Function(_$SaveSettingsImpl) then) =
      __$$SaveSettingsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SaveSettingsImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsEventCopyWithImpl<$Res, _$SaveSettingsImpl>
    implements _$$SaveSettingsImplCopyWith<$Res> {
  __$$SaveSettingsImplCopyWithImpl(
      _$SaveSettingsImpl _value, $Res Function(_$SaveSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SaveSettingsImpl implements _SaveSettings {
  const _$SaveSettingsImpl();

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.saveSettings()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SaveSettingsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSettings,
    required TResult Function(bool enabled) togglePeriodReminder,
    required TResult Function(int daysBefore) updatePeriodReminderDays,
    required TResult Function(bool enabled) toggleOvulationReminder,
    required TResult Function(int daysBefore) updateOvulationReminderDays,
    required TResult Function() saveSettings,
  }) {
    return saveSettings();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSettings,
    TResult? Function(bool enabled)? togglePeriodReminder,
    TResult? Function(int daysBefore)? updatePeriodReminderDays,
    TResult? Function(bool enabled)? toggleOvulationReminder,
    TResult? Function(int daysBefore)? updateOvulationReminderDays,
    TResult? Function()? saveSettings,
  }) {
    return saveSettings?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSettings,
    TResult Function(bool enabled)? togglePeriodReminder,
    TResult Function(int daysBefore)? updatePeriodReminderDays,
    TResult Function(bool enabled)? toggleOvulationReminder,
    TResult Function(int daysBefore)? updateOvulationReminderDays,
    TResult Function()? saveSettings,
    required TResult orElse(),
  }) {
    if (saveSettings != null) {
      return saveSettings();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSettings value) loadSettings,
    required TResult Function(_TogglePeriodReminder value) togglePeriodReminder,
    required TResult Function(_UpdatePeriodReminderDays value)
        updatePeriodReminderDays,
    required TResult Function(_ToggleOvulationReminder value)
        toggleOvulationReminder,
    required TResult Function(_UpdateOvulationReminderDays value)
        updateOvulationReminderDays,
    required TResult Function(_SaveSettings value) saveSettings,
  }) {
    return saveSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSettings value)? loadSettings,
    TResult? Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult? Function(_UpdatePeriodReminderDays value)?
        updatePeriodReminderDays,
    TResult? Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult? Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult? Function(_SaveSettings value)? saveSettings,
  }) {
    return saveSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSettings value)? loadSettings,
    TResult Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult Function(_UpdatePeriodReminderDays value)? updatePeriodReminderDays,
    TResult Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult Function(_SaveSettings value)? saveSettings,
    required TResult orElse(),
  }) {
    if (saveSettings != null) {
      return saveSettings(this);
    }
    return orElse();
  }
}

abstract class _SaveSettings implements PeriodReminderSettingsEvent {
  const factory _SaveSettings() = _$SaveSettingsImpl;
}

/// @nodoc
mixin _$PeriodReminderSettingsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(PeriodReminderSettings settings) loaded,
    required TResult Function() saving,
    required TResult Function(PeriodReminderSettings settings) saved,
    required TResult Function(String message) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(PeriodReminderSettings settings)? loaded,
    TResult? Function()? saving,
    TResult? Function(PeriodReminderSettings settings)? saved,
    TResult? Function(String message)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(PeriodReminderSettings settings)? loaded,
    TResult Function()? saving,
    TResult Function(PeriodReminderSettings settings)? saved,
    TResult Function(String message)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Saving value) saving,
    required TResult Function(_Saved value) saved,
    required TResult Function(_Failure value) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Saving value)? saving,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_Failure value)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Saving value)? saving,
    TResult Function(_Saved value)? saved,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodReminderSettingsStateCopyWith<$Res> {
  factory $PeriodReminderSettingsStateCopyWith(
          PeriodReminderSettingsState value,
          $Res Function(PeriodReminderSettingsState) then) =
      _$PeriodReminderSettingsStateCopyWithImpl<$Res,
          PeriodReminderSettingsState>;
}

/// @nodoc
class _$PeriodReminderSettingsStateCopyWithImpl<$Res,
        $Val extends PeriodReminderSettingsState>
    implements $PeriodReminderSettingsStateCopyWith<$Res> {
  _$PeriodReminderSettingsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'PeriodReminderSettingsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(PeriodReminderSettings settings) loaded,
    required TResult Function() saving,
    required TResult Function(PeriodReminderSettings settings) saved,
    required TResult Function(String message) failure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(PeriodReminderSettings settings)? loaded,
    TResult? Function()? saving,
    TResult? Function(PeriodReminderSettings settings)? saved,
    TResult? Function(String message)? failure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(PeriodReminderSettings settings)? loaded,
    TResult Function()? saving,
    TResult Function(PeriodReminderSettings settings)? saved,
    TResult Function(String message)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Saving value) saving,
    required TResult Function(_Saved value) saved,
    required TResult Function(_Failure value) failure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Saving value)? saving,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_Failure value)? failure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Saving value)? saving,
    TResult Function(_Saved value)? saved,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements PeriodReminderSettingsState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'PeriodReminderSettingsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(PeriodReminderSettings settings) loaded,
    required TResult Function() saving,
    required TResult Function(PeriodReminderSettings settings) saved,
    required TResult Function(String message) failure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(PeriodReminderSettings settings)? loaded,
    TResult? Function()? saving,
    TResult? Function(PeriodReminderSettings settings)? saved,
    TResult? Function(String message)? failure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(PeriodReminderSettings settings)? loaded,
    TResult Function()? saving,
    TResult Function(PeriodReminderSettings settings)? saved,
    TResult Function(String message)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Saving value) saving,
    required TResult Function(_Saved value) saved,
    required TResult Function(_Failure value) failure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Saving value)? saving,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_Failure value)? failure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Saving value)? saving,
    TResult Function(_Saved value)? saved,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements PeriodReminderSettingsState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodReminderSettings settings});
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? settings = null,
  }) {
    return _then(_$LoadedImpl(
      settings: null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as PeriodReminderSettings,
    ));
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl({required this.settings});

  @override
  final PeriodReminderSettings settings;

  @override
  String toString() {
    return 'PeriodReminderSettingsState.loaded(settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            (identical(other.settings, settings) ||
                other.settings == settings));
  }

  @override
  int get hashCode => Object.hash(runtimeType, settings);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(PeriodReminderSettings settings) loaded,
    required TResult Function() saving,
    required TResult Function(PeriodReminderSettings settings) saved,
    required TResult Function(String message) failure,
  }) {
    return loaded(settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(PeriodReminderSettings settings)? loaded,
    TResult? Function()? saving,
    TResult? Function(PeriodReminderSettings settings)? saved,
    TResult? Function(String message)? failure,
  }) {
    return loaded?.call(settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(PeriodReminderSettings settings)? loaded,
    TResult Function()? saving,
    TResult Function(PeriodReminderSettings settings)? saved,
    TResult Function(String message)? failure,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Saving value) saving,
    required TResult Function(_Saved value) saved,
    required TResult Function(_Failure value) failure,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Saving value)? saving,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_Failure value)? failure,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Saving value)? saving,
    TResult Function(_Saved value)? saved,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements PeriodReminderSettingsState {
  const factory _Loaded({required final PeriodReminderSettings settings}) =
      _$LoadedImpl;

  PeriodReminderSettings get settings;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SavingImplCopyWith<$Res> {
  factory _$$SavingImplCopyWith(
          _$SavingImpl value, $Res Function(_$SavingImpl) then) =
      __$$SavingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SavingImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsStateCopyWithImpl<$Res, _$SavingImpl>
    implements _$$SavingImplCopyWith<$Res> {
  __$$SavingImplCopyWithImpl(
      _$SavingImpl _value, $Res Function(_$SavingImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SavingImpl implements _Saving {
  const _$SavingImpl();

  @override
  String toString() {
    return 'PeriodReminderSettingsState.saving()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SavingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(PeriodReminderSettings settings) loaded,
    required TResult Function() saving,
    required TResult Function(PeriodReminderSettings settings) saved,
    required TResult Function(String message) failure,
  }) {
    return saving();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(PeriodReminderSettings settings)? loaded,
    TResult? Function()? saving,
    TResult? Function(PeriodReminderSettings settings)? saved,
    TResult? Function(String message)? failure,
  }) {
    return saving?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(PeriodReminderSettings settings)? loaded,
    TResult Function()? saving,
    TResult Function(PeriodReminderSettings settings)? saved,
    TResult Function(String message)? failure,
    required TResult orElse(),
  }) {
    if (saving != null) {
      return saving();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Saving value) saving,
    required TResult Function(_Saved value) saved,
    required TResult Function(_Failure value) failure,
  }) {
    return saving(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Saving value)? saving,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_Failure value)? failure,
  }) {
    return saving?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Saving value)? saving,
    TResult Function(_Saved value)? saved,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (saving != null) {
      return saving(this);
    }
    return orElse();
  }
}

abstract class _Saving implements PeriodReminderSettingsState {
  const factory _Saving() = _$SavingImpl;
}

/// @nodoc
abstract class _$$SavedImplCopyWith<$Res> {
  factory _$$SavedImplCopyWith(
          _$SavedImpl value, $Res Function(_$SavedImpl) then) =
      __$$SavedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodReminderSettings settings});
}

/// @nodoc
class __$$SavedImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsStateCopyWithImpl<$Res, _$SavedImpl>
    implements _$$SavedImplCopyWith<$Res> {
  __$$SavedImplCopyWithImpl(
      _$SavedImpl _value, $Res Function(_$SavedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? settings = null,
  }) {
    return _then(_$SavedImpl(
      settings: null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as PeriodReminderSettings,
    ));
  }
}

/// @nodoc

class _$SavedImpl implements _Saved {
  const _$SavedImpl({required this.settings});

  @override
  final PeriodReminderSettings settings;

  @override
  String toString() {
    return 'PeriodReminderSettingsState.saved(settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SavedImpl &&
            (identical(other.settings, settings) ||
                other.settings == settings));
  }

  @override
  int get hashCode => Object.hash(runtimeType, settings);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SavedImplCopyWith<_$SavedImpl> get copyWith =>
      __$$SavedImplCopyWithImpl<_$SavedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(PeriodReminderSettings settings) loaded,
    required TResult Function() saving,
    required TResult Function(PeriodReminderSettings settings) saved,
    required TResult Function(String message) failure,
  }) {
    return saved(settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(PeriodReminderSettings settings)? loaded,
    TResult? Function()? saving,
    TResult? Function(PeriodReminderSettings settings)? saved,
    TResult? Function(String message)? failure,
  }) {
    return saved?.call(settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(PeriodReminderSettings settings)? loaded,
    TResult Function()? saving,
    TResult Function(PeriodReminderSettings settings)? saved,
    TResult Function(String message)? failure,
    required TResult orElse(),
  }) {
    if (saved != null) {
      return saved(settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Saving value) saving,
    required TResult Function(_Saved value) saved,
    required TResult Function(_Failure value) failure,
  }) {
    return saved(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Saving value)? saving,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_Failure value)? failure,
  }) {
    return saved?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Saving value)? saving,
    TResult Function(_Saved value)? saved,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (saved != null) {
      return saved(this);
    }
    return orElse();
  }
}

abstract class _Saved implements PeriodReminderSettingsState {
  const factory _Saved({required final PeriodReminderSettings settings}) =
      _$SavedImpl;

  PeriodReminderSettings get settings;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SavedImplCopyWith<_$SavedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureImplCopyWith<$Res> {
  factory _$$FailureImplCopyWith(
          _$FailureImpl value, $Res Function(_$FailureImpl) then) =
      __$$FailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$FailureImplCopyWithImpl<$Res>
    extends _$PeriodReminderSettingsStateCopyWithImpl<$Res, _$FailureImpl>
    implements _$$FailureImplCopyWith<$Res> {
  __$$FailureImplCopyWithImpl(
      _$FailureImpl _value, $Res Function(_$FailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$FailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FailureImpl implements _Failure {
  const _$FailureImpl({required this.message});

  @override
  final String message;

  @override
  String toString() {
    return 'PeriodReminderSettingsState.failure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      __$$FailureImplCopyWithImpl<_$FailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(PeriodReminderSettings settings) loaded,
    required TResult Function() saving,
    required TResult Function(PeriodReminderSettings settings) saved,
    required TResult Function(String message) failure,
  }) {
    return failure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(PeriodReminderSettings settings)? loaded,
    TResult? Function()? saving,
    TResult? Function(PeriodReminderSettings settings)? saved,
    TResult? Function(String message)? failure,
  }) {
    return failure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(PeriodReminderSettings settings)? loaded,
    TResult Function()? saving,
    TResult Function(PeriodReminderSettings settings)? saved,
    TResult Function(String message)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Saving value) saving,
    required TResult Function(_Saved value) saved,
    required TResult Function(_Failure value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Saving value)? saving,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_Failure value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Saving value)? saving,
    TResult Function(_Saved value)? saved,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class _Failure implements PeriodReminderSettingsState {
  const factory _Failure({required final String message}) = _$FailureImpl;

  String get message;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
