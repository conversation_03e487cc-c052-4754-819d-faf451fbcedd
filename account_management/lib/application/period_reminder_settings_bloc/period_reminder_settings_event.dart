part of 'period_reminder_settings_bloc.dart';

@freezed
class PeriodReminderSettingsEvent with _$PeriodReminderSettingsEvent {
  // Load current reminder settings
  const factory PeriodReminderSettingsEvent.loadSettings() = _LoadSettings;
  
  // Toggle period reminder on/off
  const factory PeriodReminderSettingsEvent.togglePeriodReminder(bool enabled) = _TogglePeriodReminder;
  
  // Update period reminder days before
  const factory PeriodReminderSettingsEvent.updatePeriodReminderDays(int daysBefore) = _UpdatePeriodReminderDays;
  
  // Toggle ovulation reminder on/off
  const factory PeriodReminderSettingsEvent.toggleOvulationReminder(bool enabled) = _ToggleOvulationReminder;
  
  // Update ovulation reminder days before
  const factory PeriodReminderSettingsEvent.updateOvulationReminderDays(int daysBefore) = _UpdateOvulationReminderDays;
  
  // Save settings
  const factory PeriodReminderSettingsEvent.saveSettings() = _SaveSettings;
}
