part of 'period_reminder_settings_bloc.dart';

@freezed
class PeriodReminderSettingsState with _$PeriodReminderSettingsState {
  const factory PeriodReminderSettingsState.initial() = _Initial;
  
  const factory PeriodReminderSettingsState.loading() = _Loading;
  
  const factory PeriodReminderSettingsState.loaded({
    required PeriodReminderSettings settings,
  }) = _Loaded;
  
  const factory PeriodReminderSettingsState.saving() = _Saving;
  
  const factory PeriodReminderSettingsState.saved({
    required PeriodReminderSettings settings,
  }) = _Saved;
  
  const factory PeriodReminderSettingsState.failure({
    required String message,
  }) = _Failure;
}
