import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';
import '../../domain/model/symptom_model.dart';

part 'manage_period_tracking_event.dart';
part 'manage_period_tracking_state.dart';
part 'manage_period_tracking_bloc.freezed.dart';

@injectable
class ManagePeriodTrackingBloc
    extends Bloc<ManagePeriodTrackingEvent, ManagePeriodTrackingState> {
  final PeriodTrackingFacade _periodTrackingFacade;
  PeriodTrackingModel _selectedPeriodTrackingDay = PeriodTrackingModel();
  List<PeriodTrackingModel> periodTrackingList = [];
  DateTime _focusedDay = DateTime.now();
  final Set<DateTime> _selectedDays = {};

  ManagePeriodTrackingBloc(this._periodTrackingFacade)
      : super(const ManagePeriodTrackingState.initial()) {
    on<_AddPeriodTracking>(_onAddPeriodTracking);
    on<_UpdatePeriodTracking>(_onUpdatePeriodTracking);
    on<_DeletePeriodTracking>(_onDeletePeriodTracking);
    on<_SelectDay>(_onSelectDay);
    on<_ToggleSymptom>(_onToggleSymptom);
    on<_ChangePainLevel>(_onChangePainLevel);
    on<_ChangeFlowLevel>(_onChangeFlowLevel);
    on<_CalculateOvulationDays>(_onCalculateOvulationDays);
    on<_SavePeriodDatesAndRecalculate>(_onSavePeriodDatesAndRecalculate);
  }

  Future<void> _onAddPeriodTracking(
    _AddPeriodTracking event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess = await _periodTrackingFacade
        .createPeriodTrackingDetails(event.periodTracking);
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        periodTrackingList.add(event.periodTracking);
        emit(const ManagePeriodTrackingState.periodTrackingAdded());
      },
    );
  }

  Future<void> _onUpdatePeriodTracking(
    _UpdatePeriodTracking event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess = await _periodTrackingFacade
        .updatePeriodTrackingDetails(event.periodTracking);
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        final index = periodTrackingList.indexWhere(
            (element) => element.date?.day == event.periodTracking.date?.day);
        if (index != -1) periodTrackingList[index] = event.periodTracking;
        emit(const ManagePeriodTrackingState.periodTrackingUpdated());
      },
    );
  }

  Future<void> _onDeletePeriodTracking(
    _DeletePeriodTracking event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess = await _periodTrackingFacade
        .deletePeriodTrackingDetails(event.periodTracking);
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        periodTrackingList.removeWhere(
            (element) => element.date?.day == event.periodTracking.date?.day);
        emit(const ManagePeriodTrackingState.periodTrackingDeleted());
      },
    );
  }

  Future<void> _onSelectDay(
    _SelectDay event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess =
        await _periodTrackingFacade.selectDay(event.selectedDay, event.format);
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        emit(const ManagePeriodTrackingState.dataLoaded());
      },
    );
  }

  Future<void> _onToggleSymptom(
    _ToggleSymptom event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess =
        await _periodTrackingFacade.toggleSymptom(event.symptom);
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        emit(const ManagePeriodTrackingState.dataLoaded());
      },
    );
  }

  Future<void> _onChangePainLevel(
    _ChangePainLevel event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess =
        await _periodTrackingFacade.changePainLevel(event.painLevel);
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        emit(const ManagePeriodTrackingState.dataLoaded());
      },
    );
  }

  Future<void> _onChangeFlowLevel(
    _ChangeFlowLevel event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess =
        await _periodTrackingFacade.changeFlowLevel(event.flowLevel);
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        emit(const ManagePeriodTrackingState.dataLoaded());
      },
    );
  }

  Future<void> _onCalculateOvulationDays(
    _CalculateOvulationDays event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess =
        await _periodTrackingFacade.calculateOvulationDays();
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        emit(const ManagePeriodTrackingState.ovulationDaysCalculated());
      },
    );
  }

  Future<void> _onSavePeriodDatesAndRecalculate(
    _SavePeriodDatesAndRecalculate event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    final failureOrSuccess =
        await _periodTrackingFacade.savePeriodDatesAndRecalculate();
    failureOrSuccess.mapBoth(
      onLeft: (failure) =>
          emit(ManagePeriodTrackingState.periodTrackingFailure(failure)),
      onRight: (_) {
        emit(const ManagePeriodTrackingState.dataLoaded());
      },
    );
  }
}
