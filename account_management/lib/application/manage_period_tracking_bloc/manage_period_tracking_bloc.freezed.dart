// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_period_tracking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManagePeriodTrackingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManagePeriodTrackingEventCopyWith<$Res> {
  factory $ManagePeriodTrackingEventCopyWith(ManagePeriodTrackingEvent value,
          $Res Function(ManagePeriodTrackingEvent) then) =
      _$ManagePeriodTrackingEventCopyWithImpl<$Res, ManagePeriodTrackingEvent>;
}

/// @nodoc
class _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        $Val extends ManagePeriodTrackingEvent>
    implements $ManagePeriodTrackingEventCopyWith<$Res> {
  _$ManagePeriodTrackingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$AddPeriodTrackingImplCopyWith<$Res> {
  factory _$$AddPeriodTrackingImplCopyWith(_$AddPeriodTrackingImpl value,
          $Res Function(_$AddPeriodTrackingImpl) then) =
      __$$AddPeriodTrackingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodTrackingModel periodTracking});
}

/// @nodoc
class __$$AddPeriodTrackingImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$AddPeriodTrackingImpl>
    implements _$$AddPeriodTrackingImplCopyWith<$Res> {
  __$$AddPeriodTrackingImplCopyWithImpl(_$AddPeriodTrackingImpl _value,
      $Res Function(_$AddPeriodTrackingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodTracking = null,
  }) {
    return _then(_$AddPeriodTrackingImpl(
      null == periodTracking
          ? _value.periodTracking
          : periodTracking // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingModel,
    ));
  }
}

/// @nodoc

class _$AddPeriodTrackingImpl implements _AddPeriodTracking {
  const _$AddPeriodTrackingImpl(this.periodTracking);

  @override
  final PeriodTrackingModel periodTracking;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.addPeriodTracking(periodTracking: $periodTracking)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddPeriodTrackingImpl &&
            (identical(other.periodTracking, periodTracking) ||
                other.periodTracking == periodTracking));
  }

  @override
  int get hashCode => Object.hash(runtimeType, periodTracking);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddPeriodTrackingImplCopyWith<_$AddPeriodTrackingImpl> get copyWith =>
      __$$AddPeriodTrackingImplCopyWithImpl<_$AddPeriodTrackingImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return addPeriodTracking(periodTracking);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return addPeriodTracking?.call(periodTracking);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (addPeriodTracking != null) {
      return addPeriodTracking(periodTracking);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return addPeriodTracking(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return addPeriodTracking?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (addPeriodTracking != null) {
      return addPeriodTracking(this);
    }
    return orElse();
  }
}

abstract class _AddPeriodTracking implements ManagePeriodTrackingEvent {
  const factory _AddPeriodTracking(final PeriodTrackingModel periodTracking) =
      _$AddPeriodTrackingImpl;

  PeriodTrackingModel get periodTracking;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddPeriodTrackingImplCopyWith<_$AddPeriodTrackingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatePeriodTrackingImplCopyWith<$Res> {
  factory _$$UpdatePeriodTrackingImplCopyWith(_$UpdatePeriodTrackingImpl value,
          $Res Function(_$UpdatePeriodTrackingImpl) then) =
      __$$UpdatePeriodTrackingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodTrackingModel periodTracking});
}

/// @nodoc
class __$$UpdatePeriodTrackingImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$UpdatePeriodTrackingImpl>
    implements _$$UpdatePeriodTrackingImplCopyWith<$Res> {
  __$$UpdatePeriodTrackingImplCopyWithImpl(_$UpdatePeriodTrackingImpl _value,
      $Res Function(_$UpdatePeriodTrackingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodTracking = null,
  }) {
    return _then(_$UpdatePeriodTrackingImpl(
      null == periodTracking
          ? _value.periodTracking
          : periodTracking // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingModel,
    ));
  }
}

/// @nodoc

class _$UpdatePeriodTrackingImpl implements _UpdatePeriodTracking {
  const _$UpdatePeriodTrackingImpl(this.periodTracking);

  @override
  final PeriodTrackingModel periodTracking;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.updatePeriodTracking(periodTracking: $periodTracking)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatePeriodTrackingImpl &&
            (identical(other.periodTracking, periodTracking) ||
                other.periodTracking == periodTracking));
  }

  @override
  int get hashCode => Object.hash(runtimeType, periodTracking);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatePeriodTrackingImplCopyWith<_$UpdatePeriodTrackingImpl>
      get copyWith =>
          __$$UpdatePeriodTrackingImplCopyWithImpl<_$UpdatePeriodTrackingImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return updatePeriodTracking(periodTracking);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return updatePeriodTracking?.call(periodTracking);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (updatePeriodTracking != null) {
      return updatePeriodTracking(periodTracking);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return updatePeriodTracking(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return updatePeriodTracking?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (updatePeriodTracking != null) {
      return updatePeriodTracking(this);
    }
    return orElse();
  }
}

abstract class _UpdatePeriodTracking implements ManagePeriodTrackingEvent {
  const factory _UpdatePeriodTracking(
      final PeriodTrackingModel periodTracking) = _$UpdatePeriodTrackingImpl;

  PeriodTrackingModel get periodTracking;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatePeriodTrackingImplCopyWith<_$UpdatePeriodTrackingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeletePeriodTrackingImplCopyWith<$Res> {
  factory _$$DeletePeriodTrackingImplCopyWith(_$DeletePeriodTrackingImpl value,
          $Res Function(_$DeletePeriodTrackingImpl) then) =
      __$$DeletePeriodTrackingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodTrackingModel periodTracking});
}

/// @nodoc
class __$$DeletePeriodTrackingImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$DeletePeriodTrackingImpl>
    implements _$$DeletePeriodTrackingImplCopyWith<$Res> {
  __$$DeletePeriodTrackingImplCopyWithImpl(_$DeletePeriodTrackingImpl _value,
      $Res Function(_$DeletePeriodTrackingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodTracking = null,
  }) {
    return _then(_$DeletePeriodTrackingImpl(
      null == periodTracking
          ? _value.periodTracking
          : periodTracking // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingModel,
    ));
  }
}

/// @nodoc

class _$DeletePeriodTrackingImpl implements _DeletePeriodTracking {
  const _$DeletePeriodTrackingImpl(this.periodTracking);

  @override
  final PeriodTrackingModel periodTracking;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.deletePeriodTracking(periodTracking: $periodTracking)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeletePeriodTrackingImpl &&
            (identical(other.periodTracking, periodTracking) ||
                other.periodTracking == periodTracking));
  }

  @override
  int get hashCode => Object.hash(runtimeType, periodTracking);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeletePeriodTrackingImplCopyWith<_$DeletePeriodTrackingImpl>
      get copyWith =>
          __$$DeletePeriodTrackingImplCopyWithImpl<_$DeletePeriodTrackingImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return deletePeriodTracking(periodTracking);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return deletePeriodTracking?.call(periodTracking);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (deletePeriodTracking != null) {
      return deletePeriodTracking(periodTracking);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return deletePeriodTracking(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return deletePeriodTracking?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (deletePeriodTracking != null) {
      return deletePeriodTracking(this);
    }
    return orElse();
  }
}

abstract class _DeletePeriodTracking implements ManagePeriodTrackingEvent {
  const factory _DeletePeriodTracking(
      final PeriodTrackingModel periodTracking) = _$DeletePeriodTrackingImpl;

  PeriodTrackingModel get periodTracking;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeletePeriodTrackingImplCopyWith<_$DeletePeriodTrackingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectDayImplCopyWith<$Res> {
  factory _$$SelectDayImplCopyWith(
          _$SelectDayImpl value, $Res Function(_$SelectDayImpl) then) =
      __$$SelectDayImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime selectedDay, bool format});
}

/// @nodoc
class __$$SelectDayImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res, _$SelectDayImpl>
    implements _$$SelectDayImplCopyWith<$Res> {
  __$$SelectDayImplCopyWithImpl(
      _$SelectDayImpl _value, $Res Function(_$SelectDayImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedDay = null,
    Object? format = null,
  }) {
    return _then(_$SelectDayImpl(
      null == selectedDay
          ? _value.selectedDay
          : selectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime,
      null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SelectDayImpl implements _SelectDay {
  const _$SelectDayImpl(this.selectedDay, this.format);

  @override
  final DateTime selectedDay;
  @override
  final bool format;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.selectDay(selectedDay: $selectedDay, format: $format)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectDayImpl &&
            (identical(other.selectedDay, selectedDay) ||
                other.selectedDay == selectedDay) &&
            (identical(other.format, format) || other.format == format));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedDay, format);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectDayImplCopyWith<_$SelectDayImpl> get copyWith =>
      __$$SelectDayImplCopyWithImpl<_$SelectDayImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return selectDay(selectedDay, format);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return selectDay?.call(selectedDay, format);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (selectDay != null) {
      return selectDay(selectedDay, format);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return selectDay(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return selectDay?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (selectDay != null) {
      return selectDay(this);
    }
    return orElse();
  }
}

abstract class _SelectDay implements ManagePeriodTrackingEvent {
  const factory _SelectDay(final DateTime selectedDay, final bool format) =
      _$SelectDayImpl;

  DateTime get selectedDay;
  bool get format;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelectDayImplCopyWith<_$SelectDayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ToggleSymptomImplCopyWith<$Res> {
  factory _$$ToggleSymptomImplCopyWith(
          _$ToggleSymptomImpl value, $Res Function(_$ToggleSymptomImpl) then) =
      __$$ToggleSymptomImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SymptomModel symptom});
}

/// @nodoc
class __$$ToggleSymptomImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res, _$ToggleSymptomImpl>
    implements _$$ToggleSymptomImplCopyWith<$Res> {
  __$$ToggleSymptomImplCopyWithImpl(
      _$ToggleSymptomImpl _value, $Res Function(_$ToggleSymptomImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? symptom = null,
  }) {
    return _then(_$ToggleSymptomImpl(
      null == symptom
          ? _value.symptom
          : symptom // ignore: cast_nullable_to_non_nullable
              as SymptomModel,
    ));
  }
}

/// @nodoc

class _$ToggleSymptomImpl implements _ToggleSymptom {
  const _$ToggleSymptomImpl(this.symptom);

  @override
  final SymptomModel symptom;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.toggleSymptom(symptom: $symptom)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ToggleSymptomImpl &&
            (identical(other.symptom, symptom) || other.symptom == symptom));
  }

  @override
  int get hashCode => Object.hash(runtimeType, symptom);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ToggleSymptomImplCopyWith<_$ToggleSymptomImpl> get copyWith =>
      __$$ToggleSymptomImplCopyWithImpl<_$ToggleSymptomImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return toggleSymptom(symptom);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return toggleSymptom?.call(symptom);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (toggleSymptom != null) {
      return toggleSymptom(symptom);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return toggleSymptom(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return toggleSymptom?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (toggleSymptom != null) {
      return toggleSymptom(this);
    }
    return orElse();
  }
}

abstract class _ToggleSymptom implements ManagePeriodTrackingEvent {
  const factory _ToggleSymptom(final SymptomModel symptom) =
      _$ToggleSymptomImpl;

  SymptomModel get symptom;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ToggleSymptomImplCopyWith<_$ToggleSymptomImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangePainLevelImplCopyWith<$Res> {
  factory _$$ChangePainLevelImplCopyWith(_$ChangePainLevelImpl value,
          $Res Function(_$ChangePainLevelImpl) then) =
      __$$ChangePainLevelImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int painLevel});
}

/// @nodoc
class __$$ChangePainLevelImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res, _$ChangePainLevelImpl>
    implements _$$ChangePainLevelImplCopyWith<$Res> {
  __$$ChangePainLevelImplCopyWithImpl(
      _$ChangePainLevelImpl _value, $Res Function(_$ChangePainLevelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? painLevel = null,
  }) {
    return _then(_$ChangePainLevelImpl(
      null == painLevel
          ? _value.painLevel
          : painLevel // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ChangePainLevelImpl implements _ChangePainLevel {
  const _$ChangePainLevelImpl(this.painLevel);

  @override
  final int painLevel;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.changePainLevel(painLevel: $painLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePainLevelImpl &&
            (identical(other.painLevel, painLevel) ||
                other.painLevel == painLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, painLevel);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangePainLevelImplCopyWith<_$ChangePainLevelImpl> get copyWith =>
      __$$ChangePainLevelImplCopyWithImpl<_$ChangePainLevelImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return changePainLevel(painLevel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return changePainLevel?.call(painLevel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (changePainLevel != null) {
      return changePainLevel(painLevel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return changePainLevel(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return changePainLevel?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (changePainLevel != null) {
      return changePainLevel(this);
    }
    return orElse();
  }
}

abstract class _ChangePainLevel implements ManagePeriodTrackingEvent {
  const factory _ChangePainLevel(final int painLevel) = _$ChangePainLevelImpl;

  int get painLevel;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChangePainLevelImplCopyWith<_$ChangePainLevelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeFlowLevelImplCopyWith<$Res> {
  factory _$$ChangeFlowLevelImplCopyWith(_$ChangeFlowLevelImpl value,
          $Res Function(_$ChangeFlowLevelImpl) then) =
      __$$ChangeFlowLevelImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int flowLevel});
}

/// @nodoc
class __$$ChangeFlowLevelImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res, _$ChangeFlowLevelImpl>
    implements _$$ChangeFlowLevelImplCopyWith<$Res> {
  __$$ChangeFlowLevelImplCopyWithImpl(
      _$ChangeFlowLevelImpl _value, $Res Function(_$ChangeFlowLevelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flowLevel = null,
  }) {
    return _then(_$ChangeFlowLevelImpl(
      null == flowLevel
          ? _value.flowLevel
          : flowLevel // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ChangeFlowLevelImpl implements _ChangeFlowLevel {
  const _$ChangeFlowLevelImpl(this.flowLevel);

  @override
  final int flowLevel;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.changeFlowLevel(flowLevel: $flowLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeFlowLevelImpl &&
            (identical(other.flowLevel, flowLevel) ||
                other.flowLevel == flowLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, flowLevel);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeFlowLevelImplCopyWith<_$ChangeFlowLevelImpl> get copyWith =>
      __$$ChangeFlowLevelImplCopyWithImpl<_$ChangeFlowLevelImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return changeFlowLevel(flowLevel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return changeFlowLevel?.call(flowLevel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (changeFlowLevel != null) {
      return changeFlowLevel(flowLevel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return changeFlowLevel(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return changeFlowLevel?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (changeFlowLevel != null) {
      return changeFlowLevel(this);
    }
    return orElse();
  }
}

abstract class _ChangeFlowLevel implements ManagePeriodTrackingEvent {
  const factory _ChangeFlowLevel(final int flowLevel) = _$ChangeFlowLevelImpl;

  int get flowLevel;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChangeFlowLevelImplCopyWith<_$ChangeFlowLevelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CalculateOvulationDaysImplCopyWith<$Res> {
  factory _$$CalculateOvulationDaysImplCopyWith(
          _$CalculateOvulationDaysImpl value,
          $Res Function(_$CalculateOvulationDaysImpl) then) =
      __$$CalculateOvulationDaysImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CalculateOvulationDaysImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$CalculateOvulationDaysImpl>
    implements _$$CalculateOvulationDaysImplCopyWith<$Res> {
  __$$CalculateOvulationDaysImplCopyWithImpl(
      _$CalculateOvulationDaysImpl _value,
      $Res Function(_$CalculateOvulationDaysImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CalculateOvulationDaysImpl implements _CalculateOvulationDays {
  const _$CalculateOvulationDaysImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.calculateOvulationDays()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalculateOvulationDaysImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return calculateOvulationDays();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return calculateOvulationDays?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (calculateOvulationDays != null) {
      return calculateOvulationDays();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return calculateOvulationDays(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return calculateOvulationDays?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (calculateOvulationDays != null) {
      return calculateOvulationDays(this);
    }
    return orElse();
  }
}

abstract class _CalculateOvulationDays implements ManagePeriodTrackingEvent {
  const factory _CalculateOvulationDays() = _$CalculateOvulationDaysImpl;
}

/// @nodoc
abstract class _$$SavePeriodDatesAndRecalculateImplCopyWith<$Res> {
  factory _$$SavePeriodDatesAndRecalculateImplCopyWith(
          _$SavePeriodDatesAndRecalculateImpl value,
          $Res Function(_$SavePeriodDatesAndRecalculateImpl) then) =
      __$$SavePeriodDatesAndRecalculateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SavePeriodDatesAndRecalculateImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$SavePeriodDatesAndRecalculateImpl>
    implements _$$SavePeriodDatesAndRecalculateImplCopyWith<$Res> {
  __$$SavePeriodDatesAndRecalculateImplCopyWithImpl(
      _$SavePeriodDatesAndRecalculateImpl _value,
      $Res Function(_$SavePeriodDatesAndRecalculateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SavePeriodDatesAndRecalculateImpl
    implements _SavePeriodDatesAndRecalculate {
  const _$SavePeriodDatesAndRecalculateImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.savePeriodDatesAndRecalculate()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SavePeriodDatesAndRecalculateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PeriodTrackingModel periodTracking)
        addPeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        updatePeriodTracking,
    required TResult Function(PeriodTrackingModel periodTracking)
        deletePeriodTracking,
    required TResult Function(DateTime selectedDay, bool format) selectDay,
    required TResult Function(SymptomModel symptom) toggleSymptom,
    required TResult Function(int painLevel) changePainLevel,
    required TResult Function(int flowLevel) changeFlowLevel,
    required TResult Function() calculateOvulationDays,
    required TResult Function() savePeriodDatesAndRecalculate,
  }) {
    return savePeriodDatesAndRecalculate();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult? Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult? Function(DateTime selectedDay, bool format)? selectDay,
    TResult? Function(SymptomModel symptom)? toggleSymptom,
    TResult? Function(int painLevel)? changePainLevel,
    TResult? Function(int flowLevel)? changeFlowLevel,
    TResult? Function()? calculateOvulationDays,
    TResult? Function()? savePeriodDatesAndRecalculate,
  }) {
    return savePeriodDatesAndRecalculate?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PeriodTrackingModel periodTracking)? addPeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? updatePeriodTracking,
    TResult Function(PeriodTrackingModel periodTracking)? deletePeriodTracking,
    TResult Function(DateTime selectedDay, bool format)? selectDay,
    TResult Function(SymptomModel symptom)? toggleSymptom,
    TResult Function(int painLevel)? changePainLevel,
    TResult Function(int flowLevel)? changeFlowLevel,
    TResult Function()? calculateOvulationDays,
    TResult Function()? savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (savePeriodDatesAndRecalculate != null) {
      return savePeriodDatesAndRecalculate();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddPeriodTracking value) addPeriodTracking,
    required TResult Function(_UpdatePeriodTracking value) updatePeriodTracking,
    required TResult Function(_DeletePeriodTracking value) deletePeriodTracking,
    required TResult Function(_SelectDay value) selectDay,
    required TResult Function(_ToggleSymptom value) toggleSymptom,
    required TResult Function(_ChangePainLevel value) changePainLevel,
    required TResult Function(_ChangeFlowLevel value) changeFlowLevel,
    required TResult Function(_CalculateOvulationDays value)
        calculateOvulationDays,
    required TResult Function(_SavePeriodDatesAndRecalculate value)
        savePeriodDatesAndRecalculate,
  }) {
    return savePeriodDatesAndRecalculate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult? Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult? Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult? Function(_SelectDay value)? selectDay,
    TResult? Function(_ToggleSymptom value)? toggleSymptom,
    TResult? Function(_ChangePainLevel value)? changePainLevel,
    TResult? Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult? Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult? Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
  }) {
    return savePeriodDatesAndRecalculate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddPeriodTracking value)? addPeriodTracking,
    TResult Function(_UpdatePeriodTracking value)? updatePeriodTracking,
    TResult Function(_DeletePeriodTracking value)? deletePeriodTracking,
    TResult Function(_SelectDay value)? selectDay,
    TResult Function(_ToggleSymptom value)? toggleSymptom,
    TResult Function(_ChangePainLevel value)? changePainLevel,
    TResult Function(_ChangeFlowLevel value)? changeFlowLevel,
    TResult Function(_CalculateOvulationDays value)? calculateOvulationDays,
    TResult Function(_SavePeriodDatesAndRecalculate value)?
        savePeriodDatesAndRecalculate,
    required TResult orElse(),
  }) {
    if (savePeriodDatesAndRecalculate != null) {
      return savePeriodDatesAndRecalculate(this);
    }
    return orElse();
  }
}

abstract class _SavePeriodDatesAndRecalculate
    implements ManagePeriodTrackingEvent {
  const factory _SavePeriodDatesAndRecalculate() =
      _$SavePeriodDatesAndRecalculateImpl;
}

/// @nodoc
mixin _$ManagePeriodTrackingState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() periodTrackingAdded,
    required TResult Function() periodTrackingUpdated,
    required TResult Function() periodTrackingDeleted,
    required TResult Function(PeriodTrackingFailure failure)
        periodTrackingFailure,
    required TResult Function() ovulationDaysCalculated,
    required TResult Function() dataLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? periodTrackingAdded,
    TResult? Function()? periodTrackingUpdated,
    TResult? Function()? periodTrackingDeleted,
    TResult? Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult? Function()? ovulationDaysCalculated,
    TResult? Function()? dataLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? periodTrackingAdded,
    TResult Function()? periodTrackingUpdated,
    TResult Function()? periodTrackingDeleted,
    TResult Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult Function()? ovulationDaysCalculated,
    TResult Function()? dataLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_PeriodTrackingAdded value) periodTrackingAdded,
    required TResult Function(_PeriodTrackingUpdated value)
        periodTrackingUpdated,
    required TResult Function(_PeriodTrackingDeleted value)
        periodTrackingDeleted,
    required TResult Function(_PeriodTrackingFailure value)
        periodTrackingFailure,
    required TResult Function(_OvulationDaysCalculated value)
        ovulationDaysCalculated,
    required TResult Function(_DataLoaded value) dataLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult? Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult? Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult? Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult? Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult? Function(_DataLoaded value)? dataLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult Function(_DataLoaded value)? dataLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManagePeriodTrackingStateCopyWith<$Res> {
  factory $ManagePeriodTrackingStateCopyWith(ManagePeriodTrackingState value,
          $Res Function(ManagePeriodTrackingState) then) =
      _$ManagePeriodTrackingStateCopyWithImpl<$Res, ManagePeriodTrackingState>;
}

/// @nodoc
class _$ManagePeriodTrackingStateCopyWithImpl<$Res,
        $Val extends ManagePeriodTrackingState>
    implements $ManagePeriodTrackingStateCopyWith<$Res> {
  _$ManagePeriodTrackingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() periodTrackingAdded,
    required TResult Function() periodTrackingUpdated,
    required TResult Function() periodTrackingDeleted,
    required TResult Function(PeriodTrackingFailure failure)
        periodTrackingFailure,
    required TResult Function() ovulationDaysCalculated,
    required TResult Function() dataLoaded,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? periodTrackingAdded,
    TResult? Function()? periodTrackingUpdated,
    TResult? Function()? periodTrackingDeleted,
    TResult? Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult? Function()? ovulationDaysCalculated,
    TResult? Function()? dataLoaded,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? periodTrackingAdded,
    TResult Function()? periodTrackingUpdated,
    TResult Function()? periodTrackingDeleted,
    TResult Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult Function()? ovulationDaysCalculated,
    TResult Function()? dataLoaded,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_PeriodTrackingAdded value) periodTrackingAdded,
    required TResult Function(_PeriodTrackingUpdated value)
        periodTrackingUpdated,
    required TResult Function(_PeriodTrackingDeleted value)
        periodTrackingDeleted,
    required TResult Function(_PeriodTrackingFailure value)
        periodTrackingFailure,
    required TResult Function(_OvulationDaysCalculated value)
        ovulationDaysCalculated,
    required TResult Function(_DataLoaded value) dataLoaded,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult? Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult? Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult? Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult? Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult? Function(_DataLoaded value)? dataLoaded,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult Function(_DataLoaded value)? dataLoaded,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements ManagePeriodTrackingState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$PeriodTrackingAddedImplCopyWith<$Res> {
  factory _$$PeriodTrackingAddedImplCopyWith(_$PeriodTrackingAddedImpl value,
          $Res Function(_$PeriodTrackingAddedImpl) then) =
      __$$PeriodTrackingAddedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PeriodTrackingAddedImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res,
        _$PeriodTrackingAddedImpl>
    implements _$$PeriodTrackingAddedImplCopyWith<$Res> {
  __$$PeriodTrackingAddedImplCopyWithImpl(_$PeriodTrackingAddedImpl _value,
      $Res Function(_$PeriodTrackingAddedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PeriodTrackingAddedImpl implements _PeriodTrackingAdded {
  const _$PeriodTrackingAddedImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.periodTrackingAdded()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodTrackingAddedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() periodTrackingAdded,
    required TResult Function() periodTrackingUpdated,
    required TResult Function() periodTrackingDeleted,
    required TResult Function(PeriodTrackingFailure failure)
        periodTrackingFailure,
    required TResult Function() ovulationDaysCalculated,
    required TResult Function() dataLoaded,
  }) {
    return periodTrackingAdded();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? periodTrackingAdded,
    TResult? Function()? periodTrackingUpdated,
    TResult? Function()? periodTrackingDeleted,
    TResult? Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult? Function()? ovulationDaysCalculated,
    TResult? Function()? dataLoaded,
  }) {
    return periodTrackingAdded?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? periodTrackingAdded,
    TResult Function()? periodTrackingUpdated,
    TResult Function()? periodTrackingDeleted,
    TResult Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult Function()? ovulationDaysCalculated,
    TResult Function()? dataLoaded,
    required TResult orElse(),
  }) {
    if (periodTrackingAdded != null) {
      return periodTrackingAdded();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_PeriodTrackingAdded value) periodTrackingAdded,
    required TResult Function(_PeriodTrackingUpdated value)
        periodTrackingUpdated,
    required TResult Function(_PeriodTrackingDeleted value)
        periodTrackingDeleted,
    required TResult Function(_PeriodTrackingFailure value)
        periodTrackingFailure,
    required TResult Function(_OvulationDaysCalculated value)
        ovulationDaysCalculated,
    required TResult Function(_DataLoaded value) dataLoaded,
  }) {
    return periodTrackingAdded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult? Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult? Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult? Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult? Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult? Function(_DataLoaded value)? dataLoaded,
  }) {
    return periodTrackingAdded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult Function(_DataLoaded value)? dataLoaded,
    required TResult orElse(),
  }) {
    if (periodTrackingAdded != null) {
      return periodTrackingAdded(this);
    }
    return orElse();
  }
}

abstract class _PeriodTrackingAdded implements ManagePeriodTrackingState {
  const factory _PeriodTrackingAdded() = _$PeriodTrackingAddedImpl;
}

/// @nodoc
abstract class _$$PeriodTrackingUpdatedImplCopyWith<$Res> {
  factory _$$PeriodTrackingUpdatedImplCopyWith(
          _$PeriodTrackingUpdatedImpl value,
          $Res Function(_$PeriodTrackingUpdatedImpl) then) =
      __$$PeriodTrackingUpdatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PeriodTrackingUpdatedImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res,
        _$PeriodTrackingUpdatedImpl>
    implements _$$PeriodTrackingUpdatedImplCopyWith<$Res> {
  __$$PeriodTrackingUpdatedImplCopyWithImpl(_$PeriodTrackingUpdatedImpl _value,
      $Res Function(_$PeriodTrackingUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PeriodTrackingUpdatedImpl implements _PeriodTrackingUpdated {
  const _$PeriodTrackingUpdatedImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.periodTrackingUpdated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodTrackingUpdatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() periodTrackingAdded,
    required TResult Function() periodTrackingUpdated,
    required TResult Function() periodTrackingDeleted,
    required TResult Function(PeriodTrackingFailure failure)
        periodTrackingFailure,
    required TResult Function() ovulationDaysCalculated,
    required TResult Function() dataLoaded,
  }) {
    return periodTrackingUpdated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? periodTrackingAdded,
    TResult? Function()? periodTrackingUpdated,
    TResult? Function()? periodTrackingDeleted,
    TResult? Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult? Function()? ovulationDaysCalculated,
    TResult? Function()? dataLoaded,
  }) {
    return periodTrackingUpdated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? periodTrackingAdded,
    TResult Function()? periodTrackingUpdated,
    TResult Function()? periodTrackingDeleted,
    TResult Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult Function()? ovulationDaysCalculated,
    TResult Function()? dataLoaded,
    required TResult orElse(),
  }) {
    if (periodTrackingUpdated != null) {
      return periodTrackingUpdated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_PeriodTrackingAdded value) periodTrackingAdded,
    required TResult Function(_PeriodTrackingUpdated value)
        periodTrackingUpdated,
    required TResult Function(_PeriodTrackingDeleted value)
        periodTrackingDeleted,
    required TResult Function(_PeriodTrackingFailure value)
        periodTrackingFailure,
    required TResult Function(_OvulationDaysCalculated value)
        ovulationDaysCalculated,
    required TResult Function(_DataLoaded value) dataLoaded,
  }) {
    return periodTrackingUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult? Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult? Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult? Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult? Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult? Function(_DataLoaded value)? dataLoaded,
  }) {
    return periodTrackingUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult Function(_DataLoaded value)? dataLoaded,
    required TResult orElse(),
  }) {
    if (periodTrackingUpdated != null) {
      return periodTrackingUpdated(this);
    }
    return orElse();
  }
}

abstract class _PeriodTrackingUpdated implements ManagePeriodTrackingState {
  const factory _PeriodTrackingUpdated() = _$PeriodTrackingUpdatedImpl;
}

/// @nodoc
abstract class _$$PeriodTrackingDeletedImplCopyWith<$Res> {
  factory _$$PeriodTrackingDeletedImplCopyWith(
          _$PeriodTrackingDeletedImpl value,
          $Res Function(_$PeriodTrackingDeletedImpl) then) =
      __$$PeriodTrackingDeletedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PeriodTrackingDeletedImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res,
        _$PeriodTrackingDeletedImpl>
    implements _$$PeriodTrackingDeletedImplCopyWith<$Res> {
  __$$PeriodTrackingDeletedImplCopyWithImpl(_$PeriodTrackingDeletedImpl _value,
      $Res Function(_$PeriodTrackingDeletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PeriodTrackingDeletedImpl implements _PeriodTrackingDeleted {
  const _$PeriodTrackingDeletedImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.periodTrackingDeleted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodTrackingDeletedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() periodTrackingAdded,
    required TResult Function() periodTrackingUpdated,
    required TResult Function() periodTrackingDeleted,
    required TResult Function(PeriodTrackingFailure failure)
        periodTrackingFailure,
    required TResult Function() ovulationDaysCalculated,
    required TResult Function() dataLoaded,
  }) {
    return periodTrackingDeleted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? periodTrackingAdded,
    TResult? Function()? periodTrackingUpdated,
    TResult? Function()? periodTrackingDeleted,
    TResult? Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult? Function()? ovulationDaysCalculated,
    TResult? Function()? dataLoaded,
  }) {
    return periodTrackingDeleted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? periodTrackingAdded,
    TResult Function()? periodTrackingUpdated,
    TResult Function()? periodTrackingDeleted,
    TResult Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult Function()? ovulationDaysCalculated,
    TResult Function()? dataLoaded,
    required TResult orElse(),
  }) {
    if (periodTrackingDeleted != null) {
      return periodTrackingDeleted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_PeriodTrackingAdded value) periodTrackingAdded,
    required TResult Function(_PeriodTrackingUpdated value)
        periodTrackingUpdated,
    required TResult Function(_PeriodTrackingDeleted value)
        periodTrackingDeleted,
    required TResult Function(_PeriodTrackingFailure value)
        periodTrackingFailure,
    required TResult Function(_OvulationDaysCalculated value)
        ovulationDaysCalculated,
    required TResult Function(_DataLoaded value) dataLoaded,
  }) {
    return periodTrackingDeleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult? Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult? Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult? Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult? Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult? Function(_DataLoaded value)? dataLoaded,
  }) {
    return periodTrackingDeleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult Function(_DataLoaded value)? dataLoaded,
    required TResult orElse(),
  }) {
    if (periodTrackingDeleted != null) {
      return periodTrackingDeleted(this);
    }
    return orElse();
  }
}

abstract class _PeriodTrackingDeleted implements ManagePeriodTrackingState {
  const factory _PeriodTrackingDeleted() = _$PeriodTrackingDeletedImpl;
}

/// @nodoc
abstract class _$$PeriodTrackingFailureImplCopyWith<$Res> {
  factory _$$PeriodTrackingFailureImplCopyWith(
          _$PeriodTrackingFailureImpl value,
          $Res Function(_$PeriodTrackingFailureImpl) then) =
      __$$PeriodTrackingFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodTrackingFailure failure});

  $PeriodTrackingFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$PeriodTrackingFailureImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res,
        _$PeriodTrackingFailureImpl>
    implements _$$PeriodTrackingFailureImplCopyWith<$Res> {
  __$$PeriodTrackingFailureImplCopyWithImpl(_$PeriodTrackingFailureImpl _value,
      $Res Function(_$PeriodTrackingFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$PeriodTrackingFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingFailure,
    ));
  }

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PeriodTrackingFailureCopyWith<$Res> get failure {
    return $PeriodTrackingFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$PeriodTrackingFailureImpl implements _PeriodTrackingFailure {
  const _$PeriodTrackingFailureImpl(this.failure);

  @override
  final PeriodTrackingFailure failure;

  @override
  String toString() {
    return 'ManagePeriodTrackingState.periodTrackingFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodTrackingFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PeriodTrackingFailureImplCopyWith<_$PeriodTrackingFailureImpl>
      get copyWith => __$$PeriodTrackingFailureImplCopyWithImpl<
          _$PeriodTrackingFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() periodTrackingAdded,
    required TResult Function() periodTrackingUpdated,
    required TResult Function() periodTrackingDeleted,
    required TResult Function(PeriodTrackingFailure failure)
        periodTrackingFailure,
    required TResult Function() ovulationDaysCalculated,
    required TResult Function() dataLoaded,
  }) {
    return periodTrackingFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? periodTrackingAdded,
    TResult? Function()? periodTrackingUpdated,
    TResult? Function()? periodTrackingDeleted,
    TResult? Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult? Function()? ovulationDaysCalculated,
    TResult? Function()? dataLoaded,
  }) {
    return periodTrackingFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? periodTrackingAdded,
    TResult Function()? periodTrackingUpdated,
    TResult Function()? periodTrackingDeleted,
    TResult Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult Function()? ovulationDaysCalculated,
    TResult Function()? dataLoaded,
    required TResult orElse(),
  }) {
    if (periodTrackingFailure != null) {
      return periodTrackingFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_PeriodTrackingAdded value) periodTrackingAdded,
    required TResult Function(_PeriodTrackingUpdated value)
        periodTrackingUpdated,
    required TResult Function(_PeriodTrackingDeleted value)
        periodTrackingDeleted,
    required TResult Function(_PeriodTrackingFailure value)
        periodTrackingFailure,
    required TResult Function(_OvulationDaysCalculated value)
        ovulationDaysCalculated,
    required TResult Function(_DataLoaded value) dataLoaded,
  }) {
    return periodTrackingFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult? Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult? Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult? Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult? Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult? Function(_DataLoaded value)? dataLoaded,
  }) {
    return periodTrackingFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult Function(_DataLoaded value)? dataLoaded,
    required TResult orElse(),
  }) {
    if (periodTrackingFailure != null) {
      return periodTrackingFailure(this);
    }
    return orElse();
  }
}

abstract class _PeriodTrackingFailure implements ManagePeriodTrackingState {
  const factory _PeriodTrackingFailure(final PeriodTrackingFailure failure) =
      _$PeriodTrackingFailureImpl;

  PeriodTrackingFailure get failure;

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PeriodTrackingFailureImplCopyWith<_$PeriodTrackingFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OvulationDaysCalculatedImplCopyWith<$Res> {
  factory _$$OvulationDaysCalculatedImplCopyWith(
          _$OvulationDaysCalculatedImpl value,
          $Res Function(_$OvulationDaysCalculatedImpl) then) =
      __$$OvulationDaysCalculatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OvulationDaysCalculatedImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res,
        _$OvulationDaysCalculatedImpl>
    implements _$$OvulationDaysCalculatedImplCopyWith<$Res> {
  __$$OvulationDaysCalculatedImplCopyWithImpl(
      _$OvulationDaysCalculatedImpl _value,
      $Res Function(_$OvulationDaysCalculatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OvulationDaysCalculatedImpl implements _OvulationDaysCalculated {
  const _$OvulationDaysCalculatedImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.ovulationDaysCalculated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OvulationDaysCalculatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() periodTrackingAdded,
    required TResult Function() periodTrackingUpdated,
    required TResult Function() periodTrackingDeleted,
    required TResult Function(PeriodTrackingFailure failure)
        periodTrackingFailure,
    required TResult Function() ovulationDaysCalculated,
    required TResult Function() dataLoaded,
  }) {
    return ovulationDaysCalculated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? periodTrackingAdded,
    TResult? Function()? periodTrackingUpdated,
    TResult? Function()? periodTrackingDeleted,
    TResult? Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult? Function()? ovulationDaysCalculated,
    TResult? Function()? dataLoaded,
  }) {
    return ovulationDaysCalculated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? periodTrackingAdded,
    TResult Function()? periodTrackingUpdated,
    TResult Function()? periodTrackingDeleted,
    TResult Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult Function()? ovulationDaysCalculated,
    TResult Function()? dataLoaded,
    required TResult orElse(),
  }) {
    if (ovulationDaysCalculated != null) {
      return ovulationDaysCalculated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_PeriodTrackingAdded value) periodTrackingAdded,
    required TResult Function(_PeriodTrackingUpdated value)
        periodTrackingUpdated,
    required TResult Function(_PeriodTrackingDeleted value)
        periodTrackingDeleted,
    required TResult Function(_PeriodTrackingFailure value)
        periodTrackingFailure,
    required TResult Function(_OvulationDaysCalculated value)
        ovulationDaysCalculated,
    required TResult Function(_DataLoaded value) dataLoaded,
  }) {
    return ovulationDaysCalculated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult? Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult? Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult? Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult? Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult? Function(_DataLoaded value)? dataLoaded,
  }) {
    return ovulationDaysCalculated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult Function(_DataLoaded value)? dataLoaded,
    required TResult orElse(),
  }) {
    if (ovulationDaysCalculated != null) {
      return ovulationDaysCalculated(this);
    }
    return orElse();
  }
}

abstract class _OvulationDaysCalculated implements ManagePeriodTrackingState {
  const factory _OvulationDaysCalculated() = _$OvulationDaysCalculatedImpl;
}

/// @nodoc
abstract class _$$DataLoadedImplCopyWith<$Res> {
  factory _$$DataLoadedImplCopyWith(
          _$DataLoadedImpl value, $Res Function(_$DataLoadedImpl) then) =
      __$$DataLoadedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DataLoadedImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res, _$DataLoadedImpl>
    implements _$$DataLoadedImplCopyWith<$Res> {
  __$$DataLoadedImplCopyWithImpl(
      _$DataLoadedImpl _value, $Res Function(_$DataLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DataLoadedImpl implements _DataLoaded {
  const _$DataLoadedImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.dataLoaded()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DataLoadedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() periodTrackingAdded,
    required TResult Function() periodTrackingUpdated,
    required TResult Function() periodTrackingDeleted,
    required TResult Function(PeriodTrackingFailure failure)
        periodTrackingFailure,
    required TResult Function() ovulationDaysCalculated,
    required TResult Function() dataLoaded,
  }) {
    return dataLoaded();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? periodTrackingAdded,
    TResult? Function()? periodTrackingUpdated,
    TResult? Function()? periodTrackingDeleted,
    TResult? Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult? Function()? ovulationDaysCalculated,
    TResult? Function()? dataLoaded,
  }) {
    return dataLoaded?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? periodTrackingAdded,
    TResult Function()? periodTrackingUpdated,
    TResult Function()? periodTrackingDeleted,
    TResult Function(PeriodTrackingFailure failure)? periodTrackingFailure,
    TResult Function()? ovulationDaysCalculated,
    TResult Function()? dataLoaded,
    required TResult orElse(),
  }) {
    if (dataLoaded != null) {
      return dataLoaded();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_PeriodTrackingAdded value) periodTrackingAdded,
    required TResult Function(_PeriodTrackingUpdated value)
        periodTrackingUpdated,
    required TResult Function(_PeriodTrackingDeleted value)
        periodTrackingDeleted,
    required TResult Function(_PeriodTrackingFailure value)
        periodTrackingFailure,
    required TResult Function(_OvulationDaysCalculated value)
        ovulationDaysCalculated,
    required TResult Function(_DataLoaded value) dataLoaded,
  }) {
    return dataLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult? Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult? Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult? Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult? Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult? Function(_DataLoaded value)? dataLoaded,
  }) {
    return dataLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_PeriodTrackingAdded value)? periodTrackingAdded,
    TResult Function(_PeriodTrackingUpdated value)? periodTrackingUpdated,
    TResult Function(_PeriodTrackingDeleted value)? periodTrackingDeleted,
    TResult Function(_PeriodTrackingFailure value)? periodTrackingFailure,
    TResult Function(_OvulationDaysCalculated value)? ovulationDaysCalculated,
    TResult Function(_DataLoaded value)? dataLoaded,
    required TResult orElse(),
  }) {
    if (dataLoaded != null) {
      return dataLoaded(this);
    }
    return orElse();
  }
}

abstract class _DataLoaded implements ManagePeriodTrackingState {
  const factory _DataLoaded() = _$DataLoadedImpl;
}
