// Event
part of 'manage_period_tracking_bloc.dart';

@freezed
class ManagePeriodTrackingEvent with _$ManagePeriodTrackingEvent {
  const factory ManagePeriodTrackingEvent.addPeriodTracking(
      PeriodTrackingModel periodTracking) = _AddPeriodTracking;
  const factory ManagePeriodTrackingEvent.updatePeriodTracking(
      PeriodTrackingModel periodTracking) = _UpdatePeriodTracking;
  const factory ManagePeriodTrackingEvent.deletePeriodTracking(
      PeriodTrackingModel periodTracking) = _DeletePeriodTracking;
  const factory ManagePeriodTrackingEvent.selectDay(
      DateTime selectedDay, bool format) = _SelectDay;
  const factory ManagePeriodTrackingEvent.toggleSymptom(SymptomModel symptom) =
      _ToggleSymptom;
  const factory ManagePeriodTrackingEvent.changePainLevel(int painLevel) =
      _ChangePainLevel;
  const factory ManagePeriodTrackingEvent.changeFlowLevel(int flowLevel) =
      _ChangeFlowLevel;
  const factory ManagePeriodTrackingEvent.calculateOvulationDays() =
      _CalculateOvulationDays;
  const factory ManagePeriodTrackingEvent.savePeriodDatesAndRecalculate() =
      _SavePeriodDatesAndRecalculate;
}
