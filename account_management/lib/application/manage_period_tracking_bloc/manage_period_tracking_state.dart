// State
part of 'manage_period_tracking_bloc.dart';

@freezed
class ManagePeriodTrackingState with _$ManagePeriodTrackingState {
  const factory ManagePeriodTrackingState.initial() = _Initial;
  const factory ManagePeriodTrackingState.periodTrackingAdded() = _PeriodTrackingAdded;
  const factory ManagePeriodTrackingState.periodTrackingUpdated() = _PeriodTrackingUpdated;
  const factory ManagePeriodTrackingState.periodTrackingDeleted() = _PeriodTrackingDeleted;
  const factory ManagePeriodTrackingState.periodTrackingFailure(PeriodTrackingFailure failure) = _PeriodTrackingFailure;
  const factory ManagePeriodTrackingState.ovulationDaysCalculated() = _OvulationDaysCalculated;
  const factory ManagePeriodTrackingState.dataLoaded() = _DataLoaded;
}