part of 'medication_form_bloc.dart';

@freezed
class MedicationFormState with _$MedicationFormState {
  const factory MedicationFormState({
    required MedicationModel medication,
    required bool showErrorMessages,
    required bool isEditing,
    required bool isSaving,
    required Option<Either<MedicationFailure, Unit>> saveFailureOrSuccessOption,
  }) = _MedicationFormState;

  factory MedicationFormState.initial() => MedicationFormState(
        medication: MedicationModel.empty(),
        showErrorMessages: false,
        isEditing: false,
        isSaving: false,
        saveFailureOrSuccessOption: None(),
      );

}
