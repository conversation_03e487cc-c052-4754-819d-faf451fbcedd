import 'package:account_management/application/manage_period_tracking_bloc/manage_period_tracking_bloc.dart';
import 'package:account_management/domain/facade/period_tracking_facade.dart';
import 'package:account_management/domain/failure/period_tracking_failure.dart';
import 'package:account_management/domain/model/period_tracking_model.dart';
import 'package:account_management/domain/model/symptom_model.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:fpdart/fpdart.dart';


class MockPeriodTrackingFacade extends Mock implements PeriodTrackingFacade {}
class FakePeriodTrackingModel extends Fake implements PeriodTrackingModel {}
class FakeSymptomModel extends Fake implements SymptomModel {}

void main() {
  late MockPeriodTrackingFacade mockPeriodTrackingFacade;
  late ManagePeriodTrackingBloc managePeriodTrackingBloc;

  setUpAll(() {
    registerFallbackValue(FakePeriodTrackingModel());
    registerFallbackValue(FakeSymptomModel());
  });

  setUp(() {
    mockPeriodTrackingFacade = MockPeriodTrackingFacade();
    managePeriodTrackingBloc = ManagePeriodTrackingBloc(mockPeriodTrackingFacade);
  });

  tearDown(() {
    managePeriodTrackingBloc.close();
  });

  group('ManagePeriodTrackingBloc', () {
    final periodTracking = PeriodTrackingModel(
      date: DateTime.now(),
      // Add other necessary fields
    );

    const failure = PeriodTrackingFailure.unexpected();

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingAdded] when _AddPeriodTracking is added',
      build: () {
        when(() => mockPeriodTrackingFacade.createPeriodTrackingDetails(any()))
            .thenAnswer((_) async => const Right(unit));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.addPeriodTracking(periodTracking)),
      expect: () => [
        const ManagePeriodTrackingState.periodTrackingAdded(),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingFailure] when _AddPeriodTracking fails',
      build: () {
        when(() => mockPeriodTrackingFacade.createPeriodTrackingDetails(any()))
            .thenAnswer((_) async => const Left(failure));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.addPeriodTracking(periodTracking)),
      expect: () => [
        ManagePeriodTrackingState.periodTrackingFailure(failure),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingUpdated] when _UpdatePeriodTracking is added',
      build: () {
        when(() => mockPeriodTrackingFacade.updatePeriodTrackingDetails(any()))
            .thenAnswer((_) async => const Right(unit));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.updatePeriodTracking(periodTracking)),
      expect: () => [
        const ManagePeriodTrackingState.periodTrackingUpdated(),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingFailure] when _UpdatePeriodTracking fails',
      build: () {
        when(() => mockPeriodTrackingFacade.updatePeriodTrackingDetails(any()))
            .thenAnswer((_) async => const Left(failure));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.updatePeriodTracking(periodTracking)),
      expect: () => [
        ManagePeriodTrackingState.periodTrackingFailure(failure),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingDeleted] when _DeletePeriodTracking is added',
      build: () {
        when(() => mockPeriodTrackingFacade.deletePeriodTrackingDetails(any()))
            .thenAnswer((_) async => const Right(unit));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.deletePeriodTracking(periodTracking)),
      expect: () => [
        const ManagePeriodTrackingState.periodTrackingDeleted(),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingFailure] when _DeletePeriodTracking fails',
      build: () {
        when(() => mockPeriodTrackingFacade.deletePeriodTrackingDetails(any()))
            .thenAnswer((_) async => const Left(failure));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.deletePeriodTracking(periodTracking)),
      expect: () => [
        ManagePeriodTrackingState.periodTrackingFailure(failure),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [dataLoaded] when _SelectDay is added',
      build: () {
        when(() => mockPeriodTrackingFacade.selectDay(any(), any()))
            .thenAnswer((_) async => const Right(unit));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.selectDay(DateTime.now(), true)),
      expect: () => [
        const ManagePeriodTrackingState.dataLoaded(),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingFailure] when _SelectDay fails',
      build: () {
        when(() => mockPeriodTrackingFacade.selectDay(any(), any()))
            .thenAnswer((_) async => const Left(failure));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.selectDay(DateTime.now(), true)),
      expect: () => [
        ManagePeriodTrackingState.periodTrackingFailure(failure),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [dataLoaded] when _ToggleSymptom is added',
      build: () {
        when(() => mockPeriodTrackingFacade.toggleSymptom(any()))
            .thenAnswer((_) async => const Right(unit));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.toggleSymptom(SymptomModel(name: 'Headache'))),
      expect: () => [
        const ManagePeriodTrackingState.dataLoaded(),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingFailure] when _ToggleSymptom fails',
      build: () {
        when(() => mockPeriodTrackingFacade.toggleSymptom(any()))
            .thenAnswer((_) async => const Left(failure));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.toggleSymptom(SymptomModel(name: 'Headache'))),
      expect: () => [
        ManagePeriodTrackingState.periodTrackingFailure(failure),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [dataLoaded] when _ChangePainLevel is added',
      build: () {
        when(() => mockPeriodTrackingFacade.changePainLevel(any()))
            .thenAnswer((_) async => const Right(unit));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.changePainLevel(5)),
      expect: () => [
        const ManagePeriodTrackingState.dataLoaded(),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingFailure] when _ChangePainLevel fails',
      build: () {
        when(() => mockPeriodTrackingFacade.changePainLevel(any()))
            .thenAnswer((_) async => const Left(failure));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.changePainLevel(5)),
      expect: () => [
        ManagePeriodTrackingState.periodTrackingFailure(failure),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [dataLoaded] when _ChangeFlowLevel is added',
      build: () {
        when(() => mockPeriodTrackingFacade.changeFlowLevel(any()))
            .thenAnswer((_) async => const Right(unit));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.changeFlowLevel(3)),
      expect: () => [
        const ManagePeriodTrackingState.dataLoaded(),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingFailure] when _ChangeFlowLevel fails',
      build: () {
        when(() => mockPeriodTrackingFacade.changeFlowLevel(any()))
            .thenAnswer((_) async => const Left(failure));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(ManagePeriodTrackingEvent.changeFlowLevel(3)),
      expect: () => [
        ManagePeriodTrackingState.periodTrackingFailure(failure),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [ovulationDaysCalculated] when _CalculateOvulationDays is added',
      build: () {
        when(() => mockPeriodTrackingFacade.calculateOvulationDays())
            .thenAnswer((_) async => const Right(unit));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(const ManagePeriodTrackingEvent.calculateOvulationDays()),
      expect: () => [
        const ManagePeriodTrackingState.ovulationDaysCalculated(),
      ],
    );

    blocTest<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      'emits [periodTrackingFailure] when _CalculateOvulationDays fails',
      build: () {
        when(() => mockPeriodTrackingFacade.calculateOvulationDays())
            .thenAnswer((_) async => const Left(failure));
        return managePeriodTrackingBloc;
      },
      act: (bloc) => bloc.add(const ManagePeriodTrackingEvent.calculateOvulationDays()),
      expect: () => [
        ManagePeriodTrackingState.periodTrackingFailure(failure),
      ],
    );
  });
}