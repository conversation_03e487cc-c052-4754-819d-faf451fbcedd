import 'package:account_management/application/update_health_data_bloc/update_health_data_bloc.dart';
import 'package:account_management/domain/facade/health_data_facade.dart';
import 'package:account_management/domain/failure/health_data_failure.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:fpdart/fpdart.dart';


class MockHealthDataFacade extends Mock implements HealthDataFacade {}

void main() {
  late MockHealthDataFacade mockHealthDataFacade;
  late UpdateHealthDataBloc updateHealthDataBloc;

  setUpAll(() {
    registerFallbackValue(UpdateCycleLength(0));
    registerFallbackValue(UpdatePeriodLength(0));
    registerFallbackValue(UpdateOvulationDate(DateTime.now()));
  });

  setUp(() {
    mockHealthDataFacade = MockHealthDataFacade();
    updateHealthDataBloc = UpdateHealthDataBloc(mockHealthDataFacade);
  });

  tearDown(() {
    updateHealthDataBloc.close();
  });

  group('UpdateHealthDataBloc', () {
    const failure = HealthDataFailure.unexpected();

    blocTest<UpdateHealthDataBloc, UpdateHealthDataState>(
      'emits [loading, updatedHealthData] when UpdateCycleLength is successful',
      build: () {
        when(() => mockHealthDataFacade.updateCycleLength(any()))
            .thenAnswer((_) async => const Right(unit));
        return updateHealthDataBloc;
      },
      act: (bloc) => bloc.add(const UpdateCycleLength(28)),
      expect: () => [
        const UpdateHealthDataState.loading(),
        const UpdateHealthDataState.updatedHealthData('cycleLength'),
      ],
    );

    blocTest<UpdateHealthDataBloc, UpdateHealthDataState>(
      'emits [loading, failure] when UpdateCycleLength fails',
      build: () {
        when(() => mockHealthDataFacade.updateCycleLength(any()))
            .thenAnswer((_) async => const Left(failure));
        return updateHealthDataBloc;
      },
      act: (bloc) => bloc.add(const UpdateCycleLength(28)),
      expect: () => [
        const UpdateHealthDataState.loading(),
        UpdateHealthDataState.failure(failure),
      ],
    );

    blocTest<UpdateHealthDataBloc, UpdateHealthDataState>(
      'emits [loading, updatedHealthData] when UpdatePeriodLength is successful',
      build: () {
        when(() => mockHealthDataFacade.updatePeriodLength(any()))
            .thenAnswer((_) async => const Right(unit));
        return updateHealthDataBloc;
      },
      act: (bloc) => bloc.add(const UpdatePeriodLength(5)),
      expect: () => [
        const UpdateHealthDataState.loading(),
        const UpdateHealthDataState.updatedHealthData('periodLength'),
      ],
    );

    blocTest<UpdateHealthDataBloc, UpdateHealthDataState>(
      'emits [loading, failure] when UpdatePeriodLength fails',
      build: () {
        when(() => mockHealthDataFacade.updatePeriodLength(any()))
            .thenAnswer((_) async => const Left(failure));
        return updateHealthDataBloc;
      },
      act: (bloc) => bloc.add(const UpdatePeriodLength(5)),
      expect: () => [
        const UpdateHealthDataState.loading(),
        UpdateHealthDataState.failure(failure),
      ],
    );

    blocTest<UpdateHealthDataBloc, UpdateHealthDataState>(
      'emits [loading, updatedHealthData] when UpdateOvulationDate is successful',
      build: () {
        when(() => mockHealthDataFacade.updateOvulationDate(any()))
            .thenAnswer((_) async => const Right(unit));
        return updateHealthDataBloc;
      },
      act: (bloc) => bloc.add(UpdateOvulationDate(DateTime(2023, 12, 25))),
      expect: () => [
        const UpdateHealthDataState.loading(),
        const UpdateHealthDataState.updatedHealthData('ovulationDate'),
      ],
    );

    blocTest<UpdateHealthDataBloc, UpdateHealthDataState>(
      'emits [loading, failure] when UpdateOvulationDate fails',
      build: () {
        when(() => mockHealthDataFacade.updateOvulationDate(any()))
            .thenAnswer((_) async => const Left(failure));
        return updateHealthDataBloc;
      },
      act: (bloc) => bloc.add(UpdateOvulationDate(DateTime(2023, 12, 25))),
      expect: () => [
        const UpdateHealthDataState.loading(),
        UpdateHealthDataState.failure(failure),
      ],
    );
  });
}