// import 'package:account_management/domain/model/period_tracking_model.dart';
// import 'package:bloc_test/bloc_test.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:fpdart/fpdart.dart';
// import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
// import 'package:account_management/domain/facade/period_tracking_facade.dart';
// import 'package:account_management/domain/failure/period_tracking_failure.dart';
// import 'package:account_management/repository/period_tracking_data_model.dart';
// import 'package:fake_async/fake_async.dart';
//
// class MockPeriodTrackingFacade extends Mock implements PeriodTrackingFacade {}
// class FakePeriodTrackingWatcherEvent extends Fake implements PeriodTrackingWatcherEvent {}
// class FakePeriodTrackingWatcherState extends Fake implements PeriodTrackingWatcherState {}
//
// void main() {
//   late MockPeriodTrackingFacade mockPeriodTrackingFacade;
//   late PeriodTrackingWatcherBloc periodTrackingWatcherBloc;
//
//   setUpAll(() {
//     registerFallbackValue(FakePeriodTrackingWatcherEvent());
//     registerFallbackValue(FakePeriodTrackingWatcherState());
//   });
//
//   setUp(() {
//     mockPeriodTrackingFacade = MockPeriodTrackingFacade();
//     periodTrackingWatcherBloc = PeriodTrackingWatcherBloc(mockPeriodTrackingFacade);
//   });
//
//   tearDown(() {
//     periodTrackingWatcherBloc.close();
//   });
//
//   group('PeriodTrackingWatcherBloc', () {
//     final fixedDateTime = DateTime(2025, 1, 7, 15, 4, 44);
//     final periodTrackingData = PeriodTrackingData(
//       periodTrackingDetails: [PeriodTrackingModel()],
//       focusedDay: fixedDateTime,
//       selectedDays: {fixedDateTime},
//       ovulationDays: {fixedDateTime},
//       selectedPeriodTrackingDay: PeriodTrackingModel(),
//     );
//
//     const failure = PeriodTrackingFailure.unexpected();
//
//     blocTest<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
//       'emits [initial, dataLoaded] when _DataReceived is successful',
//       build: () => periodTrackingWatcherBloc,
//       act: (bloc) {
//         fakeAsync((async) {
//           when(() => mockPeriodTrackingFacade.getPeriodTrackingDetails())
//               .thenAnswer((_) async => Right(unit));
//           bloc.add(PeriodTrackingWatcherEvent.dataReceived(Right(periodTrackingData)));
//           async.elapse(Duration(seconds: 1));
//         });
//       },
//       expect: () => [
//         PeriodTrackingWatcherState.initial(),
//         PeriodTrackingWatcherState(
//           periodTrackingDetails: [PeriodTrackingModel()],
//           focusedDay: fixedDateTime,
//           selectedDays: {fixedDateTime},
//           ovulationDays: {fixedDateTime},
//           selectedPeriodTrackingDay: PeriodTrackingModel(),
//           updateCounter: 1,
//         ),
//       ],
//     );
//
//     blocTest<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
//       'emits [initial] when _DataReceived fails',
//       build: () => periodTrackingWatcherBloc,
//       act: (bloc) {
//         fakeAsync((async) {
//           when(() => mockPeriodTrackingFacade.getPeriodTrackingDetails())
//               .thenAnswer((_) async => const Left(failure));
//           bloc.add(PeriodTrackingWatcherEvent.dataReceived(const Left(failure)));
//           async.elapse(Duration(seconds: 1));
//         });
//       },
//       expect: () => [
//         PeriodTrackingWatcherState.initial(),
//       ],
//     );
//   });
// }