import 'package:account_management/application/medication_form_bloc/medication_form_bloc.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fpdart/fpdart.dart';
import 'package:mocktail/mocktail.dart';

import 'package:account_management/domain/facade/medication_facade.dart';
import 'package:account_management/domain/model/medication_model.dart';
import 'package:account_management/domain/failure/medication_failure.dart';

class MockMedicationFacade extends Mock implements MedicationFacade {}

void main() {
  late MedicationFormBloc bloc;
  late MockMedicationFacade mockFacade;

  setUpAll(() {
    registerFallbackValue(MedicationModel.empty());
  });

  setUp(() {
    mockFacade = MockMedicationFacade();
    bloc = MedicationFormBloc(mockFacade);
  });

  final testMedication = MedicationModel(
      id: '1',
      name: 'Test Med',
      dosage: '10',
      frequency: '1',
      frequencyUnit: 'daily',
      dosageUnit: 'mg',
      daystoBeNotified: ['Monday'],
      timeofDay: ['09:00'],
      monthlyDateToBeNotified: DateTime(2025, 1, 1),
      isNotificationEnabled: true,
      notes: 'Test notes'
  );

  group('MedicationFormBloc', () {
    blocTest<MedicationFormBloc, MedicationFormState>(
      'emits failure when updating medication fails',
      build: () {
        when(() => mockFacade.update(any()))
            .thenAnswer((_) async => Left(const MedicationFailure.unexpected()));
        return bloc;
      },
      seed: () => MedicationFormState.initial().copyWith(
        medication: testMedication,
        isEditing: true,
      ),
      act: (bloc) => bloc.add(const MedicationFormEvent.save()),
      expect: () => [
        isA<MedicationFormState>()
            .having((s) => s.medication, 'medication', testMedication)
            .having((s) => s.isEditing, 'isEditing', true)
            .having((s) => s.isSaving, 'isSaving', true)
            .having((s) => s.saveFailureOrSuccessOption, 'saveFailureOrSuccessOption', const None()),
        isA<MedicationFormState>()
            .having((s) => s.medication, 'medication', testMedication)
            .having((s) => s.isEditing, 'isEditing', true)
            .having((s) => s.isSaving, 'isSaving', false)
            .having((s) => s.showErrorMessages, 'showErrorMessages', true)
            .having((s) => s.saveFailureOrSuccessOption, 'saveFailureOrSuccessOption',
            Some(Left(const MedicationFailure.unexpected()))),
      ],
      verify: (_) {
        verify(() => mockFacade.update(any())).called(1);
      },
    );
  });
}