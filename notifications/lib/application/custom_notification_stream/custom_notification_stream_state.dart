part of 'custom_notification_stream_bloc.dart';

@freezed
class CustomNotificationStreamState with _$CustomNotificationStreamState {

  const factory CustomNotificationStreamState.initial() = Initial;
  const factory CustomNotificationStreamState.loadingNotification() = LoadingNotification;
  const factory CustomNotificationStreamState.successNotification(NotificationModel? notification) = SuccessNotification;
  const factory CustomNotificationStreamState.failureNotification(NotificationFailure failure) = FailureNotification;

}