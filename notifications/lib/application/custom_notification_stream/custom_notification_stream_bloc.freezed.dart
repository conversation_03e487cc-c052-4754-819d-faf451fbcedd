// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'custom_notification_stream_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CustomNotificationStreamEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadCustomNotificationStream,
    required TResult Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)
        CustomNotificationStreamLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadCustomNotificationStream,
    TResult? Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)?
        CustomNotificationStreamLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadCustomNotificationStream,
    TResult Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)?
        CustomNotificationStreamLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCustomNotificationStream value)
        loadCustomNotificationStream,
    required TResult Function(CustomNotificationStreamLoaded value)
        CustomNotificationStreamLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCustomNotificationStream value)?
        loadCustomNotificationStream,
    TResult? Function(CustomNotificationStreamLoaded value)?
        CustomNotificationStreamLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCustomNotificationStream value)?
        loadCustomNotificationStream,
    TResult Function(CustomNotificationStreamLoaded value)?
        CustomNotificationStreamLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomNotificationStreamEventCopyWith<$Res> {
  factory $CustomNotificationStreamEventCopyWith(
          CustomNotificationStreamEvent value,
          $Res Function(CustomNotificationStreamEvent) then) =
      _$CustomNotificationStreamEventCopyWithImpl<$Res,
          CustomNotificationStreamEvent>;
}

/// @nodoc
class _$CustomNotificationStreamEventCopyWithImpl<$Res,
        $Val extends CustomNotificationStreamEvent>
    implements $CustomNotificationStreamEventCopyWith<$Res> {
  _$CustomNotificationStreamEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomNotificationStreamEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadCustomNotificationStreamImplCopyWith<$Res> {
  factory _$$LoadCustomNotificationStreamImplCopyWith(
          _$LoadCustomNotificationStreamImpl value,
          $Res Function(_$LoadCustomNotificationStreamImpl) then) =
      __$$LoadCustomNotificationStreamImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadCustomNotificationStreamImplCopyWithImpl<$Res>
    extends _$CustomNotificationStreamEventCopyWithImpl<$Res,
        _$LoadCustomNotificationStreamImpl>
    implements _$$LoadCustomNotificationStreamImplCopyWith<$Res> {
  __$$LoadCustomNotificationStreamImplCopyWithImpl(
      _$LoadCustomNotificationStreamImpl _value,
      $Res Function(_$LoadCustomNotificationStreamImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomNotificationStreamEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadCustomNotificationStreamImpl
    implements LoadCustomNotificationStream {
  const _$LoadCustomNotificationStreamImpl();

  @override
  String toString() {
    return 'CustomNotificationStreamEvent.loadCustomNotificationStream()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadCustomNotificationStreamImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadCustomNotificationStream,
    required TResult Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)
        CustomNotificationStreamLoaded,
  }) {
    return loadCustomNotificationStream();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadCustomNotificationStream,
    TResult? Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)?
        CustomNotificationStreamLoaded,
  }) {
    return loadCustomNotificationStream?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadCustomNotificationStream,
    TResult Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)?
        CustomNotificationStreamLoaded,
    required TResult orElse(),
  }) {
    if (loadCustomNotificationStream != null) {
      return loadCustomNotificationStream();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCustomNotificationStream value)
        loadCustomNotificationStream,
    required TResult Function(CustomNotificationStreamLoaded value)
        CustomNotificationStreamLoaded,
  }) {
    return loadCustomNotificationStream(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCustomNotificationStream value)?
        loadCustomNotificationStream,
    TResult? Function(CustomNotificationStreamLoaded value)?
        CustomNotificationStreamLoaded,
  }) {
    return loadCustomNotificationStream?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCustomNotificationStream value)?
        loadCustomNotificationStream,
    TResult Function(CustomNotificationStreamLoaded value)?
        CustomNotificationStreamLoaded,
    required TResult orElse(),
  }) {
    if (loadCustomNotificationStream != null) {
      return loadCustomNotificationStream(this);
    }
    return orElse();
  }
}

abstract class LoadCustomNotificationStream
    implements CustomNotificationStreamEvent {
  const factory LoadCustomNotificationStream() =
      _$LoadCustomNotificationStreamImpl;
}

/// @nodoc
abstract class _$$CustomNotificationStreamLoadedImplCopyWith<$Res> {
  factory _$$CustomNotificationStreamLoadedImplCopyWith(
          _$CustomNotificationStreamLoadedImpl value,
          $Res Function(_$CustomNotificationStreamLoadedImpl) then) =
      __$$CustomNotificationStreamLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Either<NotificationFailure, NotificationModel?> failureOrNotification});
}

/// @nodoc
class __$$CustomNotificationStreamLoadedImplCopyWithImpl<$Res>
    extends _$CustomNotificationStreamEventCopyWithImpl<$Res,
        _$CustomNotificationStreamLoadedImpl>
    implements _$$CustomNotificationStreamLoadedImplCopyWith<$Res> {
  __$$CustomNotificationStreamLoadedImplCopyWithImpl(
      _$CustomNotificationStreamLoadedImpl _value,
      $Res Function(_$CustomNotificationStreamLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomNotificationStreamEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrNotification = null,
  }) {
    return _then(_$CustomNotificationStreamLoadedImpl(
      null == failureOrNotification
          ? _value.failureOrNotification
          : failureOrNotification // ignore: cast_nullable_to_non_nullable
              as Either<NotificationFailure, NotificationModel?>,
    ));
  }
}

/// @nodoc

class _$CustomNotificationStreamLoadedImpl
    implements CustomNotificationStreamLoaded {
  const _$CustomNotificationStreamLoadedImpl(this.failureOrNotification);

  @override
  final Either<NotificationFailure, NotificationModel?> failureOrNotification;

  @override
  String toString() {
    return 'CustomNotificationStreamEvent.CustomNotificationStreamLoaded(failureOrNotification: $failureOrNotification)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomNotificationStreamLoadedImpl &&
            (identical(other.failureOrNotification, failureOrNotification) ||
                other.failureOrNotification == failureOrNotification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrNotification);

  /// Create a copy of CustomNotificationStreamEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomNotificationStreamLoadedImplCopyWith<
          _$CustomNotificationStreamLoadedImpl>
      get copyWith => __$$CustomNotificationStreamLoadedImplCopyWithImpl<
          _$CustomNotificationStreamLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadCustomNotificationStream,
    required TResult Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)
        CustomNotificationStreamLoaded,
  }) {
    return CustomNotificationStreamLoaded(failureOrNotification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadCustomNotificationStream,
    TResult? Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)?
        CustomNotificationStreamLoaded,
  }) {
    return CustomNotificationStreamLoaded?.call(failureOrNotification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadCustomNotificationStream,
    TResult Function(
            Either<NotificationFailure, NotificationModel?>
                failureOrNotification)?
        CustomNotificationStreamLoaded,
    required TResult orElse(),
  }) {
    if (CustomNotificationStreamLoaded != null) {
      return CustomNotificationStreamLoaded(failureOrNotification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCustomNotificationStream value)
        loadCustomNotificationStream,
    required TResult Function(CustomNotificationStreamLoaded value)
        CustomNotificationStreamLoaded,
  }) {
    return CustomNotificationStreamLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCustomNotificationStream value)?
        loadCustomNotificationStream,
    TResult? Function(CustomNotificationStreamLoaded value)?
        CustomNotificationStreamLoaded,
  }) {
    return CustomNotificationStreamLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCustomNotificationStream value)?
        loadCustomNotificationStream,
    TResult Function(CustomNotificationStreamLoaded value)?
        CustomNotificationStreamLoaded,
    required TResult orElse(),
  }) {
    if (CustomNotificationStreamLoaded != null) {
      return CustomNotificationStreamLoaded(this);
    }
    return orElse();
  }
}

abstract class CustomNotificationStreamLoaded
    implements CustomNotificationStreamEvent {
  const factory CustomNotificationStreamLoaded(
      final Either<NotificationFailure, NotificationModel?>
          failureOrNotification) = _$CustomNotificationStreamLoadedImpl;

  Either<NotificationFailure, NotificationModel?> get failureOrNotification;

  /// Create a copy of CustomNotificationStreamEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomNotificationStreamLoadedImplCopyWith<
          _$CustomNotificationStreamLoadedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CustomNotificationStreamState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingNotification,
    required TResult Function(NotificationModel? notification)
        successNotification,
    required TResult Function(NotificationFailure failure) failureNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingNotification,
    TResult? Function(NotificationModel? notification)? successNotification,
    TResult? Function(NotificationFailure failure)? failureNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingNotification,
    TResult Function(NotificationModel? notification)? successNotification,
    TResult Function(NotificationFailure failure)? failureNotification,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingNotification value) loadingNotification,
    required TResult Function(SuccessNotification value) successNotification,
    required TResult Function(FailureNotification value) failureNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingNotification value)? loadingNotification,
    TResult? Function(SuccessNotification value)? successNotification,
    TResult? Function(FailureNotification value)? failureNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingNotification value)? loadingNotification,
    TResult Function(SuccessNotification value)? successNotification,
    TResult Function(FailureNotification value)? failureNotification,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomNotificationStreamStateCopyWith<$Res> {
  factory $CustomNotificationStreamStateCopyWith(
          CustomNotificationStreamState value,
          $Res Function(CustomNotificationStreamState) then) =
      _$CustomNotificationStreamStateCopyWithImpl<$Res,
          CustomNotificationStreamState>;
}

/// @nodoc
class _$CustomNotificationStreamStateCopyWithImpl<$Res,
        $Val extends CustomNotificationStreamState>
    implements $CustomNotificationStreamStateCopyWith<$Res> {
  _$CustomNotificationStreamStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$CustomNotificationStreamStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'CustomNotificationStreamState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingNotification,
    required TResult Function(NotificationModel? notification)
        successNotification,
    required TResult Function(NotificationFailure failure) failureNotification,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingNotification,
    TResult? Function(NotificationModel? notification)? successNotification,
    TResult? Function(NotificationFailure failure)? failureNotification,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingNotification,
    TResult Function(NotificationModel? notification)? successNotification,
    TResult Function(NotificationFailure failure)? failureNotification,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingNotification value) loadingNotification,
    required TResult Function(SuccessNotification value) successNotification,
    required TResult Function(FailureNotification value) failureNotification,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingNotification value)? loadingNotification,
    TResult? Function(SuccessNotification value)? successNotification,
    TResult? Function(FailureNotification value)? failureNotification,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingNotification value)? loadingNotification,
    TResult Function(SuccessNotification value)? successNotification,
    TResult Function(FailureNotification value)? failureNotification,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements CustomNotificationStreamState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingNotificationImplCopyWith<$Res> {
  factory _$$LoadingNotificationImplCopyWith(_$LoadingNotificationImpl value,
          $Res Function(_$LoadingNotificationImpl) then) =
      __$$LoadingNotificationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingNotificationImplCopyWithImpl<$Res>
    extends _$CustomNotificationStreamStateCopyWithImpl<$Res,
        _$LoadingNotificationImpl>
    implements _$$LoadingNotificationImplCopyWith<$Res> {
  __$$LoadingNotificationImplCopyWithImpl(_$LoadingNotificationImpl _value,
      $Res Function(_$LoadingNotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingNotificationImpl implements LoadingNotification {
  const _$LoadingNotificationImpl();

  @override
  String toString() {
    return 'CustomNotificationStreamState.loadingNotification()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadingNotificationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingNotification,
    required TResult Function(NotificationModel? notification)
        successNotification,
    required TResult Function(NotificationFailure failure) failureNotification,
  }) {
    return loadingNotification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingNotification,
    TResult? Function(NotificationModel? notification)? successNotification,
    TResult? Function(NotificationFailure failure)? failureNotification,
  }) {
    return loadingNotification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingNotification,
    TResult Function(NotificationModel? notification)? successNotification,
    TResult Function(NotificationFailure failure)? failureNotification,
    required TResult orElse(),
  }) {
    if (loadingNotification != null) {
      return loadingNotification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingNotification value) loadingNotification,
    required TResult Function(SuccessNotification value) successNotification,
    required TResult Function(FailureNotification value) failureNotification,
  }) {
    return loadingNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingNotification value)? loadingNotification,
    TResult? Function(SuccessNotification value)? successNotification,
    TResult? Function(FailureNotification value)? failureNotification,
  }) {
    return loadingNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingNotification value)? loadingNotification,
    TResult Function(SuccessNotification value)? successNotification,
    TResult Function(FailureNotification value)? failureNotification,
    required TResult orElse(),
  }) {
    if (loadingNotification != null) {
      return loadingNotification(this);
    }
    return orElse();
  }
}

abstract class LoadingNotification implements CustomNotificationStreamState {
  const factory LoadingNotification() = _$LoadingNotificationImpl;
}

/// @nodoc
abstract class _$$SuccessNotificationImplCopyWith<$Res> {
  factory _$$SuccessNotificationImplCopyWith(_$SuccessNotificationImpl value,
          $Res Function(_$SuccessNotificationImpl) then) =
      __$$SuccessNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationModel? notification});
}

/// @nodoc
class __$$SuccessNotificationImplCopyWithImpl<$Res>
    extends _$CustomNotificationStreamStateCopyWithImpl<$Res,
        _$SuccessNotificationImpl>
    implements _$$SuccessNotificationImplCopyWith<$Res> {
  __$$SuccessNotificationImplCopyWithImpl(_$SuccessNotificationImpl _value,
      $Res Function(_$SuccessNotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notification = freezed,
  }) {
    return _then(_$SuccessNotificationImpl(
      freezed == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as NotificationModel?,
    ));
  }
}

/// @nodoc

class _$SuccessNotificationImpl implements SuccessNotification {
  const _$SuccessNotificationImpl(this.notification);

  @override
  final NotificationModel? notification;

  @override
  String toString() {
    return 'CustomNotificationStreamState.successNotification(notification: $notification)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessNotificationImpl &&
            (identical(other.notification, notification) ||
                other.notification == notification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notification);

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessNotificationImplCopyWith<_$SuccessNotificationImpl> get copyWith =>
      __$$SuccessNotificationImplCopyWithImpl<_$SuccessNotificationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingNotification,
    required TResult Function(NotificationModel? notification)
        successNotification,
    required TResult Function(NotificationFailure failure) failureNotification,
  }) {
    return successNotification(notification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingNotification,
    TResult? Function(NotificationModel? notification)? successNotification,
    TResult? Function(NotificationFailure failure)? failureNotification,
  }) {
    return successNotification?.call(notification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingNotification,
    TResult Function(NotificationModel? notification)? successNotification,
    TResult Function(NotificationFailure failure)? failureNotification,
    required TResult orElse(),
  }) {
    if (successNotification != null) {
      return successNotification(notification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingNotification value) loadingNotification,
    required TResult Function(SuccessNotification value) successNotification,
    required TResult Function(FailureNotification value) failureNotification,
  }) {
    return successNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingNotification value)? loadingNotification,
    TResult? Function(SuccessNotification value)? successNotification,
    TResult? Function(FailureNotification value)? failureNotification,
  }) {
    return successNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingNotification value)? loadingNotification,
    TResult Function(SuccessNotification value)? successNotification,
    TResult Function(FailureNotification value)? failureNotification,
    required TResult orElse(),
  }) {
    if (successNotification != null) {
      return successNotification(this);
    }
    return orElse();
  }
}

abstract class SuccessNotification implements CustomNotificationStreamState {
  const factory SuccessNotification(final NotificationModel? notification) =
      _$SuccessNotificationImpl;

  NotificationModel? get notification;

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessNotificationImplCopyWith<_$SuccessNotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureNotificationImplCopyWith<$Res> {
  factory _$$FailureNotificationImplCopyWith(_$FailureNotificationImpl value,
          $Res Function(_$FailureNotificationImpl) then) =
      __$$FailureNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationFailure failure});

  $NotificationFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureNotificationImplCopyWithImpl<$Res>
    extends _$CustomNotificationStreamStateCopyWithImpl<$Res,
        _$FailureNotificationImpl>
    implements _$$FailureNotificationImplCopyWith<$Res> {
  __$$FailureNotificationImplCopyWithImpl(_$FailureNotificationImpl _value,
      $Res Function(_$FailureNotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureNotificationImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as NotificationFailure,
    ));
  }

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NotificationFailureCopyWith<$Res> get failure {
    return $NotificationFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureNotificationImpl implements FailureNotification {
  const _$FailureNotificationImpl(this.failure);

  @override
  final NotificationFailure failure;

  @override
  String toString() {
    return 'CustomNotificationStreamState.failureNotification(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureNotificationImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureNotificationImplCopyWith<_$FailureNotificationImpl> get copyWith =>
      __$$FailureNotificationImplCopyWithImpl<_$FailureNotificationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingNotification,
    required TResult Function(NotificationModel? notification)
        successNotification,
    required TResult Function(NotificationFailure failure) failureNotification,
  }) {
    return failureNotification(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingNotification,
    TResult? Function(NotificationModel? notification)? successNotification,
    TResult? Function(NotificationFailure failure)? failureNotification,
  }) {
    return failureNotification?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingNotification,
    TResult Function(NotificationModel? notification)? successNotification,
    TResult Function(NotificationFailure failure)? failureNotification,
    required TResult orElse(),
  }) {
    if (failureNotification != null) {
      return failureNotification(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingNotification value) loadingNotification,
    required TResult Function(SuccessNotification value) successNotification,
    required TResult Function(FailureNotification value) failureNotification,
  }) {
    return failureNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingNotification value)? loadingNotification,
    TResult? Function(SuccessNotification value)? successNotification,
    TResult? Function(FailureNotification value)? failureNotification,
  }) {
    return failureNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingNotification value)? loadingNotification,
    TResult Function(SuccessNotification value)? successNotification,
    TResult Function(FailureNotification value)? failureNotification,
    required TResult orElse(),
  }) {
    if (failureNotification != null) {
      return failureNotification(this);
    }
    return orElse();
  }
}

abstract class FailureNotification implements CustomNotificationStreamState {
  const factory FailureNotification(final NotificationFailure failure) =
      _$FailureNotificationImpl;

  NotificationFailure get failure;

  /// Create a copy of CustomNotificationStreamState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureNotificationImplCopyWith<_$FailureNotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
