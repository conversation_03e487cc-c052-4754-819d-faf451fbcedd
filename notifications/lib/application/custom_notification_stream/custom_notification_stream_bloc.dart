import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:notifications/domain/failure/notification_failure.dart';
import 'package:notifications/domain/model/notification_model.dart';
import '../../domain/facade/notifications_facade.dart';
import '../../repository/notification_repository.dart';

part 'custom_notification_stream_event.dart';
part 'custom_notification_stream_state.dart';
part 'custom_notification_stream_bloc.freezed.dart';

@injectable
class CustomNotificationStreamBloc
    extends Bloc<CustomNotificationStreamEvent, CustomNotificationStreamState> {
  final NotificationFacade _notificationFacade;
  StreamSubscription< Either<NotificationFailure, NotificationModel?>>? _notificationSubscription;
  CustomNotificationStreamBloc(this._notificationFacade) : super(const CustomNotificationStreamState.initial()) {
    on<LoadCustomNotificationStream>(_onLoadCustomNotificationStream);
    on<CustomNotificationStreamLoaded>(_onCustomNotificationStreamLoaded);
  }

  Future<void> _onLoadCustomNotificationStream(
      LoadCustomNotificationStream event,
      Emitter<CustomNotificationStreamState> emit,
      ) async {
    emit(const CustomNotificationStreamState.loadingNotification());
    await _notificationSubscription?.cancel();
    _notificationSubscription = _notificationFacade.streamNotifications().listen(
          (FailureNotification) {
        add(CustomNotificationStreamEvent.CustomNotificationStreamLoaded(FailureNotification));
      },
    );
  }

  Future<void> _onCustomNotificationStreamLoaded(
      CustomNotificationStreamLoaded event,
      Emitter<CustomNotificationStreamState> emit,
      ) async {
    event.failureOrNotification.mapBoth(
      onLeft:  (failure) => emit(CustomNotificationStreamState.failureNotification(failure)),
      onRight:    (notification) => emit(CustomNotificationStreamState.successNotification(notification)),
    );
  }

  @override
  Future<void> close() async {
    await _notificationSubscription?.cancel();
    return super.close();
  }


}
