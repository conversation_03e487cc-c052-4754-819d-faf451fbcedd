// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NotificationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationModel notification)
        scheduleNotification,
    required TResult Function(NotificationModel notification) showNotification,
    required TResult Function(BuildContext context) checkForNotifications,
    required TResult Function(int id) cancelNotification,
    required TResult Function(BuildContext context) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationModel notification)? scheduleNotification,
    TResult? Function(NotificationModel notification)? showNotification,
    TResult? Function(BuildContext context)? checkForNotifications,
    TResult? Function(int id)? cancelNotification,
    TResult? Function(BuildContext context)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationModel notification)? scheduleNotification,
    TResult Function(NotificationModel notification)? showNotification,
    TResult Function(BuildContext context)? checkForNotifications,
    TResult Function(int id)? cancelNotification,
    TResult Function(BuildContext context)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ScheduleNotification value) scheduleNotification,
    required TResult Function(ShowNotification value) showNotification,
    required TResult Function(CheckForNotifications value)
        checkForNotifications,
    required TResult Function(CancelNotification value) cancelNotification,
    required TResult Function(Initialize value) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ScheduleNotification value)? scheduleNotification,
    TResult? Function(ShowNotification value)? showNotification,
    TResult? Function(CheckForNotifications value)? checkForNotifications,
    TResult? Function(CancelNotification value)? cancelNotification,
    TResult? Function(Initialize value)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ScheduleNotification value)? scheduleNotification,
    TResult Function(ShowNotification value)? showNotification,
    TResult Function(CheckForNotifications value)? checkForNotifications,
    TResult Function(CancelNotification value)? cancelNotification,
    TResult Function(Initialize value)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationEventCopyWith<$Res> {
  factory $NotificationEventCopyWith(
          NotificationEvent value, $Res Function(NotificationEvent) then) =
      _$NotificationEventCopyWithImpl<$Res, NotificationEvent>;
}

/// @nodoc
class _$NotificationEventCopyWithImpl<$Res, $Val extends NotificationEvent>
    implements $NotificationEventCopyWith<$Res> {
  _$NotificationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ScheduleNotificationImplCopyWith<$Res> {
  factory _$$ScheduleNotificationImplCopyWith(_$ScheduleNotificationImpl value,
          $Res Function(_$ScheduleNotificationImpl) then) =
      __$$ScheduleNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationModel notification});
}

/// @nodoc
class __$$ScheduleNotificationImplCopyWithImpl<$Res>
    extends _$NotificationEventCopyWithImpl<$Res, _$ScheduleNotificationImpl>
    implements _$$ScheduleNotificationImplCopyWith<$Res> {
  __$$ScheduleNotificationImplCopyWithImpl(_$ScheduleNotificationImpl _value,
      $Res Function(_$ScheduleNotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notification = null,
  }) {
    return _then(_$ScheduleNotificationImpl(
      null == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as NotificationModel,
    ));
  }
}

/// @nodoc

class _$ScheduleNotificationImpl implements ScheduleNotification {
  const _$ScheduleNotificationImpl(this.notification);

  @override
  final NotificationModel notification;

  @override
  String toString() {
    return 'NotificationEvent.scheduleNotification(notification: $notification)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleNotificationImpl &&
            (identical(other.notification, notification) ||
                other.notification == notification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notification);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleNotificationImplCopyWith<_$ScheduleNotificationImpl>
      get copyWith =>
          __$$ScheduleNotificationImplCopyWithImpl<_$ScheduleNotificationImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationModel notification)
        scheduleNotification,
    required TResult Function(NotificationModel notification) showNotification,
    required TResult Function(BuildContext context) checkForNotifications,
    required TResult Function(int id) cancelNotification,
    required TResult Function(BuildContext context) initialize,
  }) {
    return scheduleNotification(notification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationModel notification)? scheduleNotification,
    TResult? Function(NotificationModel notification)? showNotification,
    TResult? Function(BuildContext context)? checkForNotifications,
    TResult? Function(int id)? cancelNotification,
    TResult? Function(BuildContext context)? initialize,
  }) {
    return scheduleNotification?.call(notification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationModel notification)? scheduleNotification,
    TResult Function(NotificationModel notification)? showNotification,
    TResult Function(BuildContext context)? checkForNotifications,
    TResult Function(int id)? cancelNotification,
    TResult Function(BuildContext context)? initialize,
    required TResult orElse(),
  }) {
    if (scheduleNotification != null) {
      return scheduleNotification(notification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ScheduleNotification value) scheduleNotification,
    required TResult Function(ShowNotification value) showNotification,
    required TResult Function(CheckForNotifications value)
        checkForNotifications,
    required TResult Function(CancelNotification value) cancelNotification,
    required TResult Function(Initialize value) initialize,
  }) {
    return scheduleNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ScheduleNotification value)? scheduleNotification,
    TResult? Function(ShowNotification value)? showNotification,
    TResult? Function(CheckForNotifications value)? checkForNotifications,
    TResult? Function(CancelNotification value)? cancelNotification,
    TResult? Function(Initialize value)? initialize,
  }) {
    return scheduleNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ScheduleNotification value)? scheduleNotification,
    TResult Function(ShowNotification value)? showNotification,
    TResult Function(CheckForNotifications value)? checkForNotifications,
    TResult Function(CancelNotification value)? cancelNotification,
    TResult Function(Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (scheduleNotification != null) {
      return scheduleNotification(this);
    }
    return orElse();
  }
}

abstract class ScheduleNotification implements NotificationEvent {
  const factory ScheduleNotification(final NotificationModel notification) =
      _$ScheduleNotificationImpl;

  NotificationModel get notification;

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleNotificationImplCopyWith<_$ScheduleNotificationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ShowNotificationImplCopyWith<$Res> {
  factory _$$ShowNotificationImplCopyWith(_$ShowNotificationImpl value,
          $Res Function(_$ShowNotificationImpl) then) =
      __$$ShowNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationModel notification});
}

/// @nodoc
class __$$ShowNotificationImplCopyWithImpl<$Res>
    extends _$NotificationEventCopyWithImpl<$Res, _$ShowNotificationImpl>
    implements _$$ShowNotificationImplCopyWith<$Res> {
  __$$ShowNotificationImplCopyWithImpl(_$ShowNotificationImpl _value,
      $Res Function(_$ShowNotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notification = null,
  }) {
    return _then(_$ShowNotificationImpl(
      null == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as NotificationModel,
    ));
  }
}

/// @nodoc

class _$ShowNotificationImpl implements ShowNotification {
  const _$ShowNotificationImpl(this.notification);

  @override
  final NotificationModel notification;

  @override
  String toString() {
    return 'NotificationEvent.showNotification(notification: $notification)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShowNotificationImpl &&
            (identical(other.notification, notification) ||
                other.notification == notification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notification);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShowNotificationImplCopyWith<_$ShowNotificationImpl> get copyWith =>
      __$$ShowNotificationImplCopyWithImpl<_$ShowNotificationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationModel notification)
        scheduleNotification,
    required TResult Function(NotificationModel notification) showNotification,
    required TResult Function(BuildContext context) checkForNotifications,
    required TResult Function(int id) cancelNotification,
    required TResult Function(BuildContext context) initialize,
  }) {
    return showNotification(notification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationModel notification)? scheduleNotification,
    TResult? Function(NotificationModel notification)? showNotification,
    TResult? Function(BuildContext context)? checkForNotifications,
    TResult? Function(int id)? cancelNotification,
    TResult? Function(BuildContext context)? initialize,
  }) {
    return showNotification?.call(notification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationModel notification)? scheduleNotification,
    TResult Function(NotificationModel notification)? showNotification,
    TResult Function(BuildContext context)? checkForNotifications,
    TResult Function(int id)? cancelNotification,
    TResult Function(BuildContext context)? initialize,
    required TResult orElse(),
  }) {
    if (showNotification != null) {
      return showNotification(notification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ScheduleNotification value) scheduleNotification,
    required TResult Function(ShowNotification value) showNotification,
    required TResult Function(CheckForNotifications value)
        checkForNotifications,
    required TResult Function(CancelNotification value) cancelNotification,
    required TResult Function(Initialize value) initialize,
  }) {
    return showNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ScheduleNotification value)? scheduleNotification,
    TResult? Function(ShowNotification value)? showNotification,
    TResult? Function(CheckForNotifications value)? checkForNotifications,
    TResult? Function(CancelNotification value)? cancelNotification,
    TResult? Function(Initialize value)? initialize,
  }) {
    return showNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ScheduleNotification value)? scheduleNotification,
    TResult Function(ShowNotification value)? showNotification,
    TResult Function(CheckForNotifications value)? checkForNotifications,
    TResult Function(CancelNotification value)? cancelNotification,
    TResult Function(Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (showNotification != null) {
      return showNotification(this);
    }
    return orElse();
  }
}

abstract class ShowNotification implements NotificationEvent {
  const factory ShowNotification(final NotificationModel notification) =
      _$ShowNotificationImpl;

  NotificationModel get notification;

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShowNotificationImplCopyWith<_$ShowNotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CheckForNotificationsImplCopyWith<$Res> {
  factory _$$CheckForNotificationsImplCopyWith(
          _$CheckForNotificationsImpl value,
          $Res Function(_$CheckForNotificationsImpl) then) =
      __$$CheckForNotificationsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BuildContext context});
}

/// @nodoc
class __$$CheckForNotificationsImplCopyWithImpl<$Res>
    extends _$NotificationEventCopyWithImpl<$Res, _$CheckForNotificationsImpl>
    implements _$$CheckForNotificationsImplCopyWith<$Res> {
  __$$CheckForNotificationsImplCopyWithImpl(_$CheckForNotificationsImpl _value,
      $Res Function(_$CheckForNotificationsImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? context = null,
  }) {
    return _then(_$CheckForNotificationsImpl(
      null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as BuildContext,
    ));
  }
}

/// @nodoc

class _$CheckForNotificationsImpl implements CheckForNotifications {
  const _$CheckForNotificationsImpl(this.context);

  @override
  final BuildContext context;

  @override
  String toString() {
    return 'NotificationEvent.checkForNotifications(context: $context)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckForNotificationsImpl &&
            (identical(other.context, context) || other.context == context));
  }

  @override
  int get hashCode => Object.hash(runtimeType, context);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckForNotificationsImplCopyWith<_$CheckForNotificationsImpl>
      get copyWith => __$$CheckForNotificationsImplCopyWithImpl<
          _$CheckForNotificationsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationModel notification)
        scheduleNotification,
    required TResult Function(NotificationModel notification) showNotification,
    required TResult Function(BuildContext context) checkForNotifications,
    required TResult Function(int id) cancelNotification,
    required TResult Function(BuildContext context) initialize,
  }) {
    return checkForNotifications(context);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationModel notification)? scheduleNotification,
    TResult? Function(NotificationModel notification)? showNotification,
    TResult? Function(BuildContext context)? checkForNotifications,
    TResult? Function(int id)? cancelNotification,
    TResult? Function(BuildContext context)? initialize,
  }) {
    return checkForNotifications?.call(context);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationModel notification)? scheduleNotification,
    TResult Function(NotificationModel notification)? showNotification,
    TResult Function(BuildContext context)? checkForNotifications,
    TResult Function(int id)? cancelNotification,
    TResult Function(BuildContext context)? initialize,
    required TResult orElse(),
  }) {
    if (checkForNotifications != null) {
      return checkForNotifications(context);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ScheduleNotification value) scheduleNotification,
    required TResult Function(ShowNotification value) showNotification,
    required TResult Function(CheckForNotifications value)
        checkForNotifications,
    required TResult Function(CancelNotification value) cancelNotification,
    required TResult Function(Initialize value) initialize,
  }) {
    return checkForNotifications(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ScheduleNotification value)? scheduleNotification,
    TResult? Function(ShowNotification value)? showNotification,
    TResult? Function(CheckForNotifications value)? checkForNotifications,
    TResult? Function(CancelNotification value)? cancelNotification,
    TResult? Function(Initialize value)? initialize,
  }) {
    return checkForNotifications?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ScheduleNotification value)? scheduleNotification,
    TResult Function(ShowNotification value)? showNotification,
    TResult Function(CheckForNotifications value)? checkForNotifications,
    TResult Function(CancelNotification value)? cancelNotification,
    TResult Function(Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (checkForNotifications != null) {
      return checkForNotifications(this);
    }
    return orElse();
  }
}

abstract class CheckForNotifications implements NotificationEvent {
  const factory CheckForNotifications(final BuildContext context) =
      _$CheckForNotificationsImpl;

  BuildContext get context;

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckForNotificationsImplCopyWith<_$CheckForNotificationsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CancelNotificationImplCopyWith<$Res> {
  factory _$$CancelNotificationImplCopyWith(_$CancelNotificationImpl value,
          $Res Function(_$CancelNotificationImpl) then) =
      __$$CancelNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int id});
}

/// @nodoc
class __$$CancelNotificationImplCopyWithImpl<$Res>
    extends _$NotificationEventCopyWithImpl<$Res, _$CancelNotificationImpl>
    implements _$$CancelNotificationImplCopyWith<$Res> {
  __$$CancelNotificationImplCopyWithImpl(_$CancelNotificationImpl _value,
      $Res Function(_$CancelNotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$CancelNotificationImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$CancelNotificationImpl implements CancelNotification {
  const _$CancelNotificationImpl(this.id);

  @override
  final int id;

  @override
  String toString() {
    return 'NotificationEvent.cancelNotification(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CancelNotificationImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CancelNotificationImplCopyWith<_$CancelNotificationImpl> get copyWith =>
      __$$CancelNotificationImplCopyWithImpl<_$CancelNotificationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationModel notification)
        scheduleNotification,
    required TResult Function(NotificationModel notification) showNotification,
    required TResult Function(BuildContext context) checkForNotifications,
    required TResult Function(int id) cancelNotification,
    required TResult Function(BuildContext context) initialize,
  }) {
    return cancelNotification(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationModel notification)? scheduleNotification,
    TResult? Function(NotificationModel notification)? showNotification,
    TResult? Function(BuildContext context)? checkForNotifications,
    TResult? Function(int id)? cancelNotification,
    TResult? Function(BuildContext context)? initialize,
  }) {
    return cancelNotification?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationModel notification)? scheduleNotification,
    TResult Function(NotificationModel notification)? showNotification,
    TResult Function(BuildContext context)? checkForNotifications,
    TResult Function(int id)? cancelNotification,
    TResult Function(BuildContext context)? initialize,
    required TResult orElse(),
  }) {
    if (cancelNotification != null) {
      return cancelNotification(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ScheduleNotification value) scheduleNotification,
    required TResult Function(ShowNotification value) showNotification,
    required TResult Function(CheckForNotifications value)
        checkForNotifications,
    required TResult Function(CancelNotification value) cancelNotification,
    required TResult Function(Initialize value) initialize,
  }) {
    return cancelNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ScheduleNotification value)? scheduleNotification,
    TResult? Function(ShowNotification value)? showNotification,
    TResult? Function(CheckForNotifications value)? checkForNotifications,
    TResult? Function(CancelNotification value)? cancelNotification,
    TResult? Function(Initialize value)? initialize,
  }) {
    return cancelNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ScheduleNotification value)? scheduleNotification,
    TResult Function(ShowNotification value)? showNotification,
    TResult Function(CheckForNotifications value)? checkForNotifications,
    TResult Function(CancelNotification value)? cancelNotification,
    TResult Function(Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (cancelNotification != null) {
      return cancelNotification(this);
    }
    return orElse();
  }
}

abstract class CancelNotification implements NotificationEvent {
  const factory CancelNotification(final int id) = _$CancelNotificationImpl;

  int get id;

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CancelNotificationImplCopyWith<_$CancelNotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitializeImplCopyWith<$Res> {
  factory _$$InitializeImplCopyWith(
          _$InitializeImpl value, $Res Function(_$InitializeImpl) then) =
      __$$InitializeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BuildContext context});
}

/// @nodoc
class __$$InitializeImplCopyWithImpl<$Res>
    extends _$NotificationEventCopyWithImpl<$Res, _$InitializeImpl>
    implements _$$InitializeImplCopyWith<$Res> {
  __$$InitializeImplCopyWithImpl(
      _$InitializeImpl _value, $Res Function(_$InitializeImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? context = null,
  }) {
    return _then(_$InitializeImpl(
      null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as BuildContext,
    ));
  }
}

/// @nodoc

class _$InitializeImpl implements Initialize {
  const _$InitializeImpl(this.context);

  @override
  final BuildContext context;

  @override
  String toString() {
    return 'NotificationEvent.initialize(context: $context)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitializeImpl &&
            (identical(other.context, context) || other.context == context));
  }

  @override
  int get hashCode => Object.hash(runtimeType, context);

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InitializeImplCopyWith<_$InitializeImpl> get copyWith =>
      __$$InitializeImplCopyWithImpl<_$InitializeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationModel notification)
        scheduleNotification,
    required TResult Function(NotificationModel notification) showNotification,
    required TResult Function(BuildContext context) checkForNotifications,
    required TResult Function(int id) cancelNotification,
    required TResult Function(BuildContext context) initialize,
  }) {
    return initialize(context);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationModel notification)? scheduleNotification,
    TResult? Function(NotificationModel notification)? showNotification,
    TResult? Function(BuildContext context)? checkForNotifications,
    TResult? Function(int id)? cancelNotification,
    TResult? Function(BuildContext context)? initialize,
  }) {
    return initialize?.call(context);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationModel notification)? scheduleNotification,
    TResult Function(NotificationModel notification)? showNotification,
    TResult Function(BuildContext context)? checkForNotifications,
    TResult Function(int id)? cancelNotification,
    TResult Function(BuildContext context)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(context);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ScheduleNotification value) scheduleNotification,
    required TResult Function(ShowNotification value) showNotification,
    required TResult Function(CheckForNotifications value)
        checkForNotifications,
    required TResult Function(CancelNotification value) cancelNotification,
    required TResult Function(Initialize value) initialize,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ScheduleNotification value)? scheduleNotification,
    TResult? Function(ShowNotification value)? showNotification,
    TResult? Function(CheckForNotifications value)? checkForNotifications,
    TResult? Function(CancelNotification value)? cancelNotification,
    TResult? Function(Initialize value)? initialize,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ScheduleNotification value)? scheduleNotification,
    TResult Function(ShowNotification value)? showNotification,
    TResult Function(CheckForNotifications value)? checkForNotifications,
    TResult Function(CancelNotification value)? cancelNotification,
    TResult Function(Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class Initialize implements NotificationEvent {
  const factory Initialize(final BuildContext context) = _$InitializeImpl;

  BuildContext get context;

  /// Create a copy of NotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InitializeImplCopyWith<_$InitializeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$NotificationState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(NotificationFailure failure) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(NotificationFailure failure)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(NotificationFailure failure)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Success value) success,
    required TResult Function(Failure value) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Success value)? success,
    TResult? Function(Failure value)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Success value)? success,
    TResult Function(Failure value)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationStateCopyWith<$Res> {
  factory $NotificationStateCopyWith(
          NotificationState value, $Res Function(NotificationState) then) =
      _$NotificationStateCopyWithImpl<$Res, NotificationState>;
}

/// @nodoc
class _$NotificationStateCopyWithImpl<$Res, $Val extends NotificationState>
    implements $NotificationStateCopyWith<$Res> {
  _$NotificationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$NotificationStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'NotificationState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(NotificationFailure failure) failure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(NotificationFailure failure)? failure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(NotificationFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Success value) success,
    required TResult Function(Failure value) failure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Success value)? success,
    TResult? Function(Failure value)? failure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Success value)? success,
    TResult Function(Failure value)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements NotificationState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$NotificationStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'NotificationState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(NotificationFailure failure) failure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(NotificationFailure failure)? failure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(NotificationFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Success value) success,
    required TResult Function(Failure value) failure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Success value)? success,
    TResult? Function(Failure value)? failure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Success value)? success,
    TResult Function(Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class Loading implements NotificationState {
  const factory Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$SuccessImplCopyWith<$Res> {
  factory _$$SuccessImplCopyWith(
          _$SuccessImpl value, $Res Function(_$SuccessImpl) then) =
      __$$SuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SuccessImplCopyWithImpl<$Res>
    extends _$NotificationStateCopyWithImpl<$Res, _$SuccessImpl>
    implements _$$SuccessImplCopyWith<$Res> {
  __$$SuccessImplCopyWithImpl(
      _$SuccessImpl _value, $Res Function(_$SuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SuccessImpl implements Success {
  const _$SuccessImpl();

  @override
  String toString() {
    return 'NotificationState.success()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(NotificationFailure failure) failure,
  }) {
    return success();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(NotificationFailure failure)? failure,
  }) {
    return success?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(NotificationFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Success value) success,
    required TResult Function(Failure value) failure,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Success value)? success,
    TResult? Function(Failure value)? failure,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Success value)? success,
    TResult Function(Failure value)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class Success implements NotificationState {
  const factory Success() = _$SuccessImpl;
}

/// @nodoc
abstract class _$$FailureImplCopyWith<$Res> {
  factory _$$FailureImplCopyWith(
          _$FailureImpl value, $Res Function(_$FailureImpl) then) =
      __$$FailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationFailure failure});

  $NotificationFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureImplCopyWithImpl<$Res>
    extends _$NotificationStateCopyWithImpl<$Res, _$FailureImpl>
    implements _$$FailureImplCopyWith<$Res> {
  __$$FailureImplCopyWithImpl(
      _$FailureImpl _value, $Res Function(_$FailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as NotificationFailure,
    ));
  }

  /// Create a copy of NotificationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NotificationFailureCopyWith<$Res> get failure {
    return $NotificationFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureImpl implements Failure {
  const _$FailureImpl(this.failure);

  @override
  final NotificationFailure failure;

  @override
  String toString() {
    return 'NotificationState.failure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of NotificationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      __$$FailureImplCopyWithImpl<_$FailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(NotificationFailure failure) failure,
  }) {
    return failure(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(NotificationFailure failure)? failure,
  }) {
    return failure?.call(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(NotificationFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this.failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Success value) success,
    required TResult Function(Failure value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Success value)? success,
    TResult? Function(Failure value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Success value)? success,
    TResult Function(Failure value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class Failure implements NotificationState {
  const factory Failure(final NotificationFailure failure) = _$FailureImpl;

  NotificationFailure get failure;

  /// Create a copy of NotificationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
