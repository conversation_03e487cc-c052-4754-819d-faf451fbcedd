part of 'notification_bloc.dart';


@freezed
class NotificationEvent with _$NotificationEvent {
  const factory NotificationEvent.scheduleNotification(NotificationModel notification) = ScheduleNotification;
  const factory NotificationEvent.showNotification(NotificationModel notification) = ShowNotification;
  const factory NotificationEvent.checkForNotifications(BuildContext context) = CheckForNotifications;
  const factory NotificationEvent.cancelNotification(int id) = CancelNotification;
  const factory NotificationEvent.initialize(BuildContext context) = Initialize;
}

