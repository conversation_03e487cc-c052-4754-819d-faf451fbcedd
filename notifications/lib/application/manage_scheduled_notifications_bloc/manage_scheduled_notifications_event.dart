part of 'manage_scheduled_notifications_bloc.dart';

@freezed
class ManageScheduledNotificationsEvent with _$ManageScheduledNotificationsEvent {
  const factory ManageScheduledNotificationsEvent.scheduleNotificationsWeekly({
    required List<String> daystoBeNotified,
    required List<String> timeofDay,
    required String body,
    required String notificationGroupId,
  }) = _ScheduleNotifications;
//daily
  const factory ManageScheduledNotificationsEvent.scheduleNotificationsDaily({
    required List<String> timeofDay,
    required String body,
    required String notificationGroupId,
  }) = _ScheduleNotificationsDaily;
//monthly
  const factory ManageScheduledNotificationsEvent.scheduleNotificationsMonthly({
    required tz.TZDateTime dateTime,
    required String body,
    required String notificationGroupId,
  }) = _ScheduleNotificationsMonthly;

  const factory ManageScheduledNotificationsEvent.disableNotification({
    required String notificationGroupId,
  }) = _CancelNotification;

  const factory ManageScheduledNotificationsEvent.scheduleSingleNotification({
    required String body,
    required tz.TZDateTime dateTime,
    required String notificationId,
    required String notificationType,
    required String? payload,
    required bool isForeground,
  }) = _ScheduleSingleNotification;

}
