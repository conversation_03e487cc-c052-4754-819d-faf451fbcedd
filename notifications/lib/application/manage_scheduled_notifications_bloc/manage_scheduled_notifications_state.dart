part of 'manage_scheduled_notifications_bloc.dart';

@freezed
class ManageScheduledNotificationsState with _$ManageScheduledNotificationsState {
  const factory ManageScheduledNotificationsState({
    required bool isLoading,
    required Option<Either<NotificationFailure, Unit>> failureOrSuccessOption,
    required List<NotificationModel> scheduledNotifications,
  }) = _ManageScheduledNotificationsState;

  factory ManageScheduledNotificationsState.initial() => ManageScheduledNotificationsState(
    isLoading: false,
    failureOrSuccessOption: None(),
    scheduledNotifications: [],
  );
}
