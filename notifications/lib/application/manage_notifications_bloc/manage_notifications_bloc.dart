import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/facade/notifications_facade.dart';
import '../../domain/failure/notification_failure.dart';
import '../../domain/model/notification_model.dart';

part 'manage_notifications_event.dart';
part 'manage_notifications_state.dart';
part 'manage_notifications_bloc.freezed.dart';

@injectable
class ManageNotificationsBloc
    extends Bloc<ManageNotificationsEvent, ManageNotificationsState> {
  final NotificationFacade _notificationFacade;
  ManageNotificationsBloc(this._notificationFacade)
      : super(const ManageNotificationsState.initial()) {
    on<LoadNotifications>(_onLoadNotifications);
    on<DeleteNotification>(_onDeleteNotification);
    on<DeleteAllNotifications>(_onDeleteAllNotifications);
  }
  Future<void> _onLoadNotifications(
    LoadNotifications event,
    Emitter<ManageNotificationsState> emit,
  ) async {
    emit(const ManageNotificationsState.loading());
    final notifications = await _notificationFacade.getNotifications();
    notifications.mapBoth(
      onLeft: (failure) => emit(ManageNotificationsState.failure(failure)),
      onRight: (notifications) =>
          emit(ManageNotificationsState.success(notifications)),
    );
  }

  Future<void> _onDeleteNotification(
    DeleteNotification event,
    Emitter<ManageNotificationsState> emit,
  ) async {
    emit(const ManageNotificationsState.loading());
    final failureOrSuccess =
        await _notificationFacade.deleteNotification(event.id);
    failureOrSuccess.mapBoth(
      onLeft: (failure) => emit(ManageNotificationsState.failure(failure)),
      onRight: (_) => emit(const ManageNotificationsState.deleteSuccess()),
    );
  }

  Future<void> _onDeleteAllNotifications(
    DeleteAllNotifications event,
    Emitter<ManageNotificationsState> emit,
  ) async {
    emit(const ManageNotificationsState.loading());
    final failureOrSuccess = await _notificationFacade.deleteAllNotifications();
    failureOrSuccess.mapBoth(
      onLeft: (failure) => emit(ManageNotificationsState.failure(failure)),
      onRight: (_) => emit(const ManageNotificationsState.deleteSuccess()),
    );
  }
}
