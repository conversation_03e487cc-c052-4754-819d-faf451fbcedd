part of 'manage_notifications_bloc.dart';

@freezed
class ManageNotificationsState with _$ManageNotificationsState {
  const factory ManageNotificationsState.initial() = Initial;
  const factory ManageNotificationsState.loading() = Loading;
  const factory ManageNotificationsState.success(List<NotificationModel> notifications) = Success;
  const factory ManageNotificationsState.deleteSuccess() = DeleteSuccess;
  const factory ManageNotificationsState.failure(NotificationFailure failure) = Failure;
}
