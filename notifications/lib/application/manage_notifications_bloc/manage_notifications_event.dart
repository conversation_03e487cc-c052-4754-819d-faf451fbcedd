part of 'manage_notifications_bloc.dart';

@freezed
class ManageNotificationsEvent with _$ManageNotificationsEvent {
  const factory ManageNotificationsEvent.initialize() = Initialize;
  const factory ManageNotificationsEvent.loadNotifications() = LoadNotifications;
  const factory ManageNotificationsEvent.deleteNotification(int id) = DeleteNotification;
  const factory ManageNotificationsEvent.deleteAllNotifications() = DeleteAllNotifications;
}
