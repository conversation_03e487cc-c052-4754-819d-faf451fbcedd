// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:firebase_messaging/firebase_messaging.dart' as _i892;
import 'package:flutter_local_notifications/flutter_local_notifications.dart'
    as _i163;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:notifications/application/custom_notification_stream/custom_notification_stream_bloc.dart'
    as _i415;
import 'package:notifications/application/manage_notifications_bloc/manage_notifications_bloc.dart'
    as _i1040;
import 'package:notifications/application/manage_scheduled_notifications_bloc/manage_scheduled_notifications_bloc.dart'
    as _i953;
import 'package:notifications/application/notification_bloc/notification_bloc.dart'
    as _i365;
import 'package:notifications/domain/facade/notifications_facade.dart' as _i725;
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart'
    as _i828;
import 'package:notifications/domain/module/notification_module.dart' as _i582;
import 'package:notifications/repository/notification_repository.dart' as _i131;
import 'package:notifications/repository/scheduled_notifications_repository.dart'
    as _i1013;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final notificationModule = _$NotificationModule();
    gh.singleton<_i163.FlutterLocalNotificationsPlugin>(
        () => notificationModule.flutterLocalNotificationsPlugin);
    gh.lazySingleton<_i725.NotificationFacade>(
        () => _i131.NotificationFacadeImpl(
              gh<_i163.FlutterLocalNotificationsPlugin>(),
              gh<_i892.FirebaseMessaging>(),
            ));
    gh.factory<_i1040.ManageNotificationsBloc>(
        () => _i1040.ManageNotificationsBloc(gh<_i725.NotificationFacade>()));
    gh.factory<_i365.NotificationBloc>(
        () => _i365.NotificationBloc(gh<_i725.NotificationFacade>()));
    gh.factory<_i415.CustomNotificationStreamBloc>(() =>
        _i415.CustomNotificationStreamBloc(gh<_i725.NotificationFacade>()));
    gh.lazySingleton<_i828.ScheduledNotificationsFacade>(() =>
        _i1013.ScheduledNotificationRepository(gh<_i725.NotificationFacade>()));
    gh.factory<_i953.ManageScheduledNotificationsBloc>(() =>
        _i953.ManageScheduledNotificationsBloc(
            gh<_i828.ScheduledNotificationsFacade>()));
    return this;
  }
}

class _$NotificationModule extends _i582.NotificationModule {}
