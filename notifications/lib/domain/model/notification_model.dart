import 'package:json_annotation/json_annotation.dart';
  import 'package:timezone/timezone.dart' as tz;

  part 'notification_model.g.dart';

  // This class represents a help center model with JSON serialization.
  @JsonSerializable(explicitToJson: true)
  class NotificationModel {
    // Notification ID
    int? id;

    // Notification title
    String? notificationType;

    String? title;

    // Notification body
    String? body;

    // Notification schedule time
    @JsonKey(fromJson: _fromJson, toJson: _toJson)
    tz.TZDateTime? scheduleTime;

    // receivedAt
    @JsonKey(fromJson: _fromJson, toJson: _toJson)
    tz.TZDateTime? receivedAt;

    // Notification type
    String? payload;

    // Constructor for the NotificationModel class
    NotificationModel(
        {this.id, this.title, this.body, this.scheduleTime, this.receivedAt, this.notificationType, this.payload});

    // Method to convert a NotificationModel instance into a JSON map.
    Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

    // Method to create a NotificationModel instance from a JSON map.
    factory NotificationModel.fromJson(Map<String, dynamic> json) =>
        _$NotificationModelFromJson(json);

    // Method to convert a string to TZDateTime
    static tz.TZDateTime? _fromJson(String? dateString) {
      if (dateString == null || dateString.isEmpty) {
        return null;
      }
      return tz.TZDateTime.from(DateTime.parse(dateString), tz.local);
    }

    // Method to convert a DateTime instance to a JSON string.
    static String _toJson(tz.TZDateTime? dateTime) {
      if (dateTime == null) {
        return '';
      }
      return dateTime.toIso8601String();
    }
  }