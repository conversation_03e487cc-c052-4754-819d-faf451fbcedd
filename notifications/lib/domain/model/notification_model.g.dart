// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) =>
    NotificationModel(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      body: json['body'] as String?,
      scheduleTime:
          NotificationModel._fromJson(json['scheduleTime'] as String?),
      receivedAt: NotificationModel._fromJson(json['receivedAt'] as String?),
      notificationType: json['notificationType'] as String?,
      payload: json['payload'] as String?,
    );

Map<String, dynamic> _$NotificationModelToJson(NotificationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'notificationType': instance.notificationType,
      'title': instance.title,
      'body': instance.body,
      'scheduleTime': NotificationModel._toJson(instance.scheduleTime),
      'receivedAt': NotificationModel._toJson(instance.receivedAt),
      'payload': instance.payload,
    };
