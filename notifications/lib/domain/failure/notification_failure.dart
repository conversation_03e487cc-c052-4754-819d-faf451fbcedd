import 'package:freezed_annotation/freezed_annotation.dart';
part 'notification_failure.freezed.dart';

@freezed
class NotificationFailure with _$NotificationFailure {
  // Failure representing an error when trying to get the help center videos
  const factory NotificationFailure.getNotificationFailure(
      String failureMessage) = GetNotificationFailure;

  //delete notification failure
  const factory NotificationFailure.deleteNotificationFailure(
      String failureMessage) = DeleteNotificationFailure;
  //unexpected failure
  const factory NotificationFailure.unexpectedFailure(
      String failureMessage) = UnexpectedFailure;




}