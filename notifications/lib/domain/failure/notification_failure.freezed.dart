// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NotificationFailure {
  String get failureMessage => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) getNotificationFailure,
    required TResult Function(String failureMessage) deleteNotificationFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? getNotificationFailure,
    TResult? Function(String failureMessage)? deleteNotificationFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? getNotificationFailure,
    TResult Function(String failureMessage)? deleteNotificationFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetNotificationFailure value)
        getNotificationFailure,
    required TResult Function(DeleteNotificationFailure value)
        deleteNotificationFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetNotificationFailure value)? getNotificationFailure,
    TResult? Function(DeleteNotificationFailure value)?
        deleteNotificationFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetNotificationFailure value)? getNotificationFailure,
    TResult Function(DeleteNotificationFailure value)?
        deleteNotificationFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationFailureCopyWith<NotificationFailure> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationFailureCopyWith<$Res> {
  factory $NotificationFailureCopyWith(
          NotificationFailure value, $Res Function(NotificationFailure) then) =
      _$NotificationFailureCopyWithImpl<$Res, NotificationFailure>;
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$NotificationFailureCopyWithImpl<$Res, $Val extends NotificationFailure>
    implements $NotificationFailureCopyWith<$Res> {
  _$NotificationFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_value.copyWith(
      failureMessage: null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetNotificationFailureImplCopyWith<$Res>
    implements $NotificationFailureCopyWith<$Res> {
  factory _$$GetNotificationFailureImplCopyWith(
          _$GetNotificationFailureImpl value,
          $Res Function(_$GetNotificationFailureImpl) then) =
      __$$GetNotificationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$GetNotificationFailureImplCopyWithImpl<$Res>
    extends _$NotificationFailureCopyWithImpl<$Res,
        _$GetNotificationFailureImpl>
    implements _$$GetNotificationFailureImplCopyWith<$Res> {
  __$$GetNotificationFailureImplCopyWithImpl(
      _$GetNotificationFailureImpl _value,
      $Res Function(_$GetNotificationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$GetNotificationFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetNotificationFailureImpl implements GetNotificationFailure {
  const _$GetNotificationFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'NotificationFailure.getNotificationFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetNotificationFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetNotificationFailureImplCopyWith<_$GetNotificationFailureImpl>
      get copyWith => __$$GetNotificationFailureImplCopyWithImpl<
          _$GetNotificationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) getNotificationFailure,
    required TResult Function(String failureMessage) deleteNotificationFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return getNotificationFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? getNotificationFailure,
    TResult? Function(String failureMessage)? deleteNotificationFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return getNotificationFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? getNotificationFailure,
    TResult Function(String failureMessage)? deleteNotificationFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (getNotificationFailure != null) {
      return getNotificationFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetNotificationFailure value)
        getNotificationFailure,
    required TResult Function(DeleteNotificationFailure value)
        deleteNotificationFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return getNotificationFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetNotificationFailure value)? getNotificationFailure,
    TResult? Function(DeleteNotificationFailure value)?
        deleteNotificationFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return getNotificationFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetNotificationFailure value)? getNotificationFailure,
    TResult Function(DeleteNotificationFailure value)?
        deleteNotificationFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (getNotificationFailure != null) {
      return getNotificationFailure(this);
    }
    return orElse();
  }
}

abstract class GetNotificationFailure implements NotificationFailure {
  const factory GetNotificationFailure(final String failureMessage) =
      _$GetNotificationFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetNotificationFailureImplCopyWith<_$GetNotificationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteNotificationFailureImplCopyWith<$Res>
    implements $NotificationFailureCopyWith<$Res> {
  factory _$$DeleteNotificationFailureImplCopyWith(
          _$DeleteNotificationFailureImpl value,
          $Res Function(_$DeleteNotificationFailureImpl) then) =
      __$$DeleteNotificationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$DeleteNotificationFailureImplCopyWithImpl<$Res>
    extends _$NotificationFailureCopyWithImpl<$Res,
        _$DeleteNotificationFailureImpl>
    implements _$$DeleteNotificationFailureImplCopyWith<$Res> {
  __$$DeleteNotificationFailureImplCopyWithImpl(
      _$DeleteNotificationFailureImpl _value,
      $Res Function(_$DeleteNotificationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$DeleteNotificationFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteNotificationFailureImpl implements DeleteNotificationFailure {
  const _$DeleteNotificationFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'NotificationFailure.deleteNotificationFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteNotificationFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteNotificationFailureImplCopyWith<_$DeleteNotificationFailureImpl>
      get copyWith => __$$DeleteNotificationFailureImplCopyWithImpl<
          _$DeleteNotificationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) getNotificationFailure,
    required TResult Function(String failureMessage) deleteNotificationFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return deleteNotificationFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? getNotificationFailure,
    TResult? Function(String failureMessage)? deleteNotificationFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return deleteNotificationFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? getNotificationFailure,
    TResult Function(String failureMessage)? deleteNotificationFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (deleteNotificationFailure != null) {
      return deleteNotificationFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetNotificationFailure value)
        getNotificationFailure,
    required TResult Function(DeleteNotificationFailure value)
        deleteNotificationFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return deleteNotificationFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetNotificationFailure value)? getNotificationFailure,
    TResult? Function(DeleteNotificationFailure value)?
        deleteNotificationFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return deleteNotificationFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetNotificationFailure value)? getNotificationFailure,
    TResult Function(DeleteNotificationFailure value)?
        deleteNotificationFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (deleteNotificationFailure != null) {
      return deleteNotificationFailure(this);
    }
    return orElse();
  }
}

abstract class DeleteNotificationFailure implements NotificationFailure {
  const factory DeleteNotificationFailure(final String failureMessage) =
      _$DeleteNotificationFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteNotificationFailureImplCopyWith<_$DeleteNotificationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnexpectedFailureImplCopyWith<$Res>
    implements $NotificationFailureCopyWith<$Res> {
  factory _$$UnexpectedFailureImplCopyWith(_$UnexpectedFailureImpl value,
          $Res Function(_$UnexpectedFailureImpl) then) =
      __$$UnexpectedFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$UnexpectedFailureImplCopyWithImpl<$Res>
    extends _$NotificationFailureCopyWithImpl<$Res, _$UnexpectedFailureImpl>
    implements _$$UnexpectedFailureImplCopyWith<$Res> {
  __$$UnexpectedFailureImplCopyWithImpl(_$UnexpectedFailureImpl _value,
      $Res Function(_$UnexpectedFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$UnexpectedFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UnexpectedFailureImpl implements UnexpectedFailure {
  const _$UnexpectedFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'NotificationFailure.unexpectedFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnexpectedFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnexpectedFailureImplCopyWith<_$UnexpectedFailureImpl> get copyWith =>
      __$$UnexpectedFailureImplCopyWithImpl<_$UnexpectedFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) getNotificationFailure,
    required TResult Function(String failureMessage) deleteNotificationFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return unexpectedFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? getNotificationFailure,
    TResult? Function(String failureMessage)? deleteNotificationFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return unexpectedFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? getNotificationFailure,
    TResult Function(String failureMessage)? deleteNotificationFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (unexpectedFailure != null) {
      return unexpectedFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetNotificationFailure value)
        getNotificationFailure,
    required TResult Function(DeleteNotificationFailure value)
        deleteNotificationFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return unexpectedFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetNotificationFailure value)? getNotificationFailure,
    TResult? Function(DeleteNotificationFailure value)?
        deleteNotificationFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return unexpectedFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetNotificationFailure value)? getNotificationFailure,
    TResult Function(DeleteNotificationFailure value)?
        deleteNotificationFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (unexpectedFailure != null) {
      return unexpectedFailure(this);
    }
    return orElse();
  }
}

abstract class UnexpectedFailure implements NotificationFailure {
  const factory UnexpectedFailure(final String failureMessage) =
      _$UnexpectedFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of NotificationFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnexpectedFailureImplCopyWith<_$UnexpectedFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
