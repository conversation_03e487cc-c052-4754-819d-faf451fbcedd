import 'package:fpdart/fpdart.dart';

import '../failure/notification_failure.dart';
import 'package:timezone/timezone.dart' as tz;
import '../model/notification_model.dart';


abstract class ScheduledNotificationsFacade {
  Future<Either<NotificationFailure, Unit>> scheduleNotificationsWeekly({
    required List<String> daysToBeNotified,
    required List<String> timeOfDay,
    required String body,
    required String notificationGroupId,
  });
  //daily
  Future<Either<NotificationFailure, Unit>> scheduleNotificationsDaily({
    required List<String> timeOfDay,
    required String body,
    required String notificationGroupId,
  });
  //monthly
  Future<Either<NotificationFailure, Unit>> scheduleNotificationsMonthly({
    required tz.TZDateTime dateTime,
    required String body,
    required String notificationGroupId,
  });

  Future<Either<NotificationFailure, Unit>> disableNotificationGroup(String notificationGroupId);

  Future<Either<NotificationFailure, Unit>> scheduleSingleNotification({
    required String body,
    required tz.TZDateTime dateTime,
    required String notificationId,
    required String notificationType,
    required String title,
    required String? payload, required bool isForeground,
  });


}