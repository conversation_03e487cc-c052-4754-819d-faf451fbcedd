import 'package:flutter/cupertino.dart';
import 'package:fpdart/fpdart.dart';

import '../failure/notification_failure.dart';
import '../model/notification_model.dart';


abstract class NotificationFacade {
  Future<Either<NotificationFailure, Unit>> scheduleNotification(NotificationModel notification,String frequency,{Widget? widget});
    // Future<Either<NotificationFailure, Unit>> scheduleSingleNotification(NotificationModel notification,String frequency,{Widget? widget});
  // Future<Either<NotificationFailure, Unit>> showNotification(NotificationModel notification);
  Future<Either<NotificationFailure, Unit>> cancelNotification(int id);
  Future<Either<NotificationFailure, Unit>> initialize(BuildContext context);

  Future<Either<NotificationFailure, List<NotificationModel>>> getNotifications();
  Future<Either<NotificationFailure, Unit>> deleteNotification(int id);
  Future<Either<NotificationFailure, Unit>> deleteAllNotifications();

  Future<Either<NotificationFailure, Unit>> checkForNotifications(BuildContext context);

  Future<Either<NotificationFailure, Unit>> scheduleWorkManagerNotification(NotificationModel notificationModel, String s);
  Stream<Either<NotificationFailure, NotificationModel>> streamNotifications();



}