import 'dart:convert';

import 'package:fpdart/fpdart.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import '../domain/facade/notifications_facade.dart';
import '../domain/facade/scheduled_notifications_facade.dart';
import '../domain/failure/notification_failure.dart';
import '../domain/model/notification_model.dart';
import 'notification_repository.dart';
import 'package:intl/intl.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: ScheduledNotificationsFacade)
class ScheduledNotificationRepository implements ScheduledNotificationsFacade {
  final NotificationFacade _localNotificationService; // Your platform-specific notification service
  ScheduledNotificationRepository(this._localNotificationService);

  @override
  Future<Either<NotificationFailure, Unit>> scheduleNotificationsWeekly({
    required List<String> daysToBeNotified,
    required List<String> timeOfDay,
    required String body,
    required String notificationGroupId,
  }) async {
    try {
      int count = 0;
      print('Scheduling notifications for group ID: $notificationGroupId');
      List<NotificationModel> notifications = [];

      // Iterate through each day and time to schedule notifications
      for (String day in daysToBeNotified) {
        for (String time in timeOfDay) {
          count++;
          print('Scheduling notification $count');
          // Create a DateTime from day and time
          DateTime? scheduleTime = _parseDayAndTime(day, time);
          print('scheduleTime: $scheduleTime');

          if (scheduleTime != null) {
            // Generate a unique notification ID
            int notificationId = _generateNotificationId(day, time);

            // Create a NotificationModel object
            NotificationModel notificationModel = NotificationModel(
              id: notificationId,
              title:
                  'Reminder', // You can modify the title as per your use case
              body: body,
              scheduleTime: tz.TZDateTime.from(scheduleTime, tz.local),
            );

            // Schedule the notification using _localNotificationService
            try {
              await _localNotificationService.scheduleNotification(
                  notificationModel, 'weekly');
            } catch (e, stackTrace) {
              print('Error: $e');
              print('Stack trace: $stackTrace');
              return Left(NotificationFailure.unexpectedFailure(e.toString()));
            }

            // Save the notification ID in the list
            notifications.add(notificationModel);
            print('notificationModel: ${notificationModel.toJson()}');
          }
        }
      }

      SharedPreferences _sharedPreferences =
          await SharedPreferences.getInstance();
      _sharedPreferences.setStringList('notifications_$notificationGroupId',
          notifications.map((n) => jsonEncode(n.toJson())).toList());

      return Right(unit);
    } catch (e) {
      return Left(NotificationFailure.unexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<NotificationFailure, Unit>> scheduleNotificationsDaily({
    required List<String> timeOfDay,
    required String body,
    required String notificationGroupId,
  }) async {
    try {
      int count = 0;
      print(
          'Scheduling daily notifications for group ID: $notificationGroupId');
      List<NotificationModel> notifications = [];

      // Iterate through each time to schedule daily notifications
      for (String time in timeOfDay) {
        count++;
        print('Scheduling notification $count');

        // Assuming the notification is for today, adjust the DateTime accordingly
        DateTime now = DateTime.now();
        DateTime? scheduleTime = _parseTime(time) ?? DateTime.now();

        if (scheduleTime.isBefore(now)) {
          // If the scheduled time is before now, schedule for tomorrow
          scheduleTime = scheduleTime.add(Duration(days: 1));
        }
        print('scheduleTime: $scheduleTime');

        // Generate a unique notification ID
        int notificationId = _generateNotificationId('daily', time);

        // Create a NotificationModel object
        NotificationModel notificationModel = NotificationModel(
          id: notificationId,
          title:
              'Daily Reminder', // You can modify the title as per your use case
          body: body,
          scheduleTime: tz.TZDateTime.from(scheduleTime, tz.local),
        );

        // Schedule the notification using _localNotificationService
        try {
          await _localNotificationService.scheduleNotification(
              notificationModel, 'daily');
        } catch (e, stackTrace) {
          print('Error: $e');
          print('Stack trace: $stackTrace');
          return Left(NotificationFailure.unexpectedFailure(e.toString()));
        }

        // Save the notification ID in the list
        notifications.add(notificationModel);
        print('notificationModel: ${notificationModel.toJson()}');
      }

      SharedPreferences _sharedPreferences =
          await SharedPreferences.getInstance();
      _sharedPreferences.setStringList('notifications_$notificationGroupId',
          notifications.map((n) => jsonEncode(n.toJson())).toList());

      return Right(unit);
    } catch (e) {
      return Left(NotificationFailure.unexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<NotificationFailure, Unit>> scheduleNotificationsMonthly({
    required tz.TZDateTime dateTime,
    required String body,
    required String notificationGroupId,
  }) async {
    try {
      int notificationId =
          _generateNotificationId('monthly', dateTime.toString());

      // Create a NotificationModel object
      NotificationModel notificationModel = NotificationModel(
        id: notificationId,
        title:
            'Monthly Reminder', // You can modify the title as per your use case
        body: body,
        scheduleTime: tz.TZDateTime.from(dateTime, tz.local),
      );

      // Schedule the notification using _localNotificationService
      try {
        await _localNotificationService.scheduleNotification(
            notificationModel, 'monthly');
      } catch (e, stackTrace) {
        print('Error: $e');
        print('Stack trace: $stackTrace');
        return Left(NotificationFailure.unexpectedFailure(e.toString()));
      }

      SharedPreferences _sharedPreferences =
          await SharedPreferences.getInstance();
      _sharedPreferences.setStringList('notifications_$notificationGroupId',
          [jsonEncode(notificationModel.toJson())]);

      print('notificationModel: ${notificationModel.toJson()}');

      return Right(unit);
    } catch (e) {
      return Left(NotificationFailure.unexpectedFailure(e.toString()));
    }
  }

  int _generateNotificationId(String day, String time) {
    // Generate a unique ID based on the day and time
    return day.hashCode ^ time.hashCode;
  }

  @override
  Future<Either<NotificationFailure, Unit>> disableNotificationGroup(
      String notificationGroupId) async {
    try {
      SharedPreferences _sharedPreferences =
          await SharedPreferences.getInstance();

      // Retrieve the list of notification JSON strings under the group ID
      List<String>? notificationJsonList = _sharedPreferences
          .getStringList('notifications_$notificationGroupId');

      if (notificationJsonList != null) {
        for (String notificationJson in notificationJsonList) {
          // Decode the JSON string to get the NotificationModel
          Map<String, dynamic> notificationMap = jsonDecode(notificationJson);
          NotificationModel notificationModel =
              NotificationModel.fromJson(notificationMap);

          // Cancel each notification using its ID
          await _localNotificationService
              .cancelNotification(notificationModel.id!);
        }

        // Remove the group ID from SharedPreferences
        _sharedPreferences.remove('notifications_$notificationGroupId');
      }

      return Right(unit);
    } catch (e) {
      return Left(NotificationFailure.unexpectedFailure(e.toString()));
    }
  }
  // @override
  // Future<List<NotificationModel>> getScheduledNotifications() async {
  //   List<String>? storedNotifications = _sharedPreferences.getStringList('scheduled_notifications');
  //   if (storedNotifications == null) return [];
  //
  //   // Fetch notification details based on stored IDs and parse JSON back into NotificationModel
  //   return storedNotifications.map((id) {
  //     String? notificationJson = _sharedPreferences.getString(id);
  //     if (notificationJson != null) {
  //       Map<String, dynamic> json = jsonDecode(notificationJson);
  //       return NotificationModel.fromJson(json);
  //     }
  //     return NotificationModel(id: id);
  //   }).toList();
  // }

  DateTime? _parseDayAndTime(String day, String time) {
    print('day: $day');
    print('time: $time');

    // Get current time
    final now = DateTime.now();

    // Clean up the time string
    time = time.replaceAll(RegExp(r'[\u00A0\s]'), '').toUpperCase();

    // Manually parse the time string
    RegExp timeRegex = RegExp(r'^(\d{1,2}):(\d{2})(AM|PM)$');
    Match? match = timeRegex.firstMatch(time);

    if (match == null) {
      print('Error: Invalid time format');
      return null;
    }

    int hour = int.parse(match.group(1)!);
    int minute = int.parse(match.group(2)!);
    String period = match.group(3)!;

    // Adjust hour for PM
    if (period == 'PM' && hour != 12) {
      hour += 12;
    } else if (period == 'AM' && hour == 12) {
      hour = 0;
    }

    // Map the day abbreviations to DateTime weekday values (MON -> 1, TUE -> 2, etc.)
    final dayToWeekday = {
      'MON': DateTime.monday,
      'TUE': DateTime.tuesday,
      'WED': DateTime.wednesday,
      'THU': DateTime.thursday,
      'FRI': DateTime.friday,
      'SAT': DateTime.saturday,
      'SUN': DateTime.sunday,
    };

    // Get the corresponding weekday integer for the given day
    int? targetWeekday = dayToWeekday[day.toUpperCase()];
    print('targetWeekday: $targetWeekday');
    if (targetWeekday == null) return null; // Return null if the day is invalid

    // Combine the next day with the parsed time
    DateTime scheduledDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      hour,
      minute,
    );

    print('Parsed DateTime: $scheduledDateTime');
    return scheduledDateTime;
  }

  //parse time
  DateTime? _parseTime(String time) {
    final now = DateTime.now();

    // Clean up the time string
    time = time.replaceAll(RegExp(r'[\u00A0\s]'), '').toUpperCase();

    // Manually parse the time string
    RegExp timeRegex = RegExp(r'^(\d{1,2}):(\d{2})(AM|PM)$');
    Match? match = timeRegex.firstMatch(time);

    if (match == null) {
      print('Error: Invalid time format');
      return null;
    }

    int hour = int.parse(match.group(1)!);
    int minute = int.parse(match.group(2)!);
    String period = match.group(3)!;
    // Adjust hour for PM
    if (period == 'PM' && hour != 12) {
      hour += 12;
    } else if (period == 'AM' && hour == 12) {
      hour = 0;
    }

    return DateTime(now.year, now.month, now.day, hour, minute);
  }

  @override
  Future<Either<NotificationFailure, Unit>> scheduleSingleNotification({
    required String body,
    required tz.TZDateTime dateTime,
    required String notificationId, // We'll ignore this parameter
    required String notificationType,
    required bool isForeground,
    String? payload,
  }) async {
    try {
      print(
          'Scheduling single notification===========================================================================$payload');

      // Generate notification ID based on type and time
      int notificationIdInt = _generateNotificationId(
          'single', dateTime.millisecondsSinceEpoch.toString());

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String typeKey = 'notifications_type_$notificationType';
      print('typeKey: $typeKey');

      // Get current notifications for this type
      List<String> typeNotificationsJson = prefs.getStringList(typeKey) ?? [];
      print('typeNotificationsJson: $typeNotificationsJson');
      List<NotificationModel> typeNotifications = typeNotificationsJson
          .map((json) {
            try {
              return NotificationModel.fromJson(jsonDecode(json));
            } catch (e) {
              print('Error parsing notification JSON: $e');
              return null;
            }
          })
          .whereType<NotificationModel>() // Filter out null values
          .toList();
      print('typeNotifications: ${typeNotifications.length}');

      // Check if a similar notification exists within the next 5 seconds or has been scheduled in the past 5 seconds
      bool similarNotificationExists = typeNotifications.any((existing) =>
          existing.scheduleTime != null &&
          (dateTime.difference(existing.scheduleTime!).inSeconds.abs() < 5));
      print('similarNotificationExists: $similarNotificationExists');
      if (similarNotificationExists) {
        print(
            'Similar notification already scheduled within 5 seconds timeframe');
        return const Right(unit);
      }

      // Create new notification model
      NotificationModel notificationModel = NotificationModel(
        id: notificationIdInt,
        title:
            'Therapy Reminder', // You can modify the title as per your use case
        body: body,
        scheduleTime: tz.TZDateTime.from(dateTime, tz.local),
        notificationType: notificationType,
        receivedAt: tz.TZDateTime.now(tz.local),
        payload: payload,
      );

      // Schedule the notification
      await _localNotificationService.scheduleNotification(
          notificationModel, 'single');
      if(isForeground) {
        await _localNotificationService.scheduleWorkManagerNotification(
            notificationModel, 'single');
      }

      // Add new notification to the list
      typeNotifications.add(notificationModel);

      // Sort by schedule time, newest first
      typeNotifications.sort((a, b) =>
          b.scheduleTime != null && a.scheduleTime != null
              ? b.scheduleTime!.compareTo(a.scheduleTime!)
              : 0);

      // If the list exceeds 5, remove the oldest notification
      if (typeNotifications.length > 5) {
        NotificationModel oldestNotification = typeNotifications.removeLast();
        if (oldestNotification.id != null) {
          await _localNotificationService
              .cancelNotification(oldestNotification.id!);
        }
      }

      // Save updated list
      typeNotificationsJson = typeNotifications
          .map((notification) => jsonEncode(notification.toJson()))
          .toList();
      await prefs.setStringList(typeKey, typeNotificationsJson);

      return const Right(unit);
    } catch (e) {
      print('Error scheduling single notification: $e');
      return Left(NotificationFailure.unexpectedFailure(e.toString()));
    }
  }
}
