// notification_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../application/notification_bloc/notification_bloc.dart';

class NotificationPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notifications')),
      body: BlocBuilder<NotificationBloc, NotificationState>(
        builder: (context, state) {
          return state.map(
            initial: (_) => const Center(child: Text('Initial State')),
            loading: (_) => const Center(child: const CircularProgressIndicator()),
            success: (_) => const Center(child: Text('Success')),
            failure: (failure) => Center(child: Text('Failure: ${failure.failure}')),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Trigger notification events
          context.read<NotificationBloc>().add( NotificationEvent.initialize(context));
        },
        child: const Icon(Icons.notifications),
      ),
    );
  }
}
