import 'package:flutter_test/flutter_test.dart';
import 'package:help_center/domain/model/help_center_model.dart';

void main() {
  group('HelpCenterVideoModel', () {
    test('toJson and fromJson', () {
      final model = HelpCenterVideoModel(
        id: '1',
        title: 'Test Video',
        url: 'http://example.com',
        priority: 1,
        thumbnailUrl: 'http://example.com/thumbnail.jpg',
      );

      final json = model.toJson();
      expect(json['id'], '1');
      expect(json['title'], 'Test Video');
      expect(json['url'], 'http://example.com');
      expect(json['priority'], 1);
      expect(json['thumbnailUrl'], 'http://example.com/thumbnail.jpg');

      final newModel = HelpCenterVideoModel.fromJson(json);
      expect(newModel.id, '1');
      expect(newModel.title, 'Test Video');
      expect(newModel.url, 'http://example.com');
      expect(newModel.priority, 1);
      expect(newModel.thumbnailUrl, 'http://example.com/thumbnail.jpg');
    });

    test('fromJson with missing fields', () {
      final json = {
        'id': '1',
        'title': 'Test Video',
      };

      final model = HelpCenterVideoModel.fromJson(json);
      expect(model.id, '1');
      expect(model.title, 'Test Video');
      expect(model.url, isNull);
      expect(model.priority, isNull);
      expect(model.thumbnailUrl, isNull);
    });

    test('fromJson with invalid data', () {
      final json = {
        'id': 1,
        'title': 123,
        'url': true,
        'priority': 'high',
        'thumbnailUrl': 456,
      };

      expect(() => HelpCenterVideoModel.fromJson(json), throwsA(isA<TypeError>()));
    });
  });

  group('HelpCenterFaqModel', () {
    test('toJson and fromJson', () {
      final model = HelpCenterFaqModel(
        id: '1',
        question: 'Test Question',
        answer: 'Test Answer',
        priority: 1,
      );

      final json = model.toJson();
      expect(json['id'], '1');
      expect(json['question'], 'Test Question');
      expect(json['answer'], 'Test Answer');
      expect(json['priority'], 1);

      final newModel = HelpCenterFaqModel.fromJson(json);
      expect(newModel.id, '1');
      expect(newModel.question, 'Test Question');
      expect(newModel.answer, 'Test Answer');
      expect(newModel.priority, 1);
    });

    test('fromJson with missing fields', () {
      final json = {
        'id': '1',
        'question': 'Test Question',
      };

      final model = HelpCenterFaqModel.fromJson(json);
      expect(model.id, '1');
      expect(model.question, 'Test Question');
      expect(model.answer, isNull);
      expect(model.priority, isNull);
    });

    test('fromJson with invalid data', () {
      final json = {
        'id': 1,
        'question': 123,
        'answer': true,
        'priority': 'high',
      };

      expect(() => HelpCenterFaqModel.fromJson(json), throwsA(isA<TypeError>()));
    });
  });

  group('HelpCenterContactModel', () {
    test('toJson and fromJson', () {
      final model = HelpCenterContactModel(
        id: '1',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
      );

      final json = model.toJson();
      expect(json['id'], '1');
      expect(json['email'], '<EMAIL>');
      expect(json['phoneNumber'], '1234567890');

      final newModel = HelpCenterContactModel.fromJson(json);
      expect(newModel.id, '1');
      expect(newModel.email, '<EMAIL>');
      expect(newModel.phoneNumber, '1234567890');
    });

    test('fromJson with missing fields', () {
      final json = {
        'id': '1',
        'email': '<EMAIL>',
      };

      final model = HelpCenterContactModel.fromJson(json);
      expect(model.id, '1');
      expect(model.email, '<EMAIL>');
      expect(model.phoneNumber, isNull);
    });

    test('fromJson with invalid data', () {
      final json = {
        'id': 1,
        'email': 123,
        'phoneNumber': true,
      };

      expect(() => HelpCenterContactModel.fromJson(json), throwsA(isA<TypeError>()));
    });
  });
}