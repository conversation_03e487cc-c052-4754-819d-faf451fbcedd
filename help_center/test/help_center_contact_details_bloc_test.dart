import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fpdart/fpdart.dart';
import 'package:help_center/application/help_center_contact_details_bloc/help_center_contact_details_bloc.dart';
import 'package:help_center/domain/facade/help_center_facade.dart';
import 'package:help_center/domain/failure/help_center_failure.dart';
import 'package:help_center/domain/model/help_center_model.dart';
import 'package:mocktail/mocktail.dart';

class MockHelpCenterFacade extends Mock implements HelpCenterFacade {}

void main() {
  late HelpCenterContactDetailsBloc bloc;
  late MockHelpCenterFacade mockHelpCenterFacade;

  setUp(() {
    mockHelpCenterFacade = MockHelpCenterFacade();
    bloc = HelpCenterContactDetailsBloc(mockHelpCenterFacade);
  });

  test('initial state is HelpCenterContactDetailsState.initial', () {
    expect(bloc.state, equals(const HelpCenterContactDetailsState.initial()));
  });

  blocTest<HelpCenterContactDetailsBloc, HelpCenterContactDetailsState>(
    'emits [loadingContactDetails, successContactDetails] when GetContactDetails is added and succeeds',
    build: () {
      when(() => mockHelpCenterFacade.getContactDetails())
          .thenAnswer((_) async => Right(HelpCenterContactModel()));
      return bloc;
    },
    act: (bloc) => bloc.add(const GetContactDetails()),
    expect: () => [
      isA<LoadingContactDetails>(),
      isA<SuccessContactDetails>().having((state) => state.contact, 'contact', isA<HelpCenterContactModel>()),
    ],
  );

  blocTest<HelpCenterContactDetailsBloc, HelpCenterContactDetailsState>(
    'emits [loadingContactDetails, failureContactDetails] when GetContactDetails is added and fails',
    build: () {
      when(() => mockHelpCenterFacade.getContactDetails())
          .thenAnswer((_) async => Left(HelpCenterFailure.getContactDetailsFailure('Failed to get contact details')));
      return bloc;
    },
    act: (bloc) => bloc.add(const GetContactDetails()),
    expect: () => [
      isA<LoadingContactDetails>(),
      isA<FailureContactDetails>().having((state) => state.failure, 'failure', isA<HelpCenterFailure>()),
    ],
  );
}