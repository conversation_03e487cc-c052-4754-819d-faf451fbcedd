// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:help_center/application/help_center_contact_details_bloc/help_center_contact_details_bloc.dart'
    as _i902;
import 'package:help_center/application/help_center_faq_bloc/help_center_faq_bloc.dart'
    as _i711;
import 'package:help_center/application/help_center_videos_bloc/help_center_videos_bloc.dart'
    as _i899;
import 'package:help_center/domain/facade/help_center_facade.dart' as _i810;
import 'package:help_center/repository/help_center_repository.dart' as _i990;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.lazySingleton<_i810.HelpCenterFacade>(
        () => _i990.HelpCenterRepository());
    gh.factory<_i899.HelpCenterVideosBloc>(
        () => _i899.HelpCenterVideosBloc(gh<_i810.HelpCenterFacade>()));
    gh.factory<_i902.HelpCenterContactDetailsBloc>(
        () => _i902.HelpCenterContactDetailsBloc(gh<_i810.HelpCenterFacade>()));
    gh.factory<_i711.HelpCenterFaqBloc>(
        () => _i711.HelpCenterFaqBloc(gh<_i810.HelpCenterFacade>()));
    return this;
  }
}
