// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'help_center_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HelpCenterVideoModel _$HelpCenterVideoModelFromJson(
        Map<String, dynamic> json) =>
    HelpCenterVideoModel(
      id: json['id'] as String?,
      title: json['title'] as String?,
      url: json['url'] as String?,
      priority: (json['priority'] as num?)?.toInt(),
      thumbnailUrl: json['thumbnailUrl'] as String?,
    );

Map<String, dynamic> _$HelpCenterVideoModelToJson(
        HelpCenterVideoModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'url': instance.url,
      'priority': instance.priority,
      'thumbnailUrl': instance.thumbnailUrl,
    };

HelpCenterFaqModel _$HelpCenterFaqModelFromJson(Map<String, dynamic> json) =>
    HelpCenterFaqModel(
      id: json['id'] as String?,
      question: json['question'] as String?,
      answer: json['answer'] as String?,
      priority: (json['priority'] as num?)?.toInt(),
    );

Map<String, dynamic> _$HelpCenterFaqModelToJson(HelpCenterFaqModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'question': instance.question,
      'answer': instance.answer,
      'priority': instance.priority,
    };

HelpCenterContactModel _$HelpCenterContactModelFromJson(
        Map<String, dynamic> json) =>
    HelpCenterContactModel(
      id: json['id'] as String?,
      email: json['email'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
    );

Map<String, dynamic> _$HelpCenterContactModelToJson(
        HelpCenterContactModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
    };
