import 'package:json_annotation/json_annotation.dart';

part 'help_center_model.g.dart';

// This class represents a help center model with JSON serialization.
@JsonSerializable(explicitToJson: true)
class HelpCenterVideoModel {
  // Video ID
  String? id;
  // Video title
  String? title;
  // Video url
  String? url;
  //priority
  int? priority;
  String? thumbnailUrl;

  // Constructor for the HelpCenterModelVideo class
  HelpCenterVideoModel({this.id, this.title, this.url,this.priority,this.thumbnailUrl});

  // Method to convert a HelpCenterModelVideo instance into a JSON map.
  Map<String, dynamic> toJson() => _$HelpCenterVideoModelToJson(this);

  // Factory constructor to create a HelpCenterModelVideo instance from a JSON map.
  factory HelpCenterVideoModel.fromJson(Map<String, dynamic> json) =>
      _$HelpCenterVideoModelFromJson(json);
}

// This class represents a help center FAQ model with JSON serialization.
@JsonSerializable(explicitToJson: true)
class HelpCenterFaqModel {
  // FAQ ID
  String? id;
  // FAQ question
  String? question;
  // FAQ answer
  String? answer;
  //priority
  int? priority;

  // Constructor for the HelpCenterModelFaq class
  HelpCenterFaqModel({this.id, this.question, this.answer,this.priority});

  // Method to convert a HelpCenterModelFaq instance into a JSON map.
  Map<String, dynamic> toJson() => _$HelpCenterFaqModelToJson(this);

  // Factory constructor to create a HelpCenterModelFaq instance from a JSON map.
  factory HelpCenterFaqModel.fromJson(Map<String, dynamic> json) =>
      _$HelpCenterFaqModelFromJson(json);
}

// This class represents a help center contact model with JSON serialization.
@JsonSerializable(explicitToJson: true)
class HelpCenterContactModel {
  // Contact ID
  String? id;
  // Contact email
  String? email;
  // Contact phone number
  String? phoneNumber;

  // Constructor for the HelpCenterModelContact class
  HelpCenterContactModel({this.id, this.email, this.phoneNumber});

  // Method to convert a HelpCenterModelContact instance into a JSON map.
  Map<String, dynamic> toJson() => _$HelpCenterContactModelToJson(this);

  // Factory constructor to create a HelpCenterModelContact instance from a JSON map.
  factory HelpCenterContactModel.fromJson(Map<String, dynamic> json) =>
      _$HelpCenterContactModelFromJson(json);
}