import 'package:fpdart/fpdart.dart';
import 'package:help_center/domain/failure/help_center_failure.dart';

import '../model/help_center_model.dart';

abstract class HelpCenterFacade {
  // Get all help center videos
  Stream<Either<HelpCenterFailure,List<HelpCenterVideoModel?>>> getHelpCenterVideos();
  // Get all help center FAQs
  Stream<Either<HelpCenterFailure,List<HelpCenterFaqModel?>>> getHelpCenterFaq();
  // Get help center contact details
  Future<Either<HelpCenterFailure,HelpCenterContactModel?>> getContactDetails();

}