import 'package:freezed_annotation/freezed_annotation.dart';
part 'help_center_failure.freezed.dart';

@freezed
class HelpCenterFailure with _$HelpCenterFailure {
  // Failure representing an error when trying to get the help center videos
  const factory HelpCenterFailure.getHelpCenterVideosFailure(
      String failureMessage) = GetHelpCenterVideosFailure;

  // Failure representing an error when trying to get the help center FAQs
  const factory HelpCenterFailure.getHelpCenterFaqFailure(
      String failureMessage) = GetHelpCenterFaqFailure;

  // Failure representing an error when trying to get the help center contact details
  const factory HelpCenterFailure.getContactDetailsFailure(
      String failureMessage) = GetContactDetailsFailure;

}