// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'help_center_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HelpCenterFailure {
  String get failureMessage => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) getHelpCenterVideosFailure,
    required TResult Function(String failureMessage) getHelpCenterFaqFailure,
    required TResult Function(String failureMessage) getContactDetailsFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? getHelpCenterVideosFailure,
    TResult? Function(String failureMessage)? getHelpCenterFaqFailure,
    TResult? Function(String failureMessage)? getContactDetailsFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? getHelpCenterVideosFailure,
    TResult Function(String failureMessage)? getHelpCenterFaqFailure,
    TResult Function(String failureMessage)? getContactDetailsFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetHelpCenterVideosFailure value)
        getHelpCenterVideosFailure,
    required TResult Function(GetHelpCenterFaqFailure value)
        getHelpCenterFaqFailure,
    required TResult Function(GetContactDetailsFailure value)
        getContactDetailsFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetHelpCenterVideosFailure value)?
        getHelpCenterVideosFailure,
    TResult? Function(GetHelpCenterFaqFailure value)? getHelpCenterFaqFailure,
    TResult? Function(GetContactDetailsFailure value)? getContactDetailsFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetHelpCenterVideosFailure value)?
        getHelpCenterVideosFailure,
    TResult Function(GetHelpCenterFaqFailure value)? getHelpCenterFaqFailure,
    TResult Function(GetContactDetailsFailure value)? getContactDetailsFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HelpCenterFailureCopyWith<HelpCenterFailure> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpCenterFailureCopyWith<$Res> {
  factory $HelpCenterFailureCopyWith(
          HelpCenterFailure value, $Res Function(HelpCenterFailure) then) =
      _$HelpCenterFailureCopyWithImpl<$Res, HelpCenterFailure>;
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$HelpCenterFailureCopyWithImpl<$Res, $Val extends HelpCenterFailure>
    implements $HelpCenterFailureCopyWith<$Res> {
  _$HelpCenterFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_value.copyWith(
      failureMessage: null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetHelpCenterVideosFailureImplCopyWith<$Res>
    implements $HelpCenterFailureCopyWith<$Res> {
  factory _$$GetHelpCenterVideosFailureImplCopyWith(
          _$GetHelpCenterVideosFailureImpl value,
          $Res Function(_$GetHelpCenterVideosFailureImpl) then) =
      __$$GetHelpCenterVideosFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$GetHelpCenterVideosFailureImplCopyWithImpl<$Res>
    extends _$HelpCenterFailureCopyWithImpl<$Res,
        _$GetHelpCenterVideosFailureImpl>
    implements _$$GetHelpCenterVideosFailureImplCopyWith<$Res> {
  __$$GetHelpCenterVideosFailureImplCopyWithImpl(
      _$GetHelpCenterVideosFailureImpl _value,
      $Res Function(_$GetHelpCenterVideosFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$GetHelpCenterVideosFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetHelpCenterVideosFailureImpl implements GetHelpCenterVideosFailure {
  const _$GetHelpCenterVideosFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'HelpCenterFailure.getHelpCenterVideosFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetHelpCenterVideosFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetHelpCenterVideosFailureImplCopyWith<_$GetHelpCenterVideosFailureImpl>
      get copyWith => __$$GetHelpCenterVideosFailureImplCopyWithImpl<
          _$GetHelpCenterVideosFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) getHelpCenterVideosFailure,
    required TResult Function(String failureMessage) getHelpCenterFaqFailure,
    required TResult Function(String failureMessage) getContactDetailsFailure,
  }) {
    return getHelpCenterVideosFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? getHelpCenterVideosFailure,
    TResult? Function(String failureMessage)? getHelpCenterFaqFailure,
    TResult? Function(String failureMessage)? getContactDetailsFailure,
  }) {
    return getHelpCenterVideosFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? getHelpCenterVideosFailure,
    TResult Function(String failureMessage)? getHelpCenterFaqFailure,
    TResult Function(String failureMessage)? getContactDetailsFailure,
    required TResult orElse(),
  }) {
    if (getHelpCenterVideosFailure != null) {
      return getHelpCenterVideosFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetHelpCenterVideosFailure value)
        getHelpCenterVideosFailure,
    required TResult Function(GetHelpCenterFaqFailure value)
        getHelpCenterFaqFailure,
    required TResult Function(GetContactDetailsFailure value)
        getContactDetailsFailure,
  }) {
    return getHelpCenterVideosFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetHelpCenterVideosFailure value)?
        getHelpCenterVideosFailure,
    TResult? Function(GetHelpCenterFaqFailure value)? getHelpCenterFaqFailure,
    TResult? Function(GetContactDetailsFailure value)? getContactDetailsFailure,
  }) {
    return getHelpCenterVideosFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetHelpCenterVideosFailure value)?
        getHelpCenterVideosFailure,
    TResult Function(GetHelpCenterFaqFailure value)? getHelpCenterFaqFailure,
    TResult Function(GetContactDetailsFailure value)? getContactDetailsFailure,
    required TResult orElse(),
  }) {
    if (getHelpCenterVideosFailure != null) {
      return getHelpCenterVideosFailure(this);
    }
    return orElse();
  }
}

abstract class GetHelpCenterVideosFailure implements HelpCenterFailure {
  const factory GetHelpCenterVideosFailure(final String failureMessage) =
      _$GetHelpCenterVideosFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetHelpCenterVideosFailureImplCopyWith<_$GetHelpCenterVideosFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetHelpCenterFaqFailureImplCopyWith<$Res>
    implements $HelpCenterFailureCopyWith<$Res> {
  factory _$$GetHelpCenterFaqFailureImplCopyWith(
          _$GetHelpCenterFaqFailureImpl value,
          $Res Function(_$GetHelpCenterFaqFailureImpl) then) =
      __$$GetHelpCenterFaqFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$GetHelpCenterFaqFailureImplCopyWithImpl<$Res>
    extends _$HelpCenterFailureCopyWithImpl<$Res, _$GetHelpCenterFaqFailureImpl>
    implements _$$GetHelpCenterFaqFailureImplCopyWith<$Res> {
  __$$GetHelpCenterFaqFailureImplCopyWithImpl(
      _$GetHelpCenterFaqFailureImpl _value,
      $Res Function(_$GetHelpCenterFaqFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$GetHelpCenterFaqFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetHelpCenterFaqFailureImpl implements GetHelpCenterFaqFailure {
  const _$GetHelpCenterFaqFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'HelpCenterFailure.getHelpCenterFaqFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetHelpCenterFaqFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetHelpCenterFaqFailureImplCopyWith<_$GetHelpCenterFaqFailureImpl>
      get copyWith => __$$GetHelpCenterFaqFailureImplCopyWithImpl<
          _$GetHelpCenterFaqFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) getHelpCenterVideosFailure,
    required TResult Function(String failureMessage) getHelpCenterFaqFailure,
    required TResult Function(String failureMessage) getContactDetailsFailure,
  }) {
    return getHelpCenterFaqFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? getHelpCenterVideosFailure,
    TResult? Function(String failureMessage)? getHelpCenterFaqFailure,
    TResult? Function(String failureMessage)? getContactDetailsFailure,
  }) {
    return getHelpCenterFaqFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? getHelpCenterVideosFailure,
    TResult Function(String failureMessage)? getHelpCenterFaqFailure,
    TResult Function(String failureMessage)? getContactDetailsFailure,
    required TResult orElse(),
  }) {
    if (getHelpCenterFaqFailure != null) {
      return getHelpCenterFaqFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetHelpCenterVideosFailure value)
        getHelpCenterVideosFailure,
    required TResult Function(GetHelpCenterFaqFailure value)
        getHelpCenterFaqFailure,
    required TResult Function(GetContactDetailsFailure value)
        getContactDetailsFailure,
  }) {
    return getHelpCenterFaqFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetHelpCenterVideosFailure value)?
        getHelpCenterVideosFailure,
    TResult? Function(GetHelpCenterFaqFailure value)? getHelpCenterFaqFailure,
    TResult? Function(GetContactDetailsFailure value)? getContactDetailsFailure,
  }) {
    return getHelpCenterFaqFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetHelpCenterVideosFailure value)?
        getHelpCenterVideosFailure,
    TResult Function(GetHelpCenterFaqFailure value)? getHelpCenterFaqFailure,
    TResult Function(GetContactDetailsFailure value)? getContactDetailsFailure,
    required TResult orElse(),
  }) {
    if (getHelpCenterFaqFailure != null) {
      return getHelpCenterFaqFailure(this);
    }
    return orElse();
  }
}

abstract class GetHelpCenterFaqFailure implements HelpCenterFailure {
  const factory GetHelpCenterFaqFailure(final String failureMessage) =
      _$GetHelpCenterFaqFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetHelpCenterFaqFailureImplCopyWith<_$GetHelpCenterFaqFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetContactDetailsFailureImplCopyWith<$Res>
    implements $HelpCenterFailureCopyWith<$Res> {
  factory _$$GetContactDetailsFailureImplCopyWith(
          _$GetContactDetailsFailureImpl value,
          $Res Function(_$GetContactDetailsFailureImpl) then) =
      __$$GetContactDetailsFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$GetContactDetailsFailureImplCopyWithImpl<$Res>
    extends _$HelpCenterFailureCopyWithImpl<$Res,
        _$GetContactDetailsFailureImpl>
    implements _$$GetContactDetailsFailureImplCopyWith<$Res> {
  __$$GetContactDetailsFailureImplCopyWithImpl(
      _$GetContactDetailsFailureImpl _value,
      $Res Function(_$GetContactDetailsFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$GetContactDetailsFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetContactDetailsFailureImpl implements GetContactDetailsFailure {
  const _$GetContactDetailsFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'HelpCenterFailure.getContactDetailsFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetContactDetailsFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetContactDetailsFailureImplCopyWith<_$GetContactDetailsFailureImpl>
      get copyWith => __$$GetContactDetailsFailureImplCopyWithImpl<
          _$GetContactDetailsFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) getHelpCenterVideosFailure,
    required TResult Function(String failureMessage) getHelpCenterFaqFailure,
    required TResult Function(String failureMessage) getContactDetailsFailure,
  }) {
    return getContactDetailsFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? getHelpCenterVideosFailure,
    TResult? Function(String failureMessage)? getHelpCenterFaqFailure,
    TResult? Function(String failureMessage)? getContactDetailsFailure,
  }) {
    return getContactDetailsFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? getHelpCenterVideosFailure,
    TResult Function(String failureMessage)? getHelpCenterFaqFailure,
    TResult Function(String failureMessage)? getContactDetailsFailure,
    required TResult orElse(),
  }) {
    if (getContactDetailsFailure != null) {
      return getContactDetailsFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetHelpCenterVideosFailure value)
        getHelpCenterVideosFailure,
    required TResult Function(GetHelpCenterFaqFailure value)
        getHelpCenterFaqFailure,
    required TResult Function(GetContactDetailsFailure value)
        getContactDetailsFailure,
  }) {
    return getContactDetailsFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetHelpCenterVideosFailure value)?
        getHelpCenterVideosFailure,
    TResult? Function(GetHelpCenterFaqFailure value)? getHelpCenterFaqFailure,
    TResult? Function(GetContactDetailsFailure value)? getContactDetailsFailure,
  }) {
    return getContactDetailsFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetHelpCenterVideosFailure value)?
        getHelpCenterVideosFailure,
    TResult Function(GetHelpCenterFaqFailure value)? getHelpCenterFaqFailure,
    TResult Function(GetContactDetailsFailure value)? getContactDetailsFailure,
    required TResult orElse(),
  }) {
    if (getContactDetailsFailure != null) {
      return getContactDetailsFailure(this);
    }
    return orElse();
  }
}

abstract class GetContactDetailsFailure implements HelpCenterFailure {
  const factory GetContactDetailsFailure(final String failureMessage) =
      _$GetContactDetailsFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of HelpCenterFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetContactDetailsFailureImplCopyWith<_$GetContactDetailsFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}
