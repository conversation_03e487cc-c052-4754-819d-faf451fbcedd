part of 'help_center_faq_bloc.dart';



@freezed
class HelpCenterFaqState with _$HelpCenterFaqState {
  const HelpCenterFaqState._(); // Private constructor

  const factory HelpCenterFaqState.initial() = Initial;
  const factory HelpCenterFaqState.loadingFaqs() = LoadingFaqs;
  const factory HelpCenterFaqState.successFaqs(List<HelpCenterFaqModel?> faqs) = SuccessFaqs;
  const factory HelpCenterFaqState.failureFaqs(HelpCenterFailure failure) = FailureFaqs;

  @override
  List<Object?> get props => [runtimeType, ...when(
    initial: () => [],
    loadingFaqs: () => [],
    successFaqs: (faqs) => [faqs],
    failureFaqs: (failure) => [failure],
  )];
}
