// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'help_center_faq_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HelpCenterFaqEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadHelpCenterFaq,
    required TResult Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)
        helpCenterFaqLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadHelpCenterFaq,
    TResult? Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)?
        helpCenterFaqLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadHelpCenterFaq,
    TResult Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)?
        helpCenterFaqLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadHelpCenterFaq value) loadHelpCenterFaq,
    required TResult Function(HelpCenterFaqLoaded value) helpCenterFaqLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadHelpCenterFaq value)? loadHelpCenterFaq,
    TResult? Function(HelpCenterFaqLoaded value)? helpCenterFaqLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadHelpCenterFaq value)? loadHelpCenterFaq,
    TResult Function(HelpCenterFaqLoaded value)? helpCenterFaqLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpCenterFaqEventCopyWith<$Res> {
  factory $HelpCenterFaqEventCopyWith(
          HelpCenterFaqEvent value, $Res Function(HelpCenterFaqEvent) then) =
      _$HelpCenterFaqEventCopyWithImpl<$Res, HelpCenterFaqEvent>;
}

/// @nodoc
class _$HelpCenterFaqEventCopyWithImpl<$Res, $Val extends HelpCenterFaqEvent>
    implements $HelpCenterFaqEventCopyWith<$Res> {
  _$HelpCenterFaqEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpCenterFaqEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadHelpCenterFaqImplCopyWith<$Res> {
  factory _$$LoadHelpCenterFaqImplCopyWith(_$LoadHelpCenterFaqImpl value,
          $Res Function(_$LoadHelpCenterFaqImpl) then) =
      __$$LoadHelpCenterFaqImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadHelpCenterFaqImplCopyWithImpl<$Res>
    extends _$HelpCenterFaqEventCopyWithImpl<$Res, _$LoadHelpCenterFaqImpl>
    implements _$$LoadHelpCenterFaqImplCopyWith<$Res> {
  __$$LoadHelpCenterFaqImplCopyWithImpl(_$LoadHelpCenterFaqImpl _value,
      $Res Function(_$LoadHelpCenterFaqImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFaqEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadHelpCenterFaqImpl implements LoadHelpCenterFaq {
  const _$LoadHelpCenterFaqImpl();

  @override
  String toString() {
    return 'HelpCenterFaqEvent.loadHelpCenterFaq()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadHelpCenterFaqImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadHelpCenterFaq,
    required TResult Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)
        helpCenterFaqLoaded,
  }) {
    return loadHelpCenterFaq();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadHelpCenterFaq,
    TResult? Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)?
        helpCenterFaqLoaded,
  }) {
    return loadHelpCenterFaq?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadHelpCenterFaq,
    TResult Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)?
        helpCenterFaqLoaded,
    required TResult orElse(),
  }) {
    if (loadHelpCenterFaq != null) {
      return loadHelpCenterFaq();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadHelpCenterFaq value) loadHelpCenterFaq,
    required TResult Function(HelpCenterFaqLoaded value) helpCenterFaqLoaded,
  }) {
    return loadHelpCenterFaq(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadHelpCenterFaq value)? loadHelpCenterFaq,
    TResult? Function(HelpCenterFaqLoaded value)? helpCenterFaqLoaded,
  }) {
    return loadHelpCenterFaq?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadHelpCenterFaq value)? loadHelpCenterFaq,
    TResult Function(HelpCenterFaqLoaded value)? helpCenterFaqLoaded,
    required TResult orElse(),
  }) {
    if (loadHelpCenterFaq != null) {
      return loadHelpCenterFaq(this);
    }
    return orElse();
  }
}

abstract class LoadHelpCenterFaq implements HelpCenterFaqEvent {
  const factory LoadHelpCenterFaq() = _$LoadHelpCenterFaqImpl;
}

/// @nodoc
abstract class _$$HelpCenterFaqLoadedImplCopyWith<$Res> {
  factory _$$HelpCenterFaqLoadedImplCopyWith(_$HelpCenterFaqLoadedImpl value,
          $Res Function(_$HelpCenterFaqLoadedImpl) then) =
      __$$HelpCenterFaqLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs});
}

/// @nodoc
class __$$HelpCenterFaqLoadedImplCopyWithImpl<$Res>
    extends _$HelpCenterFaqEventCopyWithImpl<$Res, _$HelpCenterFaqLoadedImpl>
    implements _$$HelpCenterFaqLoadedImplCopyWith<$Res> {
  __$$HelpCenterFaqLoadedImplCopyWithImpl(_$HelpCenterFaqLoadedImpl _value,
      $Res Function(_$HelpCenterFaqLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFaqEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrFaqs = null,
  }) {
    return _then(_$HelpCenterFaqLoadedImpl(
      null == failureOrFaqs
          ? _value.failureOrFaqs
          : failureOrFaqs // ignore: cast_nullable_to_non_nullable
              as Either<HelpCenterFailure, List<HelpCenterFaqModel?>>,
    ));
  }
}

/// @nodoc

class _$HelpCenterFaqLoadedImpl implements HelpCenterFaqLoaded {
  const _$HelpCenterFaqLoadedImpl(this.failureOrFaqs);

  @override
  final Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs;

  @override
  String toString() {
    return 'HelpCenterFaqEvent.helpCenterFaqLoaded(failureOrFaqs: $failureOrFaqs)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HelpCenterFaqLoadedImpl &&
            (identical(other.failureOrFaqs, failureOrFaqs) ||
                other.failureOrFaqs == failureOrFaqs));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrFaqs);

  /// Create a copy of HelpCenterFaqEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HelpCenterFaqLoadedImplCopyWith<_$HelpCenterFaqLoadedImpl> get copyWith =>
      __$$HelpCenterFaqLoadedImplCopyWithImpl<_$HelpCenterFaqLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadHelpCenterFaq,
    required TResult Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)
        helpCenterFaqLoaded,
  }) {
    return helpCenterFaqLoaded(failureOrFaqs);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadHelpCenterFaq,
    TResult? Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)?
        helpCenterFaqLoaded,
  }) {
    return helpCenterFaqLoaded?.call(failureOrFaqs);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadHelpCenterFaq,
    TResult Function(
            Either<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs)?
        helpCenterFaqLoaded,
    required TResult orElse(),
  }) {
    if (helpCenterFaqLoaded != null) {
      return helpCenterFaqLoaded(failureOrFaqs);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadHelpCenterFaq value) loadHelpCenterFaq,
    required TResult Function(HelpCenterFaqLoaded value) helpCenterFaqLoaded,
  }) {
    return helpCenterFaqLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadHelpCenterFaq value)? loadHelpCenterFaq,
    TResult? Function(HelpCenterFaqLoaded value)? helpCenterFaqLoaded,
  }) {
    return helpCenterFaqLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadHelpCenterFaq value)? loadHelpCenterFaq,
    TResult Function(HelpCenterFaqLoaded value)? helpCenterFaqLoaded,
    required TResult orElse(),
  }) {
    if (helpCenterFaqLoaded != null) {
      return helpCenterFaqLoaded(this);
    }
    return orElse();
  }
}

abstract class HelpCenterFaqLoaded implements HelpCenterFaqEvent {
  const factory HelpCenterFaqLoaded(
      final Either<HelpCenterFailure, List<HelpCenterFaqModel?>>
          failureOrFaqs) = _$HelpCenterFaqLoadedImpl;

  Either<HelpCenterFailure, List<HelpCenterFaqModel?>> get failureOrFaqs;

  /// Create a copy of HelpCenterFaqEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HelpCenterFaqLoadedImplCopyWith<_$HelpCenterFaqLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$HelpCenterFaqState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingFaqs,
    required TResult Function(List<HelpCenterFaqModel?> faqs) successFaqs,
    required TResult Function(HelpCenterFailure failure) failureFaqs,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingFaqs,
    TResult? Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult? Function(HelpCenterFailure failure)? failureFaqs,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingFaqs,
    TResult Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult Function(HelpCenterFailure failure)? failureFaqs,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingFaqs value) loadingFaqs,
    required TResult Function(SuccessFaqs value) successFaqs,
    required TResult Function(FailureFaqs value) failureFaqs,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingFaqs value)? loadingFaqs,
    TResult? Function(SuccessFaqs value)? successFaqs,
    TResult? Function(FailureFaqs value)? failureFaqs,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingFaqs value)? loadingFaqs,
    TResult Function(SuccessFaqs value)? successFaqs,
    TResult Function(FailureFaqs value)? failureFaqs,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpCenterFaqStateCopyWith<$Res> {
  factory $HelpCenterFaqStateCopyWith(
          HelpCenterFaqState value, $Res Function(HelpCenterFaqState) then) =
      _$HelpCenterFaqStateCopyWithImpl<$Res, HelpCenterFaqState>;
}

/// @nodoc
class _$HelpCenterFaqStateCopyWithImpl<$Res, $Val extends HelpCenterFaqState>
    implements $HelpCenterFaqStateCopyWith<$Res> {
  _$HelpCenterFaqStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$HelpCenterFaqStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl extends Initial {
  const _$InitialImpl() : super._();

  @override
  String toString() {
    return 'HelpCenterFaqState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingFaqs,
    required TResult Function(List<HelpCenterFaqModel?> faqs) successFaqs,
    required TResult Function(HelpCenterFailure failure) failureFaqs,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingFaqs,
    TResult? Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult? Function(HelpCenterFailure failure)? failureFaqs,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingFaqs,
    TResult Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult Function(HelpCenterFailure failure)? failureFaqs,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingFaqs value) loadingFaqs,
    required TResult Function(SuccessFaqs value) successFaqs,
    required TResult Function(FailureFaqs value) failureFaqs,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingFaqs value)? loadingFaqs,
    TResult? Function(SuccessFaqs value)? successFaqs,
    TResult? Function(FailureFaqs value)? failureFaqs,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingFaqs value)? loadingFaqs,
    TResult Function(SuccessFaqs value)? successFaqs,
    TResult Function(FailureFaqs value)? failureFaqs,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial extends HelpCenterFaqState {
  const factory Initial() = _$InitialImpl;
  const Initial._() : super._();
}

/// @nodoc
abstract class _$$LoadingFaqsImplCopyWith<$Res> {
  factory _$$LoadingFaqsImplCopyWith(
          _$LoadingFaqsImpl value, $Res Function(_$LoadingFaqsImpl) then) =
      __$$LoadingFaqsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingFaqsImplCopyWithImpl<$Res>
    extends _$HelpCenterFaqStateCopyWithImpl<$Res, _$LoadingFaqsImpl>
    implements _$$LoadingFaqsImplCopyWith<$Res> {
  __$$LoadingFaqsImplCopyWithImpl(
      _$LoadingFaqsImpl _value, $Res Function(_$LoadingFaqsImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingFaqsImpl extends LoadingFaqs {
  const _$LoadingFaqsImpl() : super._();

  @override
  String toString() {
    return 'HelpCenterFaqState.loadingFaqs()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingFaqsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingFaqs,
    required TResult Function(List<HelpCenterFaqModel?> faqs) successFaqs,
    required TResult Function(HelpCenterFailure failure) failureFaqs,
  }) {
    return loadingFaqs();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingFaqs,
    TResult? Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult? Function(HelpCenterFailure failure)? failureFaqs,
  }) {
    return loadingFaqs?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingFaqs,
    TResult Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult Function(HelpCenterFailure failure)? failureFaqs,
    required TResult orElse(),
  }) {
    if (loadingFaqs != null) {
      return loadingFaqs();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingFaqs value) loadingFaqs,
    required TResult Function(SuccessFaqs value) successFaqs,
    required TResult Function(FailureFaqs value) failureFaqs,
  }) {
    return loadingFaqs(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingFaqs value)? loadingFaqs,
    TResult? Function(SuccessFaqs value)? successFaqs,
    TResult? Function(FailureFaqs value)? failureFaqs,
  }) {
    return loadingFaqs?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingFaqs value)? loadingFaqs,
    TResult Function(SuccessFaqs value)? successFaqs,
    TResult Function(FailureFaqs value)? failureFaqs,
    required TResult orElse(),
  }) {
    if (loadingFaqs != null) {
      return loadingFaqs(this);
    }
    return orElse();
  }
}

abstract class LoadingFaqs extends HelpCenterFaqState {
  const factory LoadingFaqs() = _$LoadingFaqsImpl;
  const LoadingFaqs._() : super._();
}

/// @nodoc
abstract class _$$SuccessFaqsImplCopyWith<$Res> {
  factory _$$SuccessFaqsImplCopyWith(
          _$SuccessFaqsImpl value, $Res Function(_$SuccessFaqsImpl) then) =
      __$$SuccessFaqsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<HelpCenterFaqModel?> faqs});
}

/// @nodoc
class __$$SuccessFaqsImplCopyWithImpl<$Res>
    extends _$HelpCenterFaqStateCopyWithImpl<$Res, _$SuccessFaqsImpl>
    implements _$$SuccessFaqsImplCopyWith<$Res> {
  __$$SuccessFaqsImplCopyWithImpl(
      _$SuccessFaqsImpl _value, $Res Function(_$SuccessFaqsImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? faqs = null,
  }) {
    return _then(_$SuccessFaqsImpl(
      null == faqs
          ? _value._faqs
          : faqs // ignore: cast_nullable_to_non_nullable
              as List<HelpCenterFaqModel?>,
    ));
  }
}

/// @nodoc

class _$SuccessFaqsImpl extends SuccessFaqs {
  const _$SuccessFaqsImpl(final List<HelpCenterFaqModel?> faqs)
      : _faqs = faqs,
        super._();

  final List<HelpCenterFaqModel?> _faqs;
  @override
  List<HelpCenterFaqModel?> get faqs {
    if (_faqs is EqualUnmodifiableListView) return _faqs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_faqs);
  }

  @override
  String toString() {
    return 'HelpCenterFaqState.successFaqs(faqs: $faqs)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessFaqsImpl &&
            const DeepCollectionEquality().equals(other._faqs, _faqs));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_faqs));

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessFaqsImplCopyWith<_$SuccessFaqsImpl> get copyWith =>
      __$$SuccessFaqsImplCopyWithImpl<_$SuccessFaqsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingFaqs,
    required TResult Function(List<HelpCenterFaqModel?> faqs) successFaqs,
    required TResult Function(HelpCenterFailure failure) failureFaqs,
  }) {
    return successFaqs(faqs);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingFaqs,
    TResult? Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult? Function(HelpCenterFailure failure)? failureFaqs,
  }) {
    return successFaqs?.call(faqs);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingFaqs,
    TResult Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult Function(HelpCenterFailure failure)? failureFaqs,
    required TResult orElse(),
  }) {
    if (successFaqs != null) {
      return successFaqs(faqs);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingFaqs value) loadingFaqs,
    required TResult Function(SuccessFaqs value) successFaqs,
    required TResult Function(FailureFaqs value) failureFaqs,
  }) {
    return successFaqs(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingFaqs value)? loadingFaqs,
    TResult? Function(SuccessFaqs value)? successFaqs,
    TResult? Function(FailureFaqs value)? failureFaqs,
  }) {
    return successFaqs?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingFaqs value)? loadingFaqs,
    TResult Function(SuccessFaqs value)? successFaqs,
    TResult Function(FailureFaqs value)? failureFaqs,
    required TResult orElse(),
  }) {
    if (successFaqs != null) {
      return successFaqs(this);
    }
    return orElse();
  }
}

abstract class SuccessFaqs extends HelpCenterFaqState {
  const factory SuccessFaqs(final List<HelpCenterFaqModel?> faqs) =
      _$SuccessFaqsImpl;
  const SuccessFaqs._() : super._();

  List<HelpCenterFaqModel?> get faqs;

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessFaqsImplCopyWith<_$SuccessFaqsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureFaqsImplCopyWith<$Res> {
  factory _$$FailureFaqsImplCopyWith(
          _$FailureFaqsImpl value, $Res Function(_$FailureFaqsImpl) then) =
      __$$FailureFaqsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HelpCenterFailure failure});

  $HelpCenterFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureFaqsImplCopyWithImpl<$Res>
    extends _$HelpCenterFaqStateCopyWithImpl<$Res, _$FailureFaqsImpl>
    implements _$$FailureFaqsImplCopyWith<$Res> {
  __$$FailureFaqsImplCopyWithImpl(
      _$FailureFaqsImpl _value, $Res Function(_$FailureFaqsImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureFaqsImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HelpCenterFailure,
    ));
  }

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $HelpCenterFailureCopyWith<$Res> get failure {
    return $HelpCenterFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureFaqsImpl extends FailureFaqs {
  const _$FailureFaqsImpl(this.failure) : super._();

  @override
  final HelpCenterFailure failure;

  @override
  String toString() {
    return 'HelpCenterFaqState.failureFaqs(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureFaqsImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureFaqsImplCopyWith<_$FailureFaqsImpl> get copyWith =>
      __$$FailureFaqsImplCopyWithImpl<_$FailureFaqsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingFaqs,
    required TResult Function(List<HelpCenterFaqModel?> faqs) successFaqs,
    required TResult Function(HelpCenterFailure failure) failureFaqs,
  }) {
    return failureFaqs(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingFaqs,
    TResult? Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult? Function(HelpCenterFailure failure)? failureFaqs,
  }) {
    return failureFaqs?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingFaqs,
    TResult Function(List<HelpCenterFaqModel?> faqs)? successFaqs,
    TResult Function(HelpCenterFailure failure)? failureFaqs,
    required TResult orElse(),
  }) {
    if (failureFaqs != null) {
      return failureFaqs(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingFaqs value) loadingFaqs,
    required TResult Function(SuccessFaqs value) successFaqs,
    required TResult Function(FailureFaqs value) failureFaqs,
  }) {
    return failureFaqs(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingFaqs value)? loadingFaqs,
    TResult? Function(SuccessFaqs value)? successFaqs,
    TResult? Function(FailureFaqs value)? failureFaqs,
  }) {
    return failureFaqs?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingFaqs value)? loadingFaqs,
    TResult Function(SuccessFaqs value)? successFaqs,
    TResult Function(FailureFaqs value)? failureFaqs,
    required TResult orElse(),
  }) {
    if (failureFaqs != null) {
      return failureFaqs(this);
    }
    return orElse();
  }
}

abstract class FailureFaqs extends HelpCenterFaqState {
  const factory FailureFaqs(final HelpCenterFailure failure) =
      _$FailureFaqsImpl;
  const FailureFaqs._() : super._();

  HelpCenterFailure get failure;

  /// Create a copy of HelpCenterFaqState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureFaqsImplCopyWith<_$FailureFaqsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
