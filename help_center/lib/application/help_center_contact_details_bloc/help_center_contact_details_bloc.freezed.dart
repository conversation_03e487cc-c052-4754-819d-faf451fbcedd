// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'help_center_contact_details_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HelpCenterContactDetailsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getContactDetails,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getContactDetails,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getContactDetails,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetContactDetails value) getContactDetails,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetContactDetails value)? getContactDetails,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetContactDetails value)? getContactDetails,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpCenterContactDetailsEventCopyWith<$Res> {
  factory $HelpCenterContactDetailsEventCopyWith(
          HelpCenterContactDetailsEvent value,
          $Res Function(HelpCenterContactDetailsEvent) then) =
      _$HelpCenterContactDetailsEventCopyWithImpl<$Res,
          HelpCenterContactDetailsEvent>;
}

/// @nodoc
class _$HelpCenterContactDetailsEventCopyWithImpl<$Res,
        $Val extends HelpCenterContactDetailsEvent>
    implements $HelpCenterContactDetailsEventCopyWith<$Res> {
  _$HelpCenterContactDetailsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpCenterContactDetailsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$GetContactDetailsImplCopyWith<$Res> {
  factory _$$GetContactDetailsImplCopyWith(_$GetContactDetailsImpl value,
          $Res Function(_$GetContactDetailsImpl) then) =
      __$$GetContactDetailsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetContactDetailsImplCopyWithImpl<$Res>
    extends _$HelpCenterContactDetailsEventCopyWithImpl<$Res,
        _$GetContactDetailsImpl>
    implements _$$GetContactDetailsImplCopyWith<$Res> {
  __$$GetContactDetailsImplCopyWithImpl(_$GetContactDetailsImpl _value,
      $Res Function(_$GetContactDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterContactDetailsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetContactDetailsImpl implements GetContactDetails {
  const _$GetContactDetailsImpl();

  @override
  String toString() {
    return 'HelpCenterContactDetailsEvent.getContactDetails()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetContactDetailsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getContactDetails,
  }) {
    return getContactDetails();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getContactDetails,
  }) {
    return getContactDetails?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getContactDetails,
    required TResult orElse(),
  }) {
    if (getContactDetails != null) {
      return getContactDetails();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetContactDetails value) getContactDetails,
  }) {
    return getContactDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetContactDetails value)? getContactDetails,
  }) {
    return getContactDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetContactDetails value)? getContactDetails,
    required TResult orElse(),
  }) {
    if (getContactDetails != null) {
      return getContactDetails(this);
    }
    return orElse();
  }
}

abstract class GetContactDetails implements HelpCenterContactDetailsEvent {
  const factory GetContactDetails() = _$GetContactDetailsImpl;
}

/// @nodoc
mixin _$HelpCenterContactDetailsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(HelpCenterContactModel? contact)
        successContactDetails,
    required TResult Function() loadingContactDetails,
    required TResult Function(HelpCenterFailure failure) failureContactDetails,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult? Function()? loadingContactDetails,
    TResult? Function(HelpCenterFailure failure)? failureContactDetails,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult Function()? loadingContactDetails,
    TResult Function(HelpCenterFailure failure)? failureContactDetails,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(SuccessContactDetails value)
        successContactDetails,
    required TResult Function(LoadingContactDetails value)
        loadingContactDetails,
    required TResult Function(FailureContactDetails value)
        failureContactDetails,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(SuccessContactDetails value)? successContactDetails,
    TResult? Function(LoadingContactDetails value)? loadingContactDetails,
    TResult? Function(FailureContactDetails value)? failureContactDetails,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(SuccessContactDetails value)? successContactDetails,
    TResult Function(LoadingContactDetails value)? loadingContactDetails,
    TResult Function(FailureContactDetails value)? failureContactDetails,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpCenterContactDetailsStateCopyWith<$Res> {
  factory $HelpCenterContactDetailsStateCopyWith(
          HelpCenterContactDetailsState value,
          $Res Function(HelpCenterContactDetailsState) then) =
      _$HelpCenterContactDetailsStateCopyWithImpl<$Res,
          HelpCenterContactDetailsState>;
}

/// @nodoc
class _$HelpCenterContactDetailsStateCopyWithImpl<$Res,
        $Val extends HelpCenterContactDetailsState>
    implements $HelpCenterContactDetailsStateCopyWith<$Res> {
  _$HelpCenterContactDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$HelpCenterContactDetailsStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'HelpCenterContactDetailsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(HelpCenterContactModel? contact)
        successContactDetails,
    required TResult Function() loadingContactDetails,
    required TResult Function(HelpCenterFailure failure) failureContactDetails,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult? Function()? loadingContactDetails,
    TResult? Function(HelpCenterFailure failure)? failureContactDetails,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult Function()? loadingContactDetails,
    TResult Function(HelpCenterFailure failure)? failureContactDetails,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(SuccessContactDetails value)
        successContactDetails,
    required TResult Function(LoadingContactDetails value)
        loadingContactDetails,
    required TResult Function(FailureContactDetails value)
        failureContactDetails,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(SuccessContactDetails value)? successContactDetails,
    TResult? Function(LoadingContactDetails value)? loadingContactDetails,
    TResult? Function(FailureContactDetails value)? failureContactDetails,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(SuccessContactDetails value)? successContactDetails,
    TResult Function(LoadingContactDetails value)? loadingContactDetails,
    TResult Function(FailureContactDetails value)? failureContactDetails,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements HelpCenterContactDetailsState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$SuccessContactDetailsImplCopyWith<$Res> {
  factory _$$SuccessContactDetailsImplCopyWith(
          _$SuccessContactDetailsImpl value,
          $Res Function(_$SuccessContactDetailsImpl) then) =
      __$$SuccessContactDetailsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HelpCenterContactModel? contact});
}

/// @nodoc
class __$$SuccessContactDetailsImplCopyWithImpl<$Res>
    extends _$HelpCenterContactDetailsStateCopyWithImpl<$Res,
        _$SuccessContactDetailsImpl>
    implements _$$SuccessContactDetailsImplCopyWith<$Res> {
  __$$SuccessContactDetailsImplCopyWithImpl(_$SuccessContactDetailsImpl _value,
      $Res Function(_$SuccessContactDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contact = freezed,
  }) {
    return _then(_$SuccessContactDetailsImpl(
      freezed == contact
          ? _value.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as HelpCenterContactModel?,
    ));
  }
}

/// @nodoc

class _$SuccessContactDetailsImpl implements SuccessContactDetails {
  const _$SuccessContactDetailsImpl(this.contact);

  @override
  final HelpCenterContactModel? contact;

  @override
  String toString() {
    return 'HelpCenterContactDetailsState.successContactDetails(contact: $contact)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessContactDetailsImpl &&
            (identical(other.contact, contact) || other.contact == contact));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contact);

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessContactDetailsImplCopyWith<_$SuccessContactDetailsImpl>
      get copyWith => __$$SuccessContactDetailsImplCopyWithImpl<
          _$SuccessContactDetailsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(HelpCenterContactModel? contact)
        successContactDetails,
    required TResult Function() loadingContactDetails,
    required TResult Function(HelpCenterFailure failure) failureContactDetails,
  }) {
    return successContactDetails(contact);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult? Function()? loadingContactDetails,
    TResult? Function(HelpCenterFailure failure)? failureContactDetails,
  }) {
    return successContactDetails?.call(contact);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult Function()? loadingContactDetails,
    TResult Function(HelpCenterFailure failure)? failureContactDetails,
    required TResult orElse(),
  }) {
    if (successContactDetails != null) {
      return successContactDetails(contact);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(SuccessContactDetails value)
        successContactDetails,
    required TResult Function(LoadingContactDetails value)
        loadingContactDetails,
    required TResult Function(FailureContactDetails value)
        failureContactDetails,
  }) {
    return successContactDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(SuccessContactDetails value)? successContactDetails,
    TResult? Function(LoadingContactDetails value)? loadingContactDetails,
    TResult? Function(FailureContactDetails value)? failureContactDetails,
  }) {
    return successContactDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(SuccessContactDetails value)? successContactDetails,
    TResult Function(LoadingContactDetails value)? loadingContactDetails,
    TResult Function(FailureContactDetails value)? failureContactDetails,
    required TResult orElse(),
  }) {
    if (successContactDetails != null) {
      return successContactDetails(this);
    }
    return orElse();
  }
}

abstract class SuccessContactDetails implements HelpCenterContactDetailsState {
  const factory SuccessContactDetails(final HelpCenterContactModel? contact) =
      _$SuccessContactDetailsImpl;

  HelpCenterContactModel? get contact;

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessContactDetailsImplCopyWith<_$SuccessContactDetailsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadingContactDetailsImplCopyWith<$Res> {
  factory _$$LoadingContactDetailsImplCopyWith(
          _$LoadingContactDetailsImpl value,
          $Res Function(_$LoadingContactDetailsImpl) then) =
      __$$LoadingContactDetailsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingContactDetailsImplCopyWithImpl<$Res>
    extends _$HelpCenterContactDetailsStateCopyWithImpl<$Res,
        _$LoadingContactDetailsImpl>
    implements _$$LoadingContactDetailsImplCopyWith<$Res> {
  __$$LoadingContactDetailsImplCopyWithImpl(_$LoadingContactDetailsImpl _value,
      $Res Function(_$LoadingContactDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingContactDetailsImpl implements LoadingContactDetails {
  const _$LoadingContactDetailsImpl();

  @override
  String toString() {
    return 'HelpCenterContactDetailsState.loadingContactDetails()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadingContactDetailsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(HelpCenterContactModel? contact)
        successContactDetails,
    required TResult Function() loadingContactDetails,
    required TResult Function(HelpCenterFailure failure) failureContactDetails,
  }) {
    return loadingContactDetails();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult? Function()? loadingContactDetails,
    TResult? Function(HelpCenterFailure failure)? failureContactDetails,
  }) {
    return loadingContactDetails?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult Function()? loadingContactDetails,
    TResult Function(HelpCenterFailure failure)? failureContactDetails,
    required TResult orElse(),
  }) {
    if (loadingContactDetails != null) {
      return loadingContactDetails();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(SuccessContactDetails value)
        successContactDetails,
    required TResult Function(LoadingContactDetails value)
        loadingContactDetails,
    required TResult Function(FailureContactDetails value)
        failureContactDetails,
  }) {
    return loadingContactDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(SuccessContactDetails value)? successContactDetails,
    TResult? Function(LoadingContactDetails value)? loadingContactDetails,
    TResult? Function(FailureContactDetails value)? failureContactDetails,
  }) {
    return loadingContactDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(SuccessContactDetails value)? successContactDetails,
    TResult Function(LoadingContactDetails value)? loadingContactDetails,
    TResult Function(FailureContactDetails value)? failureContactDetails,
    required TResult orElse(),
  }) {
    if (loadingContactDetails != null) {
      return loadingContactDetails(this);
    }
    return orElse();
  }
}

abstract class LoadingContactDetails implements HelpCenterContactDetailsState {
  const factory LoadingContactDetails() = _$LoadingContactDetailsImpl;
}

/// @nodoc
abstract class _$$FailureContactDetailsImplCopyWith<$Res> {
  factory _$$FailureContactDetailsImplCopyWith(
          _$FailureContactDetailsImpl value,
          $Res Function(_$FailureContactDetailsImpl) then) =
      __$$FailureContactDetailsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HelpCenterFailure failure});

  $HelpCenterFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureContactDetailsImplCopyWithImpl<$Res>
    extends _$HelpCenterContactDetailsStateCopyWithImpl<$Res,
        _$FailureContactDetailsImpl>
    implements _$$FailureContactDetailsImplCopyWith<$Res> {
  __$$FailureContactDetailsImplCopyWithImpl(_$FailureContactDetailsImpl _value,
      $Res Function(_$FailureContactDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureContactDetailsImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HelpCenterFailure,
    ));
  }

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $HelpCenterFailureCopyWith<$Res> get failure {
    return $HelpCenterFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureContactDetailsImpl implements FailureContactDetails {
  const _$FailureContactDetailsImpl(this.failure);

  @override
  final HelpCenterFailure failure;

  @override
  String toString() {
    return 'HelpCenterContactDetailsState.failureContactDetails(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureContactDetailsImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureContactDetailsImplCopyWith<_$FailureContactDetailsImpl>
      get copyWith => __$$FailureContactDetailsImplCopyWithImpl<
          _$FailureContactDetailsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(HelpCenterContactModel? contact)
        successContactDetails,
    required TResult Function() loadingContactDetails,
    required TResult Function(HelpCenterFailure failure) failureContactDetails,
  }) {
    return failureContactDetails(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult? Function()? loadingContactDetails,
    TResult? Function(HelpCenterFailure failure)? failureContactDetails,
  }) {
    return failureContactDetails?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(HelpCenterContactModel? contact)? successContactDetails,
    TResult Function()? loadingContactDetails,
    TResult Function(HelpCenterFailure failure)? failureContactDetails,
    required TResult orElse(),
  }) {
    if (failureContactDetails != null) {
      return failureContactDetails(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(SuccessContactDetails value)
        successContactDetails,
    required TResult Function(LoadingContactDetails value)
        loadingContactDetails,
    required TResult Function(FailureContactDetails value)
        failureContactDetails,
  }) {
    return failureContactDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(SuccessContactDetails value)? successContactDetails,
    TResult? Function(LoadingContactDetails value)? loadingContactDetails,
    TResult? Function(FailureContactDetails value)? failureContactDetails,
  }) {
    return failureContactDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(SuccessContactDetails value)? successContactDetails,
    TResult Function(LoadingContactDetails value)? loadingContactDetails,
    TResult Function(FailureContactDetails value)? failureContactDetails,
    required TResult orElse(),
  }) {
    if (failureContactDetails != null) {
      return failureContactDetails(this);
    }
    return orElse();
  }
}

abstract class FailureContactDetails implements HelpCenterContactDetailsState {
  const factory FailureContactDetails(final HelpCenterFailure failure) =
      _$FailureContactDetailsImpl;

  HelpCenterFailure get failure;

  /// Create a copy of HelpCenterContactDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureContactDetailsImplCopyWith<_$FailureContactDetailsImpl>
      get copyWith => throw _privateConstructorUsedError;
}
