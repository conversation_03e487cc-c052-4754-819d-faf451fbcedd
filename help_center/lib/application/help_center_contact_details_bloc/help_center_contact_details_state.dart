part of 'help_center_contact_details_bloc.dart';

@freezed
class HelpCenterContactDetailsState with _$HelpCenterContactDetailsState {
  // State representing the initial state of the help center
  const factory HelpCenterContactDetailsState.initial() = Initial;
  // State representing the success state of the help center contact details
  const factory HelpCenterContactDetailsState.successContactDetails(HelpCenterContactModel? contact) = SuccessContactDetails;
// State representing the loading state of the help center contact details
  const factory HelpCenterContactDetailsState.loadingContactDetails() = LoadingContactDetails;
  // State representing the failure state of the loading of the help center contact details
  const factory HelpCenterContactDetailsState.failureContactDetails(HelpCenterFailure failure) = FailureContactDetails;

}


