part of 'help_center_videos_bloc.dart';


@freezed
class HelpCenterVideosState with _$HelpCenterVideosState {
  // State representing the initial state of the help center
  const factory HelpCenterVideosState.initial() = Initial;
  // State representing the loading state of the help center videos
  const factory HelpCenterVideosState.loadingVideos() = LoadingVideos;
  // State representing the failure state of the loading of the help center videos
  const factory HelpCenterVideosState.failureVideos(HelpCenterFailure failure) = FailureVideos;
  // State representing the success state of the help center_videos
  const factory HelpCenterVideosState.successVideos(List<HelpCenterVideoModel?> videos) = SuccessVideos;
 

}

