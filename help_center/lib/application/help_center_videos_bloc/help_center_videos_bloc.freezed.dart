// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'help_center_videos_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HelpCenterVideosEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadHelpCenterVideos,
    required TResult Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)
        helpCenterVideosLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadHelpCenterVideos,
    TResult? Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)?
        helpCenterVideosLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadHelpCenterVideos,
    TResult Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)?
        helpCenterVideosLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadHelpCenterVideos value) loadHelpCenterVideos,
    required TResult Function(HelpCenterVideosLoaded value)
        helpCenterVideosLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadHelpCenterVideos value)? loadHelpCenterVideos,
    TResult? Function(HelpCenterVideosLoaded value)? helpCenterVideosLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadHelpCenterVideos value)? loadHelpCenterVideos,
    TResult Function(HelpCenterVideosLoaded value)? helpCenterVideosLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpCenterVideosEventCopyWith<$Res> {
  factory $HelpCenterVideosEventCopyWith(HelpCenterVideosEvent value,
          $Res Function(HelpCenterVideosEvent) then) =
      _$HelpCenterVideosEventCopyWithImpl<$Res, HelpCenterVideosEvent>;
}

/// @nodoc
class _$HelpCenterVideosEventCopyWithImpl<$Res,
        $Val extends HelpCenterVideosEvent>
    implements $HelpCenterVideosEventCopyWith<$Res> {
  _$HelpCenterVideosEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpCenterVideosEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadHelpCenterVideosImplCopyWith<$Res> {
  factory _$$LoadHelpCenterVideosImplCopyWith(_$LoadHelpCenterVideosImpl value,
          $Res Function(_$LoadHelpCenterVideosImpl) then) =
      __$$LoadHelpCenterVideosImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadHelpCenterVideosImplCopyWithImpl<$Res>
    extends _$HelpCenterVideosEventCopyWithImpl<$Res,
        _$LoadHelpCenterVideosImpl>
    implements _$$LoadHelpCenterVideosImplCopyWith<$Res> {
  __$$LoadHelpCenterVideosImplCopyWithImpl(_$LoadHelpCenterVideosImpl _value,
      $Res Function(_$LoadHelpCenterVideosImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterVideosEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadHelpCenterVideosImpl implements LoadHelpCenterVideos {
  const _$LoadHelpCenterVideosImpl();

  @override
  String toString() {
    return 'HelpCenterVideosEvent.loadHelpCenterVideos()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadHelpCenterVideosImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadHelpCenterVideos,
    required TResult Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)
        helpCenterVideosLoaded,
  }) {
    return loadHelpCenterVideos();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadHelpCenterVideos,
    TResult? Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)?
        helpCenterVideosLoaded,
  }) {
    return loadHelpCenterVideos?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadHelpCenterVideos,
    TResult Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)?
        helpCenterVideosLoaded,
    required TResult orElse(),
  }) {
    if (loadHelpCenterVideos != null) {
      return loadHelpCenterVideos();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadHelpCenterVideos value) loadHelpCenterVideos,
    required TResult Function(HelpCenterVideosLoaded value)
        helpCenterVideosLoaded,
  }) {
    return loadHelpCenterVideos(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadHelpCenterVideos value)? loadHelpCenterVideos,
    TResult? Function(HelpCenterVideosLoaded value)? helpCenterVideosLoaded,
  }) {
    return loadHelpCenterVideos?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadHelpCenterVideos value)? loadHelpCenterVideos,
    TResult Function(HelpCenterVideosLoaded value)? helpCenterVideosLoaded,
    required TResult orElse(),
  }) {
    if (loadHelpCenterVideos != null) {
      return loadHelpCenterVideos(this);
    }
    return orElse();
  }
}

abstract class LoadHelpCenterVideos implements HelpCenterVideosEvent {
  const factory LoadHelpCenterVideos() = _$LoadHelpCenterVideosImpl;
}

/// @nodoc
abstract class _$$HelpCenterVideosLoadedImplCopyWith<$Res> {
  factory _$$HelpCenterVideosLoadedImplCopyWith(
          _$HelpCenterVideosLoadedImpl value,
          $Res Function(_$HelpCenterVideosLoadedImpl) then) =
      __$$HelpCenterVideosLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Either<HelpCenterFailure, List<HelpCenterVideoModel?>> failureOrVideos});
}

/// @nodoc
class __$$HelpCenterVideosLoadedImplCopyWithImpl<$Res>
    extends _$HelpCenterVideosEventCopyWithImpl<$Res,
        _$HelpCenterVideosLoadedImpl>
    implements _$$HelpCenterVideosLoadedImplCopyWith<$Res> {
  __$$HelpCenterVideosLoadedImplCopyWithImpl(
      _$HelpCenterVideosLoadedImpl _value,
      $Res Function(_$HelpCenterVideosLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterVideosEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrVideos = null,
  }) {
    return _then(_$HelpCenterVideosLoadedImpl(
      null == failureOrVideos
          ? _value.failureOrVideos
          : failureOrVideos // ignore: cast_nullable_to_non_nullable
              as Either<HelpCenterFailure, List<HelpCenterVideoModel?>>,
    ));
  }
}

/// @nodoc

class _$HelpCenterVideosLoadedImpl implements HelpCenterVideosLoaded {
  const _$HelpCenterVideosLoadedImpl(this.failureOrVideos);

  @override
  final Either<HelpCenterFailure, List<HelpCenterVideoModel?>> failureOrVideos;

  @override
  String toString() {
    return 'HelpCenterVideosEvent.helpCenterVideosLoaded(failureOrVideos: $failureOrVideos)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HelpCenterVideosLoadedImpl &&
            (identical(other.failureOrVideos, failureOrVideos) ||
                other.failureOrVideos == failureOrVideos));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrVideos);

  /// Create a copy of HelpCenterVideosEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HelpCenterVideosLoadedImplCopyWith<_$HelpCenterVideosLoadedImpl>
      get copyWith => __$$HelpCenterVideosLoadedImplCopyWithImpl<
          _$HelpCenterVideosLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadHelpCenterVideos,
    required TResult Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)
        helpCenterVideosLoaded,
  }) {
    return helpCenterVideosLoaded(failureOrVideos);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadHelpCenterVideos,
    TResult? Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)?
        helpCenterVideosLoaded,
  }) {
    return helpCenterVideosLoaded?.call(failureOrVideos);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadHelpCenterVideos,
    TResult Function(
            Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
                failureOrVideos)?
        helpCenterVideosLoaded,
    required TResult orElse(),
  }) {
    if (helpCenterVideosLoaded != null) {
      return helpCenterVideosLoaded(failureOrVideos);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadHelpCenterVideos value) loadHelpCenterVideos,
    required TResult Function(HelpCenterVideosLoaded value)
        helpCenterVideosLoaded,
  }) {
    return helpCenterVideosLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadHelpCenterVideos value)? loadHelpCenterVideos,
    TResult? Function(HelpCenterVideosLoaded value)? helpCenterVideosLoaded,
  }) {
    return helpCenterVideosLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadHelpCenterVideos value)? loadHelpCenterVideos,
    TResult Function(HelpCenterVideosLoaded value)? helpCenterVideosLoaded,
    required TResult orElse(),
  }) {
    if (helpCenterVideosLoaded != null) {
      return helpCenterVideosLoaded(this);
    }
    return orElse();
  }
}

abstract class HelpCenterVideosLoaded implements HelpCenterVideosEvent {
  const factory HelpCenterVideosLoaded(
      final Either<HelpCenterFailure, List<HelpCenterVideoModel?>>
          failureOrVideos) = _$HelpCenterVideosLoadedImpl;

  Either<HelpCenterFailure, List<HelpCenterVideoModel?>> get failureOrVideos;

  /// Create a copy of HelpCenterVideosEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HelpCenterVideosLoadedImplCopyWith<_$HelpCenterVideosLoadedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$HelpCenterVideosState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingVideos,
    required TResult Function(HelpCenterFailure failure) failureVideos,
    required TResult Function(List<HelpCenterVideoModel?> videos) successVideos,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingVideos,
    TResult? Function(HelpCenterFailure failure)? failureVideos,
    TResult? Function(List<HelpCenterVideoModel?> videos)? successVideos,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingVideos,
    TResult Function(HelpCenterFailure failure)? failureVideos,
    TResult Function(List<HelpCenterVideoModel?> videos)? successVideos,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingVideos value) loadingVideos,
    required TResult Function(FailureVideos value) failureVideos,
    required TResult Function(SuccessVideos value) successVideos,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingVideos value)? loadingVideos,
    TResult? Function(FailureVideos value)? failureVideos,
    TResult? Function(SuccessVideos value)? successVideos,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingVideos value)? loadingVideos,
    TResult Function(FailureVideos value)? failureVideos,
    TResult Function(SuccessVideos value)? successVideos,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpCenterVideosStateCopyWith<$Res> {
  factory $HelpCenterVideosStateCopyWith(HelpCenterVideosState value,
          $Res Function(HelpCenterVideosState) then) =
      _$HelpCenterVideosStateCopyWithImpl<$Res, HelpCenterVideosState>;
}

/// @nodoc
class _$HelpCenterVideosStateCopyWithImpl<$Res,
        $Val extends HelpCenterVideosState>
    implements $HelpCenterVideosStateCopyWith<$Res> {
  _$HelpCenterVideosStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$HelpCenterVideosStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'HelpCenterVideosState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingVideos,
    required TResult Function(HelpCenterFailure failure) failureVideos,
    required TResult Function(List<HelpCenterVideoModel?> videos) successVideos,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingVideos,
    TResult? Function(HelpCenterFailure failure)? failureVideos,
    TResult? Function(List<HelpCenterVideoModel?> videos)? successVideos,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingVideos,
    TResult Function(HelpCenterFailure failure)? failureVideos,
    TResult Function(List<HelpCenterVideoModel?> videos)? successVideos,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingVideos value) loadingVideos,
    required TResult Function(FailureVideos value) failureVideos,
    required TResult Function(SuccessVideos value) successVideos,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingVideos value)? loadingVideos,
    TResult? Function(FailureVideos value)? failureVideos,
    TResult? Function(SuccessVideos value)? successVideos,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingVideos value)? loadingVideos,
    TResult Function(FailureVideos value)? failureVideos,
    TResult Function(SuccessVideos value)? successVideos,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements HelpCenterVideosState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingVideosImplCopyWith<$Res> {
  factory _$$LoadingVideosImplCopyWith(
          _$LoadingVideosImpl value, $Res Function(_$LoadingVideosImpl) then) =
      __$$LoadingVideosImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingVideosImplCopyWithImpl<$Res>
    extends _$HelpCenterVideosStateCopyWithImpl<$Res, _$LoadingVideosImpl>
    implements _$$LoadingVideosImplCopyWith<$Res> {
  __$$LoadingVideosImplCopyWithImpl(
      _$LoadingVideosImpl _value, $Res Function(_$LoadingVideosImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingVideosImpl implements LoadingVideos {
  const _$LoadingVideosImpl();

  @override
  String toString() {
    return 'HelpCenterVideosState.loadingVideos()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingVideosImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingVideos,
    required TResult Function(HelpCenterFailure failure) failureVideos,
    required TResult Function(List<HelpCenterVideoModel?> videos) successVideos,
  }) {
    return loadingVideos();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingVideos,
    TResult? Function(HelpCenterFailure failure)? failureVideos,
    TResult? Function(List<HelpCenterVideoModel?> videos)? successVideos,
  }) {
    return loadingVideos?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingVideos,
    TResult Function(HelpCenterFailure failure)? failureVideos,
    TResult Function(List<HelpCenterVideoModel?> videos)? successVideos,
    required TResult orElse(),
  }) {
    if (loadingVideos != null) {
      return loadingVideos();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingVideos value) loadingVideos,
    required TResult Function(FailureVideos value) failureVideos,
    required TResult Function(SuccessVideos value) successVideos,
  }) {
    return loadingVideos(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingVideos value)? loadingVideos,
    TResult? Function(FailureVideos value)? failureVideos,
    TResult? Function(SuccessVideos value)? successVideos,
  }) {
    return loadingVideos?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingVideos value)? loadingVideos,
    TResult Function(FailureVideos value)? failureVideos,
    TResult Function(SuccessVideos value)? successVideos,
    required TResult orElse(),
  }) {
    if (loadingVideos != null) {
      return loadingVideos(this);
    }
    return orElse();
  }
}

abstract class LoadingVideos implements HelpCenterVideosState {
  const factory LoadingVideos() = _$LoadingVideosImpl;
}

/// @nodoc
abstract class _$$FailureVideosImplCopyWith<$Res> {
  factory _$$FailureVideosImplCopyWith(
          _$FailureVideosImpl value, $Res Function(_$FailureVideosImpl) then) =
      __$$FailureVideosImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HelpCenterFailure failure});

  $HelpCenterFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureVideosImplCopyWithImpl<$Res>
    extends _$HelpCenterVideosStateCopyWithImpl<$Res, _$FailureVideosImpl>
    implements _$$FailureVideosImplCopyWith<$Res> {
  __$$FailureVideosImplCopyWithImpl(
      _$FailureVideosImpl _value, $Res Function(_$FailureVideosImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureVideosImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HelpCenterFailure,
    ));
  }

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $HelpCenterFailureCopyWith<$Res> get failure {
    return $HelpCenterFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureVideosImpl implements FailureVideos {
  const _$FailureVideosImpl(this.failure);

  @override
  final HelpCenterFailure failure;

  @override
  String toString() {
    return 'HelpCenterVideosState.failureVideos(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureVideosImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureVideosImplCopyWith<_$FailureVideosImpl> get copyWith =>
      __$$FailureVideosImplCopyWithImpl<_$FailureVideosImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingVideos,
    required TResult Function(HelpCenterFailure failure) failureVideos,
    required TResult Function(List<HelpCenterVideoModel?> videos) successVideos,
  }) {
    return failureVideos(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingVideos,
    TResult? Function(HelpCenterFailure failure)? failureVideos,
    TResult? Function(List<HelpCenterVideoModel?> videos)? successVideos,
  }) {
    return failureVideos?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingVideos,
    TResult Function(HelpCenterFailure failure)? failureVideos,
    TResult Function(List<HelpCenterVideoModel?> videos)? successVideos,
    required TResult orElse(),
  }) {
    if (failureVideos != null) {
      return failureVideos(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingVideos value) loadingVideos,
    required TResult Function(FailureVideos value) failureVideos,
    required TResult Function(SuccessVideos value) successVideos,
  }) {
    return failureVideos(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingVideos value)? loadingVideos,
    TResult? Function(FailureVideos value)? failureVideos,
    TResult? Function(SuccessVideos value)? successVideos,
  }) {
    return failureVideos?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingVideos value)? loadingVideos,
    TResult Function(FailureVideos value)? failureVideos,
    TResult Function(SuccessVideos value)? successVideos,
    required TResult orElse(),
  }) {
    if (failureVideos != null) {
      return failureVideos(this);
    }
    return orElse();
  }
}

abstract class FailureVideos implements HelpCenterVideosState {
  const factory FailureVideos(final HelpCenterFailure failure) =
      _$FailureVideosImpl;

  HelpCenterFailure get failure;

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureVideosImplCopyWith<_$FailureVideosImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SuccessVideosImplCopyWith<$Res> {
  factory _$$SuccessVideosImplCopyWith(
          _$SuccessVideosImpl value, $Res Function(_$SuccessVideosImpl) then) =
      __$$SuccessVideosImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<HelpCenterVideoModel?> videos});
}

/// @nodoc
class __$$SuccessVideosImplCopyWithImpl<$Res>
    extends _$HelpCenterVideosStateCopyWithImpl<$Res, _$SuccessVideosImpl>
    implements _$$SuccessVideosImplCopyWith<$Res> {
  __$$SuccessVideosImplCopyWithImpl(
      _$SuccessVideosImpl _value, $Res Function(_$SuccessVideosImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? videos = null,
  }) {
    return _then(_$SuccessVideosImpl(
      null == videos
          ? _value._videos
          : videos // ignore: cast_nullable_to_non_nullable
              as List<HelpCenterVideoModel?>,
    ));
  }
}

/// @nodoc

class _$SuccessVideosImpl implements SuccessVideos {
  const _$SuccessVideosImpl(final List<HelpCenterVideoModel?> videos)
      : _videos = videos;

  final List<HelpCenterVideoModel?> _videos;
  @override
  List<HelpCenterVideoModel?> get videos {
    if (_videos is EqualUnmodifiableListView) return _videos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_videos);
  }

  @override
  String toString() {
    return 'HelpCenterVideosState.successVideos(videos: $videos)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessVideosImpl &&
            const DeepCollectionEquality().equals(other._videos, _videos));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_videos));

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessVideosImplCopyWith<_$SuccessVideosImpl> get copyWith =>
      __$$SuccessVideosImplCopyWithImpl<_$SuccessVideosImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadingVideos,
    required TResult Function(HelpCenterFailure failure) failureVideos,
    required TResult Function(List<HelpCenterVideoModel?> videos) successVideos,
  }) {
    return successVideos(videos);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadingVideos,
    TResult? Function(HelpCenterFailure failure)? failureVideos,
    TResult? Function(List<HelpCenterVideoModel?> videos)? successVideos,
  }) {
    return successVideos?.call(videos);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadingVideos,
    TResult Function(HelpCenterFailure failure)? failureVideos,
    TResult Function(List<HelpCenterVideoModel?> videos)? successVideos,
    required TResult orElse(),
  }) {
    if (successVideos != null) {
      return successVideos(videos);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(LoadingVideos value) loadingVideos,
    required TResult Function(FailureVideos value) failureVideos,
    required TResult Function(SuccessVideos value) successVideos,
  }) {
    return successVideos(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(LoadingVideos value)? loadingVideos,
    TResult? Function(FailureVideos value)? failureVideos,
    TResult? Function(SuccessVideos value)? successVideos,
  }) {
    return successVideos?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(LoadingVideos value)? loadingVideos,
    TResult Function(FailureVideos value)? failureVideos,
    TResult Function(SuccessVideos value)? successVideos,
    required TResult orElse(),
  }) {
    if (successVideos != null) {
      return successVideos(this);
    }
    return orElse();
  }
}

abstract class SuccessVideos implements HelpCenterVideosState {
  const factory SuccessVideos(final List<HelpCenterVideoModel?> videos) =
      _$SuccessVideosImpl;

  List<HelpCenterVideoModel?> get videos;

  /// Create a copy of HelpCenterVideosState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessVideosImplCopyWith<_$SuccessVideosImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
