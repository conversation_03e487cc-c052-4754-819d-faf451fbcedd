part of 'help_center_videos_bloc.dart';

@freezed
class HelpCenterVideosEvent with _$HelpCenterVideosEvent {
  // Event representing a request to load the help center videos
  const factory HelpCenterVideosEvent.loadHelpCenterVideos() = LoadHelpCenterVideos;
  // Event representing a load of the help center videos
  const factory HelpCenterVideosEvent.helpCenterVideosLoaded(Either<HelpCenterFailure, List<HelpCenterVideoModel?>> failureOrVideos) = HelpCenterVideosLoaded;

}

