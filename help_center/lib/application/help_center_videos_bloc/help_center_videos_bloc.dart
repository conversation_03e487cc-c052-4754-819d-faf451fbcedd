import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:fpdart/fpdart.dart';
import '../../domain/facade/help_center_facade.dart';
import '../../domain/failure/help_center_failure.dart';
import '../../domain/model/help_center_model.dart';
import 'package:injectable/injectable.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'help_center_videos_event.dart';
part 'help_center_videos_state.dart';
part 'help_center_videos_bloc.freezed.dart';

@injectable
class HelpCenterVideosBloc extends Bloc<HelpCenterVideosEvent, HelpCenterVideosState> {
  final HelpCenterFacade _helpCenterFacade;
  StreamSubscription<Either<HelpCenterFailure, List<HelpCenterVideoModel?>>>? _streamVideoSubscription;

  HelpCenterVideosBloc(this._helpCenterFacade) : super(const HelpCenterVideosState.initial()) {
    on<LoadHelpCenterVideos>(_onLoadHelpCenterVideos);
    on<HelpCenterVideosLoaded>(_onHelpCenterVideosLoaded);
  }

  Future<void> _onLoadHelpCenterVideos(
      LoadHelpCenterVideos event,
      Emitter<HelpCenterVideosState> emit,
      ) async {
    emit(const HelpCenterVideosState.loadingVideos());
    await _streamVideoSubscription?.cancel();
    _streamVideoSubscription = _helpCenterFacade.getHelpCenterVideos().listen(
          (failureOrVideos) {
        add(HelpCenterVideosEvent.helpCenterVideosLoaded(failureOrVideos));
      },
    );
  }

  Future<void> _onHelpCenterVideosLoaded(
      HelpCenterVideosLoaded event,
      Emitter<HelpCenterVideosState> emit,
      ) async {
    event.failureOrVideos.mapBoth(
         onLeft:  (failure) => emit(HelpCenterVideosState.failureVideos(failure)),
         onRight: (videos) => emit(HelpCenterVideosState.successVideos(videos)),
    );
  }

  @override
  Future<void> close() async {
    await _streamVideoSubscription?.cancel();
    return super.close();
  }
}
