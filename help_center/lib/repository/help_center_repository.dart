import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fpdart/src/effect.dart';
import 'package:help_center/domain/facade/help_center_facade.dart';
import 'package:help_center/domain/failure/help_center_failure.dart';
import 'package:help_center/domain/model/help_center_model.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: HelpCenterFacade)
class HelpCenterRepository implements HelpCenterFacade{
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  @override
  Future<Either<HelpCenterFailure, HelpCenterContactModel?>> getContactDetails()async {
 try{
      return _firestore.collection('help_center_contact').doc('contact').get().then((value) {
        final data = value.data();
        if(data != null){
          final contact = HelpCenterContactModel.fromJson(data);
          return Right(contact);
        }else{
          return const Left(HelpCenterFailure.getContactDetailsFailure('Failed to get contact details'));
        }
      });
    }catch(e){
      return Left(HelpCenterFailure.getContactDetailsFailure('Failed to get contact details'));
    }
  }

  @override
  Stream<Either<HelpCenterFailure, List<HelpCenterFaqModel?>>> getHelpCenterFaq() async* {
    try{
      yield* _firestore.collection('help_center_faq').snapshots().map((snapshot) => Right(snapshot.docs.map((e) => HelpCenterFaqModel.fromJson(e.data())).toList()));
    }catch(e){
      yield Left(HelpCenterFailure.getHelpCenterFaqFailure('Failed to get FAQs'));
    }
  }

  @override
  Stream<Either<HelpCenterFailure, List<HelpCenterVideoModel?>>> getHelpCenterVideos() async* {
    try{
      yield* _firestore.collection('help_center_videos').snapshots().map((event) {
        final videos = event.docs.map((e) => HelpCenterVideoModel.fromJson(e.data())).toList();
        return Right(videos);
      });
    }catch(e){
      yield Left(HelpCenterFailure.getHelpCenterVideosFailure('Failed to get videos'));
    }
  }

}