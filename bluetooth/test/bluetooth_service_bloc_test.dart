// import 'package:bloc_test/bloc_test.dart';
// import 'package:bluetooth/application/bluetooth_service_bloc/bluetooth_service_bloc.dart';
// import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
// import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:fpdart/fpdart.dart';
// import 'package:mocktail/mocktail.dart';
//
// import 'package:flutter_blue_plus/flutter_blue_plus.dart';
//
// class MockBluetoothFacade extends Mock implements IBluetoothFacade {}
//
// class MockBluetoothDevice extends Mock implements BluetoothDevice {}
//
// void main() {
//   late BluetoothServiceBloc bloc;
//   late MockBluetoothFacade mockBluetoothFacade;
//   late MockBluetoothDevice mockBluetoothDevice;
//
//   setUp(() {
//     mockBluetoothFacade = MockBluetoothFacade();
//     mockBluetoothDevice = MockBluetoothDevice();
//     bloc = BluetoothServiceBloc(mockBluetoothFacade);
//   });
//
//   group('BluetoothServiceBloc', () {
//     blocTest<BluetoothServiceBloc, BluetoothServiceState>(
//       'emits [BluetoothOn, Connected] when CheckBluetooth is added and device is connected',
//       build: () {
//         when(() => mockBluetoothFacade.isBluetoothOn()).thenAnswer((_) => Stream.value(true));
//         when(() => mockBluetoothFacade.deviceConnected()).thenAnswer((_) async => Right(mockBluetoothDevice));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const CheckBluetooth()),
//       expect: () => [
//         const BluetoothServiceState.bluetoothOn(),
//         BluetoothServiceState.connected(mockBluetoothDevice),
//       ],
//     );
//
//     blocTest<BluetoothServiceBloc, BluetoothServiceState>(
//       'emits [BluetoothOff] when CheckBluetooth is added and Bluetooth is off',
//       build: () {
//         when(() => mockBluetoothFacade.isBluetoothOn()).thenAnswer((_) => Stream.value(false));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const CheckBluetooth()),
//       expect: () => [
//         const BluetoothServiceState.bluetoothOff(),
//       ],
//     );
//
//     blocTest<BluetoothServiceBloc, BluetoothServiceState>(
//       'emits [BluetoothSearching, BluetoothAvailableTypeDevices] when StartSearch is added and devices are found',
//       build: () {
//         when(() => mockBluetoothFacade.searchForDevices()).thenAnswer((_) async => Right([mockBluetoothDevice]));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const StartSearch()),
//       expect: () => [
//         const BluetoothServiceState.bluetoothSearching(),
//         BluetoothServiceState.bluetoothAvailableTypeDevices([mockBluetoothDevice]),
//       ],
//     );
//
//     blocTest<BluetoothServiceBloc, BluetoothServiceState>(
//       'emits [BluetoothError] when StartSearch is added and no devices are found',
//       build: () {
//         when(() => mockBluetoothFacade.searchForDevices()).thenAnswer((_) async => Left(const BluetoothFailure.noDevicesFound()));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const StartSearch()),
//       expect: () => [
//         const BluetoothServiceState.bluetoothSearching(),
//         BluetoothServiceState.bluetoothError(BluetoothFailure.noDevicesFound()),
//       ],
//     );
//
//     blocTest<BluetoothServiceBloc, BluetoothServiceState>(
//       'emits [ConnectionIntro] when ConnectToDevice is added and pairing is successful',
//       build: () {
//         when(() => mockBluetoothFacade.pairDevice(mockBluetoothDevice)).thenAnswer((_) async => Right(null));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(ConnectToDevice(mockBluetoothDevice)),
//       expect: () => [
//         BluetoothServiceState.connectionIntro(mockBluetoothDevice),
//       ],
//     );
//
//     blocTest<BluetoothServiceBloc, BluetoothServiceState>(
//       'emits [BluetoothError] when ConnectToDevice is added and pairing fails',
//       build: () {
//         when(() => mockBluetoothFacade.pairDevice(mockBluetoothDevice)).thenAnswer((_) async => Left(const BluetoothFailure.unexpected()));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(ConnectToDevice(mockBluetoothDevice)),
//       expect: () => [
//         const BluetoothServiceState.bluetoothError(BluetoothFailure.unexpected()),
//       ],
//     );
//
//     blocTest<BluetoothServiceBloc, BluetoothServiceState>(
//       'emits [Disconnected] when DeviceDisconnected is added',
//       build: () => bloc,
//       act: (bloc) => bloc.add(DeviceDisconnected(mockBluetoothDevice)),
//       expect: () => [
//         BluetoothServiceState.disconnected(mockBluetoothDevice),
//       ],
//     );
//   });
// }