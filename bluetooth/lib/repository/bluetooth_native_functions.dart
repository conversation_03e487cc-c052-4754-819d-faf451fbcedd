import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';

/// The BluetoothSettings class provides methods to manage and control
/// the Bluetooth settings of the device, such as starting/stopping monitoring,
/// opening Bluetooth settings, and turning on Bluetooth.
class BluetoothSettings {
  // Define the method channel to communicate with the native platform
  static const platform = MethodChannel('samples.flutter.dev/bluetooth');

  // This method starts the monitoring of the Bluetooth status of the device.
  // It invokes a method on the native platform to start the monitoring.
  static Future<void> startBluetoothMonitoring() async {
    try {
      await platform.invokeMethod('startBluetoothMonitoring');
    } on PlatformException catch (e) {
      const SnackBar(content: Text('Failed to start Bluetooth'));
    }
  }

  // This method stops the monitoring of the Bluetooth status of the device.
  // It invokes a method on the native platform to stop the monitoring.
  static Future<void> stopBluetoothMonitoring() async {
    try {
      await platform.invokeMethod('stopBluetoothMonitoring');
    } on PlatformException catch (e) {
      const SnackBar(content: Text('Failed to stop Bluetooth'));
    }
  }

  // This method opens the Bluetooth settings of the device.
  // It invokes a method on the native platform to open the Bluetooth settings.
  static Future<void> openBluetoothSettings() async {
    try {
      await platform.invokeMethod('openBluetoothSettings');
    } on PlatformException catch (e) {
      const SnackBar(content: Text('Failed to open Bluetooth settings'));
    }
  }

  // This method turns on the Bluetooth of the device.
  // It first checks for the necessary permissions and then invokes
  // the appropriate method to turn on Bluetooth.
  static Future<void> turnOnBluetooth() async {
    try {
      // Request Bluetooth permission
      PermissionStatus status = await Permission.bluetooth.request();
      if (status.isGranted) {
        // If permission is granted, turn on Bluetooth
        await FlutterBluePlus.turnOn();
      } else {
        // If permission is not granted and the platform is iOS, open Bluetooth settings
        // If the platform is not iOS, recursively call turnOnBluetooth() to request permission again
        if(Platform.isIOS){
          BluetoothSettings.openBluetoothSettings();
        }else{
          turnOnBluetooth();
        }
      }
    } catch (e) {
      // Handle error if needed
    }
  }
}