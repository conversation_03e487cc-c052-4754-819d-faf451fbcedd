// UUID Definitions
class BluetoothConstants {
  // Battery Service UUIDs
  static const String batteryLevelUUID = '00002a19-0000-1000-8000-00805f9b34fc';
  static const String batteryServiceUUID =
      '0000180f-0000-1000-8000-00805f9b3511';

  // Device Information Service UUIDs
  static const String manufacturerNameUUID =
      '00002a29-0000-1000-8000-00805f9b34fd';
  static const String modelNumberUUID = '00002a24-0000-1000-8000-00805f9b34fe';
  static const String serialNumberUUID = '00002a25-0000-1000-8000-00805f9b34ff';
  static const String hardwareRevisionUUID =
      '00002a27-0000-1000-8000-00805f9b3500';
  static const String firmwareRevisionUUID =
      '00002a26-0000-1000-8000-00805f9b3501';
  static const String udiUUID = '00002a28-0000-1000-8000-00805f9b3502';
  static const String deviceInfoServiceUUID =
      '0000180a-0000-1000-8000-00805f9b3512';

  // TENS Control Service UUIDs
  static const String tensControlServiceUUID =
      '23289aa0-670c-4635-8b38-e1ab58c0e9c4';
  static const String currentTensIntensityUUID =
      '23289aa3-670c-44f3-8b38-e1ab58c0e9c4';
  static const String targetTensIntensityUUID =
      '23289aa2-670c-44f3-8b38-e1ab58c0e9c4';
  static const String increaseTensIntensityUUID =
      '23289aa2-670c-44f3-8b38-e1ab58c0e9c4';
  static const String decreaseTensIntensityUUID =
      '23289aa2-670c-44f3-8b38-e1ab58c0e9c4';
  static const String activeTensUUID = '23289aa3-670c-44f3-8b38-e1ab58c0e9c4';
  static const String tensModeUUID = '23289aa1-670c-44f3-8b38-e1ab58c0e9c4';
  static const String measuredIntensityUUID =
      '23289aa4-670c-44f3-8b38-e1ab58c0e9c4';
  static const String tensControlUUID = '23289aa2-670c-44f3-8b38-e1ab58c0e9c4';
  static const String tensStatus = '23289aa6-670c-44f3-8b38-e1ab58c0e9c4';

  // Temperature Control Service UUIDs
  static const String temperatureControlServiceUUID =
      'd0b12770-6b7d-4635-ab5a-e3abdd621537';
  static const String increaseTemperatureUUID =
      'd0b12771-6b7d-4635-ab5a-e3abdd621537';
  static const String targetTemperatureUUID =
      'd0b12771-6b7d-4635-ab5a-e3abdd621537';
  static const String currentTemperatureUUID =
      'd0b12773-6b7d-4635-ab5a-e3abdd621537';
  static const String decreaseTemperatureUUID =
      'd0b12771-6b7d-4635-ab5a-e3abdd621537';
  static const String heatingElementControlUUID =
      'd0b12773-6b7d-4635-ab5a-e3abdd621537';
  static const String heatStatus = 'd0b12775-6b7d-4635-ab5a-e3abdd621537';
  static const String maxSetpointUUID = '00002a4c-0000-1000-8000-00805f9b350d';
  static const String minSetpointUUID = '00002a4d-0000-1000-8000-00805f9b350e';

  // Device Control Service UUIDs
  static const String deviceControlServiceUUID =
      'a5e1dc00-5313-45c9-a0c1-************';
  static const String powerStateUUID = 'a5e1dc01-5313-45c9-a0c1-************';
  static const String therapyStateUUID = 'a5e1dc02-5313-45c9-a0c1-************';
}

// I/flutter (30126): Service UUID: 1801
// I/flutter (30126): Characteristic UUID: 2a05
// I/flutter (30126): Characteristic UUID: 2b29
// I/flutter (30126): Characteristic UUID: 2b2a
// I/flutter (30126): Service UUID: 1800
// I/flutter (30126): Characteristic UUID: 2a00
// I/flutter (30126): Characteristic UUID: 2a01
// I/flutter (30126): Characteristic UUID: 2a04
// I/flutter (30126): Service UUID: 180f
// I/flutter (30126): Characteristic UUID: 2a19
// I/flutter (30126): Service UUID: a5e1dc00-5313-45c9-a0c1-************
// I/flutter (30126): Characteristic UUID: a5e1dc01-5313-45c9-a0c1-************
// I/flutter (30126): Characteristic UUID: a5e1dc02-5313-45c9-a0c1-************
// I/flutter (30126): Service UUID: 180a
// I/flutter (30126): Characteristic UUID: 2a24
// I/flutter (30126): Characteristic UUID: 2a29
// I/flutter (30126): Characteristic UUID: 2a25
// I/flutter (30126): Characteristic UUID: 2a26
// I/flutter (30126): Characteristic UUID: 2a27
// I/flutter (30126): Service UUID: b5e1dc00-5313-45c9-a0c1-************
// I/flutter (30126): Characteristic UUID: b5e1dc01-5313-45c9-a0c1-************
// I/flutter (30126): Service UUID: d0b12770-6b7d-4635-ab5a-e3abdd621537
// I/flutter (30126): Characteristic UUID: d0b12771-6b7d-4635-ab5a-e3abdd621537
// I/flutter (30126): Characteristic UUID: d0b12772-6b7d-4635-ab5a-e3abdd621537
// I/flutter (30126): Characteristic UUID: d0b12773-6b7d-4635-ab5a-e3abdd621537
// I/flutter (30126): Service UUID: 23289aa0-670c-4635-8b38-e1ab58c0e9c4
// I/flutter (30126): Characteristic UUID: 23289aa1-670c-44f3-8b38-e1ab58c0e9c4
// I/flutter (30126): Characteristic UUID: 23289aa2-670c-44f3-8b38-e1ab58c0e9c4
// I/flutter (30126): Characteristic UUID: 23289aa3-670c-44f3-8b38-e1ab58c0e9c4
// I/flutter (30126): Characteristic UUID: 23289aa4-670c-44f3-8b38-e1ab58c0e9c4
