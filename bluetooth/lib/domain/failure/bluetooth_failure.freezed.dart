// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bluetooth_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BluetoothFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BluetoothFailureCopyWith<$Res> {
  factory $BluetoothFailureCopyWith(
          BluetoothFailure value, $Res Function(BluetoothFailure) then) =
      _$BluetoothFailureCopyWithImpl<$Res, BluetoothFailure>;
}

/// @nodoc
class _$BluetoothFailureCopyWithImpl<$Res, $Val extends BluetoothFailure>
    implements $BluetoothFailureCopyWith<$Res> {
  _$BluetoothFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$BluetoothDisabledImplCopyWith<$Res> {
  factory _$$BluetoothDisabledImplCopyWith(_$BluetoothDisabledImpl value,
          $Res Function(_$BluetoothDisabledImpl) then) =
      __$$BluetoothDisabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BluetoothDisabledImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$BluetoothDisabledImpl>
    implements _$$BluetoothDisabledImplCopyWith<$Res> {
  __$$BluetoothDisabledImplCopyWithImpl(_$BluetoothDisabledImpl _value,
      $Res Function(_$BluetoothDisabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$BluetoothDisabledImpl implements BluetoothDisabled {
  const _$BluetoothDisabledImpl();

  @override
  String toString() {
    return 'BluetoothFailure.bluetoothDisabled()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BluetoothDisabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return bluetoothDisabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return bluetoothDisabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (bluetoothDisabled != null) {
      return bluetoothDisabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return bluetoothDisabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return bluetoothDisabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (bluetoothDisabled != null) {
      return bluetoothDisabled(this);
    }
    return orElse();
  }
}

abstract class BluetoothDisabled implements BluetoothFailure {
  const factory BluetoothDisabled() = _$BluetoothDisabledImpl;
}

/// @nodoc
abstract class _$$DeviceConnectionFailedImplCopyWith<$Res> {
  factory _$$DeviceConnectionFailedImplCopyWith(
          _$DeviceConnectionFailedImpl value,
          $Res Function(_$DeviceConnectionFailedImpl) then) =
      __$$DeviceConnectionFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeviceConnectionFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$DeviceConnectionFailedImpl>
    implements _$$DeviceConnectionFailedImplCopyWith<$Res> {
  __$$DeviceConnectionFailedImplCopyWithImpl(
      _$DeviceConnectionFailedImpl _value,
      $Res Function(_$DeviceConnectionFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeviceConnectionFailedImpl implements DeviceConnectionFailed {
  const _$DeviceConnectionFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.deviceConnectionFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceConnectionFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return deviceConnectionFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return deviceConnectionFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (deviceConnectionFailed != null) {
      return deviceConnectionFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return deviceConnectionFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return deviceConnectionFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (deviceConnectionFailed != null) {
      return deviceConnectionFailed(this);
    }
    return orElse();
  }
}

abstract class DeviceConnectionFailed implements BluetoothFailure {
  const factory DeviceConnectionFailed() = _$DeviceConnectionFailedImpl;
}

/// @nodoc
abstract class _$$NoDevicesFoundImplCopyWith<$Res> {
  factory _$$NoDevicesFoundImplCopyWith(_$NoDevicesFoundImpl value,
          $Res Function(_$NoDevicesFoundImpl) then) =
      __$$NoDevicesFoundImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NoDevicesFoundImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$NoDevicesFoundImpl>
    implements _$$NoDevicesFoundImplCopyWith<$Res> {
  __$$NoDevicesFoundImplCopyWithImpl(
      _$NoDevicesFoundImpl _value, $Res Function(_$NoDevicesFoundImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NoDevicesFoundImpl implements NoDevicesFound {
  const _$NoDevicesFoundImpl();

  @override
  String toString() {
    return 'BluetoothFailure.noDevicesFound()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NoDevicesFoundImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return noDevicesFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return noDevicesFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (noDevicesFound != null) {
      return noDevicesFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return noDevicesFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return noDevicesFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (noDevicesFound != null) {
      return noDevicesFound(this);
    }
    return orElse();
  }
}

abstract class NoDevicesFound implements BluetoothFailure {
  const factory NoDevicesFound() = _$NoDevicesFoundImpl;
}

/// @nodoc
abstract class _$$SearchTimeoutImplCopyWith<$Res> {
  factory _$$SearchTimeoutImplCopyWith(
          _$SearchTimeoutImpl value, $Res Function(_$SearchTimeoutImpl) then) =
      __$$SearchTimeoutImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SearchTimeoutImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$SearchTimeoutImpl>
    implements _$$SearchTimeoutImplCopyWith<$Res> {
  __$$SearchTimeoutImplCopyWithImpl(
      _$SearchTimeoutImpl _value, $Res Function(_$SearchTimeoutImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SearchTimeoutImpl implements SearchTimeout {
  const _$SearchTimeoutImpl();

  @override
  String toString() {
    return 'BluetoothFailure.searchTimeout()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SearchTimeoutImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return searchTimeout();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return searchTimeout?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (searchTimeout != null) {
      return searchTimeout();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return searchTimeout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return searchTimeout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (searchTimeout != null) {
      return searchTimeout(this);
    }
    return orElse();
  }
}

abstract class SearchTimeout implements BluetoothFailure {
  const factory SearchTimeout() = _$SearchTimeoutImpl;
}

/// @nodoc
abstract class _$$PermissionDeniedImplCopyWith<$Res> {
  factory _$$PermissionDeniedImplCopyWith(_$PermissionDeniedImpl value,
          $Res Function(_$PermissionDeniedImpl) then) =
      __$$PermissionDeniedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PermissionDeniedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$PermissionDeniedImpl>
    implements _$$PermissionDeniedImplCopyWith<$Res> {
  __$$PermissionDeniedImplCopyWithImpl(_$PermissionDeniedImpl _value,
      $Res Function(_$PermissionDeniedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PermissionDeniedImpl implements PermissionDenied {
  const _$PermissionDeniedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.permissionDenied()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PermissionDeniedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return permissionDenied();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return permissionDenied?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (permissionDenied != null) {
      return permissionDenied();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return permissionDenied(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return permissionDenied?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (permissionDenied != null) {
      return permissionDenied(this);
    }
    return orElse();
  }
}

abstract class PermissionDenied implements BluetoothFailure {
  const factory PermissionDenied() = _$PermissionDeniedImpl;
}

/// @nodoc
abstract class _$$UnexpectedImplCopyWith<$Res> {
  factory _$$UnexpectedImplCopyWith(
          _$UnexpectedImpl value, $Res Function(_$UnexpectedImpl) then) =
      __$$UnexpectedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$UnexpectedImpl>
    implements _$$UnexpectedImplCopyWith<$Res> {
  __$$UnexpectedImplCopyWithImpl(
      _$UnexpectedImpl _value, $Res Function(_$UnexpectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnexpectedImpl implements Unexpected {
  const _$UnexpectedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.unexpected()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnexpectedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return unexpected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return unexpected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class Unexpected implements BluetoothFailure {
  const factory Unexpected() = _$UnexpectedImpl;
}

/// @nodoc
abstract class _$$SaveDeviceFailedImplCopyWith<$Res> {
  factory _$$SaveDeviceFailedImplCopyWith(_$SaveDeviceFailedImpl value,
          $Res Function(_$SaveDeviceFailedImpl) then) =
      __$$SaveDeviceFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SaveDeviceFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$SaveDeviceFailedImpl>
    implements _$$SaveDeviceFailedImplCopyWith<$Res> {
  __$$SaveDeviceFailedImplCopyWithImpl(_$SaveDeviceFailedImpl _value,
      $Res Function(_$SaveDeviceFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SaveDeviceFailedImpl implements SaveDeviceFailed {
  const _$SaveDeviceFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.saveDeviceFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SaveDeviceFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return saveDeviceFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return saveDeviceFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (saveDeviceFailed != null) {
      return saveDeviceFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return saveDeviceFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return saveDeviceFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (saveDeviceFailed != null) {
      return saveDeviceFailed(this);
    }
    return orElse();
  }
}

abstract class SaveDeviceFailed implements BluetoothFailure {
  const factory SaveDeviceFailed() = _$SaveDeviceFailedImpl;
}

/// @nodoc
abstract class _$$GetSavedDeviceFailedImplCopyWith<$Res> {
  factory _$$GetSavedDeviceFailedImplCopyWith(_$GetSavedDeviceFailedImpl value,
          $Res Function(_$GetSavedDeviceFailedImpl) then) =
      __$$GetSavedDeviceFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetSavedDeviceFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$GetSavedDeviceFailedImpl>
    implements _$$GetSavedDeviceFailedImplCopyWith<$Res> {
  __$$GetSavedDeviceFailedImplCopyWithImpl(_$GetSavedDeviceFailedImpl _value,
      $Res Function(_$GetSavedDeviceFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetSavedDeviceFailedImpl implements GetSavedDeviceFailed {
  const _$GetSavedDeviceFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.getSavedDeviceFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetSavedDeviceFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return getSavedDeviceFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return getSavedDeviceFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (getSavedDeviceFailed != null) {
      return getSavedDeviceFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return getSavedDeviceFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return getSavedDeviceFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (getSavedDeviceFailed != null) {
      return getSavedDeviceFailed(this);
    }
    return orElse();
  }
}

abstract class GetSavedDeviceFailed implements BluetoothFailure {
  const factory GetSavedDeviceFailed() = _$GetSavedDeviceFailedImpl;
}

/// @nodoc
abstract class _$$SendCommandFailedImplCopyWith<$Res> {
  factory _$$SendCommandFailedImplCopyWith(_$SendCommandFailedImpl value,
          $Res Function(_$SendCommandFailedImpl) then) =
      __$$SendCommandFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SendCommandFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$SendCommandFailedImpl>
    implements _$$SendCommandFailedImplCopyWith<$Res> {
  __$$SendCommandFailedImplCopyWithImpl(_$SendCommandFailedImpl _value,
      $Res Function(_$SendCommandFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SendCommandFailedImpl implements SendCommandFailed {
  const _$SendCommandFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.sendCommandFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SendCommandFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return sendCommandFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return sendCommandFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (sendCommandFailed != null) {
      return sendCommandFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return sendCommandFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return sendCommandFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (sendCommandFailed != null) {
      return sendCommandFailed(this);
    }
    return orElse();
  }
}

abstract class SendCommandFailed implements BluetoothFailure {
  const factory SendCommandFailed() = _$SendCommandFailedImpl;
}

/// @nodoc
abstract class _$$GetCommandFailedImplCopyWith<$Res> {
  factory _$$GetCommandFailedImplCopyWith(_$GetCommandFailedImpl value,
          $Res Function(_$GetCommandFailedImpl) then) =
      __$$GetCommandFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetCommandFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$GetCommandFailedImpl>
    implements _$$GetCommandFailedImplCopyWith<$Res> {
  __$$GetCommandFailedImplCopyWithImpl(_$GetCommandFailedImpl _value,
      $Res Function(_$GetCommandFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetCommandFailedImpl implements GetCommandFailed {
  const _$GetCommandFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.getCommandFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetCommandFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return getCommandFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return getCommandFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (getCommandFailed != null) {
      return getCommandFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return getCommandFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return getCommandFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (getCommandFailed != null) {
      return getCommandFailed(this);
    }
    return orElse();
  }
}

abstract class GetCommandFailed implements BluetoothFailure {
  const factory GetCommandFailed() = _$GetCommandFailedImpl;
}

/// @nodoc
abstract class _$$ReconnectFailedImplCopyWith<$Res> {
  factory _$$ReconnectFailedImplCopyWith(_$ReconnectFailedImpl value,
          $Res Function(_$ReconnectFailedImpl) then) =
      __$$ReconnectFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ReconnectFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$ReconnectFailedImpl>
    implements _$$ReconnectFailedImplCopyWith<$Res> {
  __$$ReconnectFailedImplCopyWithImpl(
      _$ReconnectFailedImpl _value, $Res Function(_$ReconnectFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ReconnectFailedImpl implements ReconnectFailed {
  const _$ReconnectFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.reconnectFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ReconnectFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return reconnectFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return reconnectFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (reconnectFailed != null) {
      return reconnectFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return reconnectFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return reconnectFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (reconnectFailed != null) {
      return reconnectFailed(this);
    }
    return orElse();
  }
}

abstract class ReconnectFailed implements BluetoothFailure {
  const factory ReconnectFailed() = _$ReconnectFailedImpl;
}

/// @nodoc
abstract class _$$DisconnectionFailedImplCopyWith<$Res> {
  factory _$$DisconnectionFailedImplCopyWith(_$DisconnectionFailedImpl value,
          $Res Function(_$DisconnectionFailedImpl) then) =
      __$$DisconnectionFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DisconnectionFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$DisconnectionFailedImpl>
    implements _$$DisconnectionFailedImplCopyWith<$Res> {
  __$$DisconnectionFailedImplCopyWithImpl(_$DisconnectionFailedImpl _value,
      $Res Function(_$DisconnectionFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DisconnectionFailedImpl implements DisconnectionFailed {
  const _$DisconnectionFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.disconnectionFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisconnectionFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return disconnectionFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return disconnectionFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (disconnectionFailed != null) {
      return disconnectionFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return disconnectionFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return disconnectionFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (disconnectionFailed != null) {
      return disconnectionFailed(this);
    }
    return orElse();
  }
}

abstract class DisconnectionFailed implements BluetoothFailure {
  const factory DisconnectionFailed() = _$DisconnectionFailedImpl;
}

/// @nodoc
abstract class _$$DeviceConnectionLostImplCopyWith<$Res> {
  factory _$$DeviceConnectionLostImplCopyWith(_$DeviceConnectionLostImpl value,
          $Res Function(_$DeviceConnectionLostImpl) then) =
      __$$DeviceConnectionLostImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeviceConnectionLostImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$DeviceConnectionLostImpl>
    implements _$$DeviceConnectionLostImplCopyWith<$Res> {
  __$$DeviceConnectionLostImplCopyWithImpl(_$DeviceConnectionLostImpl _value,
      $Res Function(_$DeviceConnectionLostImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeviceConnectionLostImpl implements DeviceConnectionLost {
  const _$DeviceConnectionLostImpl();

  @override
  String toString() {
    return 'BluetoothFailure.deviceConnectionLost()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceConnectionLostImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return deviceConnectionLost();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return deviceConnectionLost?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (deviceConnectionLost != null) {
      return deviceConnectionLost();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return deviceConnectionLost(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return deviceConnectionLost?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (deviceConnectionLost != null) {
      return deviceConnectionLost(this);
    }
    return orElse();
  }
}

abstract class DeviceConnectionLost implements BluetoothFailure {
  const factory DeviceConnectionLost() = _$DeviceConnectionLostImpl;
}

/// @nodoc
abstract class _$$StopScanningFailedImplCopyWith<$Res> {
  factory _$$StopScanningFailedImplCopyWith(_$StopScanningFailedImpl value,
          $Res Function(_$StopScanningFailedImpl) then) =
      __$$StopScanningFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StopScanningFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$StopScanningFailedImpl>
    implements _$$StopScanningFailedImplCopyWith<$Res> {
  __$$StopScanningFailedImplCopyWithImpl(_$StopScanningFailedImpl _value,
      $Res Function(_$StopScanningFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StopScanningFailedImpl implements StopScanningFailed {
  const _$StopScanningFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.stopScanningFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StopScanningFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
  }) {
    return stopScanningFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
  }) {
    return stopScanningFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (stopScanningFailed != null) {
      return stopScanningFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
  }) {
    return stopScanningFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
  }) {
    return stopScanningFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    required TResult orElse(),
  }) {
    if (stopScanningFailed != null) {
      return stopScanningFailed(this);
    }
    return orElse();
  }
}

abstract class StopScanningFailed implements BluetoothFailure {
  const factory StopScanningFailed() = _$StopScanningFailedImpl;
}
