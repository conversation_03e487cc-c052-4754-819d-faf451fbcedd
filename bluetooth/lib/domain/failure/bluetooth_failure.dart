import 'package:freezed_annotation/freezed_annotation.dart';

part 'bluetooth_failure.freezed.dart';

// This class represents different types of Bluetooth operation failures.
// It uses the Freezed package for union types.
@freezed
abstract class BluetoothFailure with _$BluetoothFailure {
  // Represents a failure due to Bluetooth being disabled.
  const factory BluetoothFailure.bluetoothDisabled() = BluetoothDisabled;

  // Represents a failure where the device failed to connect.
  const factory BluetoothFailure.deviceConnectionFailed() = DeviceConnectionFailed;

  // Represents a failure where no devices were found during the scan.
  const factory BluetoothFailure.noDevicesFound() = NoDevicesFound;

  // Represents a failure due to a timeout during the device search.
  const factory BluetoothFailure.searchTimeout() = SearchTimeout;

  // Represents a failure due to a permission issue.
  const factory BluetoothFailure.permissionDenied() = PermissionDenied;
 // Represents a failure due to an unexpected error.
  const factory BluetoothFailure.unexpected() = Unexpected;

  // Represents a failure to save the device.
  const factory BluetoothFailure.saveDeviceFailed() = SaveDeviceFailed;

  // Represents a failure to get the saved device.
  const factory BluetoothFailure.getSavedDeviceFailed() = GetSavedDeviceFailed;

  // send command failed
  const factory BluetoothFailure.sendCommandFailed() = SendCommandFailed;
  // get command failed
  const factory BluetoothFailure.getCommandFailed() = GetCommandFailed;
  //reconnect failed
  const factory BluetoothFailure.reconnectFailed() = ReconnectFailed;
  //disconnect failed
  const factory BluetoothFailure.disconnectionFailed() = DisconnectionFailed;

  // Represents a failure to get the device connection state.
  const factory BluetoothFailure.deviceConnectionLost() = DeviceConnectionLost;
  // Represents a failure to stop scanning for devices.
  const factory BluetoothFailure.stopScanningFailed() = StopScanningFailed;


}

