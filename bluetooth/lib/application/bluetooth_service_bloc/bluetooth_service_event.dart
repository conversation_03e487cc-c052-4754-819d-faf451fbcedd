// bluetooth_service_event.dart
part of 'bluetooth_service_bloc.dart';
@freezed
class BluetoothServiceEvent with _$BluetoothServiceEvent {
  // Check if Bluetooth is enabled
  const factory BluetoothServiceEvent.checkBluetooth() = CheckBluetooth;

  // Check for most recently connected device
  const factory BluetoothServiceEvent.checkRecentDevice() = CheckRecentDevice;

  //saved devices received
// Add to bluetooth_service_event.dart
// Handle saved devices received from stream
  const factory BluetoothServiceEvent.savedDevicesReceived(
      Either<BluetoothFailure, List<DeviceModel>> failureOrDevices) = SavedDevicesReceived;

  // Start scanning for nearby devices
  const factory BluetoothServiceEvent.startSearch() = StartSearch;

  // Connect to a specific device by ID
  const factory BluetoothServiceEvent.connectToDevice(String deviceId, {bool? initial}) = ConnectToDevice;

  // Handle device disconnection event
  const factory BluetoothServiceEvent.deviceDisconnected(BluetoothDevice device) = DeviceDisconnected;

  // Request to disconnect a device
  const factory BluetoothServiceEvent.disconnectDevice(BluetoothDevice device) = DisconnectDevice;

  // Start listening to device connection state
  const factory BluetoothServiceEvent.listenToDevice(BluetoothDevice device) = ListenToDevice;

  // Check current connection status
  const factory BluetoothServiceEvent.checkConnection() = CheckConnection;

  // Handle device reconnection event
  const factory BluetoothServiceEvent.reconnectDevice(BluetoothDevice device) = ReconnectDevice;


}