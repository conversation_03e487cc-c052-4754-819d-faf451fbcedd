// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bluetooth_service_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BluetoothServiceEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BluetoothServiceEventCopyWith<$Res> {
  factory $BluetoothServiceEventCopyWith(BluetoothServiceEvent value,
          $Res Function(BluetoothServiceEvent) then) =
      _$BluetoothServiceEventCopyWithImpl<$Res, BluetoothServiceEvent>;
}

/// @nodoc
class _$BluetoothServiceEventCopyWithImpl<$Res,
        $Val extends BluetoothServiceEvent>
    implements $BluetoothServiceEventCopyWith<$Res> {
  _$BluetoothServiceEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CheckBluetoothImplCopyWith<$Res> {
  factory _$$CheckBluetoothImplCopyWith(_$CheckBluetoothImpl value,
          $Res Function(_$CheckBluetoothImpl) then) =
      __$$CheckBluetoothImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckBluetoothImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$CheckBluetoothImpl>
    implements _$$CheckBluetoothImplCopyWith<$Res> {
  __$$CheckBluetoothImplCopyWithImpl(
      _$CheckBluetoothImpl _value, $Res Function(_$CheckBluetoothImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckBluetoothImpl implements CheckBluetooth {
  const _$CheckBluetoothImpl();

  @override
  String toString() {
    return 'BluetoothServiceEvent.checkBluetooth()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckBluetoothImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return checkBluetooth();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return checkBluetooth?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (checkBluetooth != null) {
      return checkBluetooth();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return checkBluetooth(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return checkBluetooth?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (checkBluetooth != null) {
      return checkBluetooth(this);
    }
    return orElse();
  }
}

abstract class CheckBluetooth implements BluetoothServiceEvent {
  const factory CheckBluetooth() = _$CheckBluetoothImpl;
}

/// @nodoc
abstract class _$$CheckRecentDeviceImplCopyWith<$Res> {
  factory _$$CheckRecentDeviceImplCopyWith(_$CheckRecentDeviceImpl value,
          $Res Function(_$CheckRecentDeviceImpl) then) =
      __$$CheckRecentDeviceImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckRecentDeviceImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$CheckRecentDeviceImpl>
    implements _$$CheckRecentDeviceImplCopyWith<$Res> {
  __$$CheckRecentDeviceImplCopyWithImpl(_$CheckRecentDeviceImpl _value,
      $Res Function(_$CheckRecentDeviceImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckRecentDeviceImpl implements CheckRecentDevice {
  const _$CheckRecentDeviceImpl();

  @override
  String toString() {
    return 'BluetoothServiceEvent.checkRecentDevice()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckRecentDeviceImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return checkRecentDevice();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return checkRecentDevice?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (checkRecentDevice != null) {
      return checkRecentDevice();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return checkRecentDevice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return checkRecentDevice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (checkRecentDevice != null) {
      return checkRecentDevice(this);
    }
    return orElse();
  }
}

abstract class CheckRecentDevice implements BluetoothServiceEvent {
  const factory CheckRecentDevice() = _$CheckRecentDeviceImpl;
}

/// @nodoc
abstract class _$$SavedDevicesReceivedImplCopyWith<$Res> {
  factory _$$SavedDevicesReceivedImplCopyWith(_$SavedDevicesReceivedImpl value,
          $Res Function(_$SavedDevicesReceivedImpl) then) =
      __$$SavedDevicesReceivedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Either<BluetoothFailure, List<DeviceModel>> failureOrDevices});
}

/// @nodoc
class __$$SavedDevicesReceivedImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res,
        _$SavedDevicesReceivedImpl>
    implements _$$SavedDevicesReceivedImplCopyWith<$Res> {
  __$$SavedDevicesReceivedImplCopyWithImpl(_$SavedDevicesReceivedImpl _value,
      $Res Function(_$SavedDevicesReceivedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrDevices = null,
  }) {
    return _then(_$SavedDevicesReceivedImpl(
      null == failureOrDevices
          ? _value.failureOrDevices
          : failureOrDevices // ignore: cast_nullable_to_non_nullable
              as Either<BluetoothFailure, List<DeviceModel>>,
    ));
  }
}

/// @nodoc

class _$SavedDevicesReceivedImpl implements SavedDevicesReceived {
  const _$SavedDevicesReceivedImpl(this.failureOrDevices);

  @override
  final Either<BluetoothFailure, List<DeviceModel>> failureOrDevices;

  @override
  String toString() {
    return 'BluetoothServiceEvent.savedDevicesReceived(failureOrDevices: $failureOrDevices)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SavedDevicesReceivedImpl &&
            (identical(other.failureOrDevices, failureOrDevices) ||
                other.failureOrDevices == failureOrDevices));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrDevices);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SavedDevicesReceivedImplCopyWith<_$SavedDevicesReceivedImpl>
      get copyWith =>
          __$$SavedDevicesReceivedImplCopyWithImpl<_$SavedDevicesReceivedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return savedDevicesReceived(failureOrDevices);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return savedDevicesReceived?.call(failureOrDevices);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (savedDevicesReceived != null) {
      return savedDevicesReceived(failureOrDevices);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return savedDevicesReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return savedDevicesReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (savedDevicesReceived != null) {
      return savedDevicesReceived(this);
    }
    return orElse();
  }
}

abstract class SavedDevicesReceived implements BluetoothServiceEvent {
  const factory SavedDevicesReceived(
          final Either<BluetoothFailure, List<DeviceModel>> failureOrDevices) =
      _$SavedDevicesReceivedImpl;

  Either<BluetoothFailure, List<DeviceModel>> get failureOrDevices;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SavedDevicesReceivedImplCopyWith<_$SavedDevicesReceivedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StartSearchImplCopyWith<$Res> {
  factory _$$StartSearchImplCopyWith(
          _$StartSearchImpl value, $Res Function(_$StartSearchImpl) then) =
      __$$StartSearchImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartSearchImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$StartSearchImpl>
    implements _$$StartSearchImplCopyWith<$Res> {
  __$$StartSearchImplCopyWithImpl(
      _$StartSearchImpl _value, $Res Function(_$StartSearchImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartSearchImpl implements StartSearch {
  const _$StartSearchImpl();

  @override
  String toString() {
    return 'BluetoothServiceEvent.startSearch()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartSearchImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return startSearch();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return startSearch?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (startSearch != null) {
      return startSearch();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return startSearch(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return startSearch?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (startSearch != null) {
      return startSearch(this);
    }
    return orElse();
  }
}

abstract class StartSearch implements BluetoothServiceEvent {
  const factory StartSearch() = _$StartSearchImpl;
}

/// @nodoc
abstract class _$$ConnectToDeviceImplCopyWith<$Res> {
  factory _$$ConnectToDeviceImplCopyWith(_$ConnectToDeviceImpl value,
          $Res Function(_$ConnectToDeviceImpl) then) =
      __$$ConnectToDeviceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String deviceId, bool? initial});
}

/// @nodoc
class __$$ConnectToDeviceImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$ConnectToDeviceImpl>
    implements _$$ConnectToDeviceImplCopyWith<$Res> {
  __$$ConnectToDeviceImplCopyWithImpl(
      _$ConnectToDeviceImpl _value, $Res Function(_$ConnectToDeviceImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceId = null,
    Object? initial = freezed,
  }) {
    return _then(_$ConnectToDeviceImpl(
      null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      initial: freezed == initial
          ? _value.initial
          : initial // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$ConnectToDeviceImpl implements ConnectToDevice {
  const _$ConnectToDeviceImpl(this.deviceId, {this.initial});

  @override
  final String deviceId;
  @override
  final bool? initial;

  @override
  String toString() {
    return 'BluetoothServiceEvent.connectToDevice(deviceId: $deviceId, initial: $initial)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConnectToDeviceImpl &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.initial, initial) || other.initial == initial));
  }

  @override
  int get hashCode => Object.hash(runtimeType, deviceId, initial);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConnectToDeviceImplCopyWith<_$ConnectToDeviceImpl> get copyWith =>
      __$$ConnectToDeviceImplCopyWithImpl<_$ConnectToDeviceImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return connectToDevice(deviceId, initial);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return connectToDevice?.call(deviceId, initial);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (connectToDevice != null) {
      return connectToDevice(deviceId, initial);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return connectToDevice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return connectToDevice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (connectToDevice != null) {
      return connectToDevice(this);
    }
    return orElse();
  }
}

abstract class ConnectToDevice implements BluetoothServiceEvent {
  const factory ConnectToDevice(final String deviceId, {final bool? initial}) =
      _$ConnectToDeviceImpl;

  String get deviceId;
  bool? get initial;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConnectToDeviceImplCopyWith<_$ConnectToDeviceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeviceDisconnectedImplCopyWith<$Res> {
  factory _$$DeviceDisconnectedImplCopyWith(_$DeviceDisconnectedImpl value,
          $Res Function(_$DeviceDisconnectedImpl) then) =
      __$$DeviceDisconnectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$DeviceDisconnectedImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$DeviceDisconnectedImpl>
    implements _$$DeviceDisconnectedImplCopyWith<$Res> {
  __$$DeviceDisconnectedImplCopyWithImpl(_$DeviceDisconnectedImpl _value,
      $Res Function(_$DeviceDisconnectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$DeviceDisconnectedImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$DeviceDisconnectedImpl implements DeviceDisconnected {
  const _$DeviceDisconnectedImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'BluetoothServiceEvent.deviceDisconnected(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceDisconnectedImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceDisconnectedImplCopyWith<_$DeviceDisconnectedImpl> get copyWith =>
      __$$DeviceDisconnectedImplCopyWithImpl<_$DeviceDisconnectedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return deviceDisconnected(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return deviceDisconnected?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (deviceDisconnected != null) {
      return deviceDisconnected(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return deviceDisconnected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return deviceDisconnected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (deviceDisconnected != null) {
      return deviceDisconnected(this);
    }
    return orElse();
  }
}

abstract class DeviceDisconnected implements BluetoothServiceEvent {
  const factory DeviceDisconnected(final BluetoothDevice device) =
      _$DeviceDisconnectedImpl;

  BluetoothDevice get device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceDisconnectedImplCopyWith<_$DeviceDisconnectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DisconnectDeviceImplCopyWith<$Res> {
  factory _$$DisconnectDeviceImplCopyWith(_$DisconnectDeviceImpl value,
          $Res Function(_$DisconnectDeviceImpl) then) =
      __$$DisconnectDeviceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$DisconnectDeviceImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$DisconnectDeviceImpl>
    implements _$$DisconnectDeviceImplCopyWith<$Res> {
  __$$DisconnectDeviceImplCopyWithImpl(_$DisconnectDeviceImpl _value,
      $Res Function(_$DisconnectDeviceImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$DisconnectDeviceImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$DisconnectDeviceImpl implements DisconnectDevice {
  const _$DisconnectDeviceImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'BluetoothServiceEvent.disconnectDevice(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisconnectDeviceImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DisconnectDeviceImplCopyWith<_$DisconnectDeviceImpl> get copyWith =>
      __$$DisconnectDeviceImplCopyWithImpl<_$DisconnectDeviceImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return disconnectDevice(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return disconnectDevice?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (disconnectDevice != null) {
      return disconnectDevice(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return disconnectDevice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return disconnectDevice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (disconnectDevice != null) {
      return disconnectDevice(this);
    }
    return orElse();
  }
}

abstract class DisconnectDevice implements BluetoothServiceEvent {
  const factory DisconnectDevice(final BluetoothDevice device) =
      _$DisconnectDeviceImpl;

  BluetoothDevice get device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DisconnectDeviceImplCopyWith<_$DisconnectDeviceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListenToDeviceImplCopyWith<$Res> {
  factory _$$ListenToDeviceImplCopyWith(_$ListenToDeviceImpl value,
          $Res Function(_$ListenToDeviceImpl) then) =
      __$$ListenToDeviceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$ListenToDeviceImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$ListenToDeviceImpl>
    implements _$$ListenToDeviceImplCopyWith<$Res> {
  __$$ListenToDeviceImplCopyWithImpl(
      _$ListenToDeviceImpl _value, $Res Function(_$ListenToDeviceImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$ListenToDeviceImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$ListenToDeviceImpl implements ListenToDevice {
  const _$ListenToDeviceImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'BluetoothServiceEvent.listenToDevice(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListenToDeviceImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListenToDeviceImplCopyWith<_$ListenToDeviceImpl> get copyWith =>
      __$$ListenToDeviceImplCopyWithImpl<_$ListenToDeviceImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return listenToDevice(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return listenToDevice?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (listenToDevice != null) {
      return listenToDevice(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return listenToDevice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return listenToDevice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (listenToDevice != null) {
      return listenToDevice(this);
    }
    return orElse();
  }
}

abstract class ListenToDevice implements BluetoothServiceEvent {
  const factory ListenToDevice(final BluetoothDevice device) =
      _$ListenToDeviceImpl;

  BluetoothDevice get device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListenToDeviceImplCopyWith<_$ListenToDeviceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CheckConnectionImplCopyWith<$Res> {
  factory _$$CheckConnectionImplCopyWith(_$CheckConnectionImpl value,
          $Res Function(_$CheckConnectionImpl) then) =
      __$$CheckConnectionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckConnectionImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$CheckConnectionImpl>
    implements _$$CheckConnectionImplCopyWith<$Res> {
  __$$CheckConnectionImplCopyWithImpl(
      _$CheckConnectionImpl _value, $Res Function(_$CheckConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckConnectionImpl implements CheckConnection {
  const _$CheckConnectionImpl();

  @override
  String toString() {
    return 'BluetoothServiceEvent.checkConnection()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckConnectionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return checkConnection();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return checkConnection?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (checkConnection != null) {
      return checkConnection();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return checkConnection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return checkConnection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (checkConnection != null) {
      return checkConnection(this);
    }
    return orElse();
  }
}

abstract class CheckConnection implements BluetoothServiceEvent {
  const factory CheckConnection() = _$CheckConnectionImpl;
}

/// @nodoc
abstract class _$$ReconnectDeviceImplCopyWith<$Res> {
  factory _$$ReconnectDeviceImplCopyWith(_$ReconnectDeviceImpl value,
          $Res Function(_$ReconnectDeviceImpl) then) =
      __$$ReconnectDeviceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$ReconnectDeviceImplCopyWithImpl<$Res>
    extends _$BluetoothServiceEventCopyWithImpl<$Res, _$ReconnectDeviceImpl>
    implements _$$ReconnectDeviceImplCopyWith<$Res> {
  __$$ReconnectDeviceImplCopyWithImpl(
      _$ReconnectDeviceImpl _value, $Res Function(_$ReconnectDeviceImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$ReconnectDeviceImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$ReconnectDeviceImpl implements ReconnectDevice {
  const _$ReconnectDeviceImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'BluetoothServiceEvent.reconnectDevice(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReconnectDeviceImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReconnectDeviceImplCopyWith<_$ReconnectDeviceImpl> get copyWith =>
      __$$ReconnectDeviceImplCopyWithImpl<_$ReconnectDeviceImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkBluetooth,
    required TResult Function() checkRecentDevice,
    required TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)
        savedDevicesReceived,
    required TResult Function() startSearch,
    required TResult Function(String deviceId, bool? initial) connectToDevice,
    required TResult Function(BluetoothDevice device) deviceDisconnected,
    required TResult Function(BluetoothDevice device) disconnectDevice,
    required TResult Function(BluetoothDevice device) listenToDevice,
    required TResult Function() checkConnection,
    required TResult Function(BluetoothDevice device) reconnectDevice,
  }) {
    return reconnectDevice(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkBluetooth,
    TResult? Function()? checkRecentDevice,
    TResult? Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult? Function()? startSearch,
    TResult? Function(String deviceId, bool? initial)? connectToDevice,
    TResult? Function(BluetoothDevice device)? deviceDisconnected,
    TResult? Function(BluetoothDevice device)? disconnectDevice,
    TResult? Function(BluetoothDevice device)? listenToDevice,
    TResult? Function()? checkConnection,
    TResult? Function(BluetoothDevice device)? reconnectDevice,
  }) {
    return reconnectDevice?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkBluetooth,
    TResult Function()? checkRecentDevice,
    TResult Function(
            Either<BluetoothFailure, List<DeviceModel>> failureOrDevices)?
        savedDevicesReceived,
    TResult Function()? startSearch,
    TResult Function(String deviceId, bool? initial)? connectToDevice,
    TResult Function(BluetoothDevice device)? deviceDisconnected,
    TResult Function(BluetoothDevice device)? disconnectDevice,
    TResult Function(BluetoothDevice device)? listenToDevice,
    TResult Function()? checkConnection,
    TResult Function(BluetoothDevice device)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (reconnectDevice != null) {
      return reconnectDevice(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckBluetooth value) checkBluetooth,
    required TResult Function(CheckRecentDevice value) checkRecentDevice,
    required TResult Function(SavedDevicesReceived value) savedDevicesReceived,
    required TResult Function(StartSearch value) startSearch,
    required TResult Function(ConnectToDevice value) connectToDevice,
    required TResult Function(DeviceDisconnected value) deviceDisconnected,
    required TResult Function(DisconnectDevice value) disconnectDevice,
    required TResult Function(ListenToDevice value) listenToDevice,
    required TResult Function(CheckConnection value) checkConnection,
    required TResult Function(ReconnectDevice value) reconnectDevice,
  }) {
    return reconnectDevice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckBluetooth value)? checkBluetooth,
    TResult? Function(CheckRecentDevice value)? checkRecentDevice,
    TResult? Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult? Function(StartSearch value)? startSearch,
    TResult? Function(ConnectToDevice value)? connectToDevice,
    TResult? Function(DeviceDisconnected value)? deviceDisconnected,
    TResult? Function(DisconnectDevice value)? disconnectDevice,
    TResult? Function(ListenToDevice value)? listenToDevice,
    TResult? Function(CheckConnection value)? checkConnection,
    TResult? Function(ReconnectDevice value)? reconnectDevice,
  }) {
    return reconnectDevice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckBluetooth value)? checkBluetooth,
    TResult Function(CheckRecentDevice value)? checkRecentDevice,
    TResult Function(SavedDevicesReceived value)? savedDevicesReceived,
    TResult Function(StartSearch value)? startSearch,
    TResult Function(ConnectToDevice value)? connectToDevice,
    TResult Function(DeviceDisconnected value)? deviceDisconnected,
    TResult Function(DisconnectDevice value)? disconnectDevice,
    TResult Function(ListenToDevice value)? listenToDevice,
    TResult Function(CheckConnection value)? checkConnection,
    TResult Function(ReconnectDevice value)? reconnectDevice,
    required TResult orElse(),
  }) {
    if (reconnectDevice != null) {
      return reconnectDevice(this);
    }
    return orElse();
  }
}

abstract class ReconnectDevice implements BluetoothServiceEvent {
  const factory ReconnectDevice(final BluetoothDevice device) =
      _$ReconnectDeviceImpl;

  BluetoothDevice get device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReconnectDeviceImplCopyWith<_$ReconnectDeviceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$BluetoothServiceState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BluetoothServiceStateCopyWith<$Res> {
  factory $BluetoothServiceStateCopyWith(BluetoothServiceState value,
          $Res Function(BluetoothServiceState) then) =
      _$BluetoothServiceStateCopyWithImpl<$Res, BluetoothServiceState>;
}

/// @nodoc
class _$BluetoothServiceStateCopyWithImpl<$Res,
        $Val extends BluetoothServiceState>
    implements $BluetoothServiceStateCopyWith<$Res> {
  _$BluetoothServiceStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'BluetoothServiceState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements BluetoothServiceState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$BluetoothOnImplCopyWith<$Res> {
  factory _$$BluetoothOnImplCopyWith(
          _$BluetoothOnImpl value, $Res Function(_$BluetoothOnImpl) then) =
      __$$BluetoothOnImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BluetoothOnImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$BluetoothOnImpl>
    implements _$$BluetoothOnImplCopyWith<$Res> {
  __$$BluetoothOnImplCopyWithImpl(
      _$BluetoothOnImpl _value, $Res Function(_$BluetoothOnImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$BluetoothOnImpl implements BluetoothOn {
  const _$BluetoothOnImpl();

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothOn()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BluetoothOnImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return bluetoothOn();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return bluetoothOn?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothOn != null) {
      return bluetoothOn();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return bluetoothOn(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return bluetoothOn?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothOn != null) {
      return bluetoothOn(this);
    }
    return orElse();
  }
}

abstract class BluetoothOn implements BluetoothServiceState {
  const factory BluetoothOn() = _$BluetoothOnImpl;
}

/// @nodoc
abstract class _$$BluetoothOffImplCopyWith<$Res> {
  factory _$$BluetoothOffImplCopyWith(
          _$BluetoothOffImpl value, $Res Function(_$BluetoothOffImpl) then) =
      __$$BluetoothOffImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BluetoothOffImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$BluetoothOffImpl>
    implements _$$BluetoothOffImplCopyWith<$Res> {
  __$$BluetoothOffImplCopyWithImpl(
      _$BluetoothOffImpl _value, $Res Function(_$BluetoothOffImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$BluetoothOffImpl implements BluetoothOff {
  const _$BluetoothOffImpl();

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothOff()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BluetoothOffImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return bluetoothOff();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return bluetoothOff?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothOff != null) {
      return bluetoothOff();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return bluetoothOff(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return bluetoothOff?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothOff != null) {
      return bluetoothOff(this);
    }
    return orElse();
  }
}

abstract class BluetoothOff implements BluetoothServiceState {
  const factory BluetoothOff() = _$BluetoothOffImpl;
}

/// @nodoc
abstract class _$$CheckingRecentDeviceImplCopyWith<$Res> {
  factory _$$CheckingRecentDeviceImplCopyWith(_$CheckingRecentDeviceImpl value,
          $Res Function(_$CheckingRecentDeviceImpl) then) =
      __$$CheckingRecentDeviceImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckingRecentDeviceImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res,
        _$CheckingRecentDeviceImpl>
    implements _$$CheckingRecentDeviceImplCopyWith<$Res> {
  __$$CheckingRecentDeviceImplCopyWithImpl(_$CheckingRecentDeviceImpl _value,
      $Res Function(_$CheckingRecentDeviceImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckingRecentDeviceImpl implements CheckingRecentDevice {
  const _$CheckingRecentDeviceImpl();

  @override
  String toString() {
    return 'BluetoothServiceState.checkingRecentDevice()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckingRecentDeviceImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return checkingRecentDevice();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return checkingRecentDevice?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (checkingRecentDevice != null) {
      return checkingRecentDevice();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return checkingRecentDevice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return checkingRecentDevice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (checkingRecentDevice != null) {
      return checkingRecentDevice(this);
    }
    return orElse();
  }
}

abstract class CheckingRecentDevice implements BluetoothServiceState {
  const factory CheckingRecentDevice() = _$CheckingRecentDeviceImpl;
}

/// @nodoc
abstract class _$$FetchingSavedDevicesImplCopyWith<$Res> {
  factory _$$FetchingSavedDevicesImplCopyWith(_$FetchingSavedDevicesImpl value,
          $Res Function(_$FetchingSavedDevicesImpl) then) =
      __$$FetchingSavedDevicesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FetchingSavedDevicesImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res,
        _$FetchingSavedDevicesImpl>
    implements _$$FetchingSavedDevicesImplCopyWith<$Res> {
  __$$FetchingSavedDevicesImplCopyWithImpl(_$FetchingSavedDevicesImpl _value,
      $Res Function(_$FetchingSavedDevicesImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FetchingSavedDevicesImpl implements FetchingSavedDevices {
  const _$FetchingSavedDevicesImpl();

  @override
  String toString() {
    return 'BluetoothServiceState.fetchingSavedDevices()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FetchingSavedDevicesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return fetchingSavedDevices();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return fetchingSavedDevices?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (fetchingSavedDevices != null) {
      return fetchingSavedDevices();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return fetchingSavedDevices(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return fetchingSavedDevices?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (fetchingSavedDevices != null) {
      return fetchingSavedDevices(this);
    }
    return orElse();
  }
}

abstract class FetchingSavedDevices implements BluetoothServiceState {
  const factory FetchingSavedDevices() = _$FetchingSavedDevicesImpl;
}

/// @nodoc
abstract class _$$BluetoothSearchingImplCopyWith<$Res> {
  factory _$$BluetoothSearchingImplCopyWith(_$BluetoothSearchingImpl value,
          $Res Function(_$BluetoothSearchingImpl) then) =
      __$$BluetoothSearchingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BluetoothSearchingImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$BluetoothSearchingImpl>
    implements _$$BluetoothSearchingImplCopyWith<$Res> {
  __$$BluetoothSearchingImplCopyWithImpl(_$BluetoothSearchingImpl _value,
      $Res Function(_$BluetoothSearchingImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$BluetoothSearchingImpl implements BluetoothSearching {
  const _$BluetoothSearchingImpl();

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothSearching()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BluetoothSearchingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return bluetoothSearching();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return bluetoothSearching?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothSearching != null) {
      return bluetoothSearching();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return bluetoothSearching(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return bluetoothSearching?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothSearching != null) {
      return bluetoothSearching(this);
    }
    return orElse();
  }
}

abstract class BluetoothSearching implements BluetoothServiceState {
  const factory BluetoothSearching() = _$BluetoothSearchingImpl;
}

/// @nodoc
abstract class _$$ConnectingImplCopyWith<$Res> {
  factory _$$ConnectingImplCopyWith(
          _$ConnectingImpl value, $Res Function(_$ConnectingImpl) then) =
      __$$ConnectingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConnectingImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$ConnectingImpl>
    implements _$$ConnectingImplCopyWith<$Res> {
  __$$ConnectingImplCopyWithImpl(
      _$ConnectingImpl _value, $Res Function(_$ConnectingImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConnectingImpl implements Connecting {
  const _$ConnectingImpl();

  @override
  String toString() {
    return 'BluetoothServiceState.connecting()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ConnectingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return connecting();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return connecting?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (connecting != null) {
      return connecting();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return connecting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return connecting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (connecting != null) {
      return connecting(this);
    }
    return orElse();
  }
}

abstract class Connecting implements BluetoothServiceState {
  const factory Connecting() = _$ConnectingImpl;
}

/// @nodoc
abstract class _$$ReconnectingImplCopyWith<$Res> {
  factory _$$ReconnectingImplCopyWith(
          _$ReconnectingImpl value, $Res Function(_$ReconnectingImpl) then) =
      __$$ReconnectingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$ReconnectingImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$ReconnectingImpl>
    implements _$$ReconnectingImplCopyWith<$Res> {
  __$$ReconnectingImplCopyWithImpl(
      _$ReconnectingImpl _value, $Res Function(_$ReconnectingImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$ReconnectingImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$ReconnectingImpl implements Reconnecting {
  const _$ReconnectingImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'BluetoothServiceState.reconnecting(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReconnectingImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReconnectingImplCopyWith<_$ReconnectingImpl> get copyWith =>
      __$$ReconnectingImplCopyWithImpl<_$ReconnectingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return reconnecting(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return reconnecting?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (reconnecting != null) {
      return reconnecting(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return reconnecting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return reconnecting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (reconnecting != null) {
      return reconnecting(this);
    }
    return orElse();
  }
}

abstract class Reconnecting implements BluetoothServiceState {
  const factory Reconnecting(final BluetoothDevice device) = _$ReconnectingImpl;

  BluetoothDevice get device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReconnectingImplCopyWith<_$ReconnectingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SavedDevicesImplCopyWith<$Res> {
  factory _$$SavedDevicesImplCopyWith(
          _$SavedDevicesImpl value, $Res Function(_$SavedDevicesImpl) then) =
      __$$SavedDevicesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<DeviceModel> devices, int version});
}

/// @nodoc
class __$$SavedDevicesImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$SavedDevicesImpl>
    implements _$$SavedDevicesImplCopyWith<$Res> {
  __$$SavedDevicesImplCopyWithImpl(
      _$SavedDevicesImpl _value, $Res Function(_$SavedDevicesImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? devices = null,
    Object? version = null,
  }) {
    return _then(_$SavedDevicesImpl(
      null == devices
          ? _value._devices
          : devices // ignore: cast_nullable_to_non_nullable
              as List<DeviceModel>,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$SavedDevicesImpl implements SavedDevices {
  const _$SavedDevicesImpl(final List<DeviceModel> devices, {this.version = 0})
      : _devices = devices;

  final List<DeviceModel> _devices;
  @override
  List<DeviceModel> get devices {
    if (_devices is EqualUnmodifiableListView) return _devices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_devices);
  }

  @override
  @JsonKey()
  final int version;

  @override
  String toString() {
    return 'BluetoothServiceState.savedDevices(devices: $devices, version: $version)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SavedDevicesImpl &&
            const DeepCollectionEquality().equals(other._devices, _devices) &&
            (identical(other.version, version) || other.version == version));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_devices), version);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SavedDevicesImplCopyWith<_$SavedDevicesImpl> get copyWith =>
      __$$SavedDevicesImplCopyWithImpl<_$SavedDevicesImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return savedDevices(devices, version);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return savedDevices?.call(devices, version);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (savedDevices != null) {
      return savedDevices(devices, version);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return savedDevices(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return savedDevices?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (savedDevices != null) {
      return savedDevices(this);
    }
    return orElse();
  }
}

abstract class SavedDevices implements BluetoothServiceState {
  const factory SavedDevices(final List<DeviceModel> devices,
      {final int version}) = _$SavedDevicesImpl;

  List<DeviceModel> get devices;
  int get version;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SavedDevicesImplCopyWith<_$SavedDevicesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BluetoothAvailableTypeDevicesImplCopyWith<$Res> {
  factory _$$BluetoothAvailableTypeDevicesImplCopyWith(
          _$BluetoothAvailableTypeDevicesImpl value,
          $Res Function(_$BluetoothAvailableTypeDevicesImpl) then) =
      __$$BluetoothAvailableTypeDevicesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<BluetoothDevice?>? devices});
}

/// @nodoc
class __$$BluetoothAvailableTypeDevicesImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res,
        _$BluetoothAvailableTypeDevicesImpl>
    implements _$$BluetoothAvailableTypeDevicesImplCopyWith<$Res> {
  __$$BluetoothAvailableTypeDevicesImplCopyWithImpl(
      _$BluetoothAvailableTypeDevicesImpl _value,
      $Res Function(_$BluetoothAvailableTypeDevicesImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? devices = freezed,
  }) {
    return _then(_$BluetoothAvailableTypeDevicesImpl(
      freezed == devices
          ? _value._devices
          : devices // ignore: cast_nullable_to_non_nullable
              as List<BluetoothDevice?>?,
    ));
  }
}

/// @nodoc

class _$BluetoothAvailableTypeDevicesImpl
    implements BluetoothAvailableTypeDevices {
  const _$BluetoothAvailableTypeDevicesImpl(
      final List<BluetoothDevice?>? devices)
      : _devices = devices;

  final List<BluetoothDevice?>? _devices;
  @override
  List<BluetoothDevice?>? get devices {
    final value = _devices;
    if (value == null) return null;
    if (_devices is EqualUnmodifiableListView) return _devices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothAvailableTypeDevices(devices: $devices)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BluetoothAvailableTypeDevicesImpl &&
            const DeepCollectionEquality().equals(other._devices, _devices));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_devices));

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BluetoothAvailableTypeDevicesImplCopyWith<
          _$BluetoothAvailableTypeDevicesImpl>
      get copyWith => __$$BluetoothAvailableTypeDevicesImplCopyWithImpl<
          _$BluetoothAvailableTypeDevicesImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return bluetoothAvailableTypeDevices(devices);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return bluetoothAvailableTypeDevices?.call(devices);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothAvailableTypeDevices != null) {
      return bluetoothAvailableTypeDevices(devices);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return bluetoothAvailableTypeDevices(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return bluetoothAvailableTypeDevices?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothAvailableTypeDevices != null) {
      return bluetoothAvailableTypeDevices(this);
    }
    return orElse();
  }
}

abstract class BluetoothAvailableTypeDevices implements BluetoothServiceState {
  const factory BluetoothAvailableTypeDevices(
          final List<BluetoothDevice?>? devices) =
      _$BluetoothAvailableTypeDevicesImpl;

  List<BluetoothDevice?>? get devices;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BluetoothAvailableTypeDevicesImplCopyWith<
          _$BluetoothAvailableTypeDevicesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConnectedImplCopyWith<$Res> {
  factory _$$ConnectedImplCopyWith(
          _$ConnectedImpl value, $Res Function(_$ConnectedImpl) then) =
      __$$ConnectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$ConnectedImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$ConnectedImpl>
    implements _$$ConnectedImplCopyWith<$Res> {
  __$$ConnectedImplCopyWithImpl(
      _$ConnectedImpl _value, $Res Function(_$ConnectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$ConnectedImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$ConnectedImpl implements Connected {
  const _$ConnectedImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'BluetoothServiceState.connected(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConnectedImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConnectedImplCopyWith<_$ConnectedImpl> get copyWith =>
      __$$ConnectedImplCopyWithImpl<_$ConnectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return connected(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return connected?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (connected != null) {
      return connected(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return connected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return connected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (connected != null) {
      return connected(this);
    }
    return orElse();
  }
}

abstract class Connected implements BluetoothServiceState {
  const factory Connected(final BluetoothDevice device) = _$ConnectedImpl;

  BluetoothDevice get device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConnectedImplCopyWith<_$ConnectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DisconnectedImplCopyWith<$Res> {
  factory _$$DisconnectedImplCopyWith(
          _$DisconnectedImpl value, $Res Function(_$DisconnectedImpl) then) =
      __$$DisconnectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$DisconnectedImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$DisconnectedImpl>
    implements _$$DisconnectedImplCopyWith<$Res> {
  __$$DisconnectedImplCopyWithImpl(
      _$DisconnectedImpl _value, $Res Function(_$DisconnectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$DisconnectedImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$DisconnectedImpl implements Disconnected {
  const _$DisconnectedImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'BluetoothServiceState.disconnected(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisconnectedImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DisconnectedImplCopyWith<_$DisconnectedImpl> get copyWith =>
      __$$DisconnectedImplCopyWithImpl<_$DisconnectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return disconnected(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return disconnected?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (disconnected != null) {
      return disconnected(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return disconnected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return disconnected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (disconnected != null) {
      return disconnected(this);
    }
    return orElse();
  }
}

abstract class Disconnected implements BluetoothServiceState {
  const factory Disconnected(final BluetoothDevice device) = _$DisconnectedImpl;

  BluetoothDevice get device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DisconnectedImplCopyWith<_$DisconnectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BluetoothErrorImplCopyWith<$Res> {
  factory _$$BluetoothErrorImplCopyWith(_$BluetoothErrorImpl value,
          $Res Function(_$BluetoothErrorImpl) then) =
      __$$BluetoothErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothFailure failure});

  $BluetoothFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$BluetoothErrorImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$BluetoothErrorImpl>
    implements _$$BluetoothErrorImplCopyWith<$Res> {
  __$$BluetoothErrorImplCopyWithImpl(
      _$BluetoothErrorImpl _value, $Res Function(_$BluetoothErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$BluetoothErrorImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as BluetoothFailure,
    ));
  }

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BluetoothFailureCopyWith<$Res> get failure {
    return $BluetoothFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$BluetoothErrorImpl implements BluetoothError {
  const _$BluetoothErrorImpl(this.failure);

  @override
  final BluetoothFailure failure;

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothError(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BluetoothErrorImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BluetoothErrorImplCopyWith<_$BluetoothErrorImpl> get copyWith =>
      __$$BluetoothErrorImplCopyWithImpl<_$BluetoothErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return bluetoothError(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return bluetoothError?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothError != null) {
      return bluetoothError(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return bluetoothError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return bluetoothError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (bluetoothError != null) {
      return bluetoothError(this);
    }
    return orElse();
  }
}

abstract class BluetoothError implements BluetoothServiceState {
  const factory BluetoothError(final BluetoothFailure failure) =
      _$BluetoothErrorImpl;

  BluetoothFailure get failure;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BluetoothErrorImplCopyWith<_$BluetoothErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConnectionIntroImplCopyWith<$Res> {
  factory _$$ConnectionIntroImplCopyWith(_$ConnectionIntroImpl value,
          $Res Function(_$ConnectionIntroImpl) then) =
      __$$ConnectionIntroImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$ConnectionIntroImplCopyWithImpl<$Res>
    extends _$BluetoothServiceStateCopyWithImpl<$Res, _$ConnectionIntroImpl>
    implements _$$ConnectionIntroImplCopyWith<$Res> {
  __$$ConnectionIntroImplCopyWithImpl(
      _$ConnectionIntroImpl _value, $Res Function(_$ConnectionIntroImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$ConnectionIntroImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$ConnectionIntroImpl implements ConnectionIntro {
  const _$ConnectionIntroImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'BluetoothServiceState.connectionIntro(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConnectionIntroImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConnectionIntroImplCopyWith<_$ConnectionIntroImpl> get copyWith =>
      __$$ConnectionIntroImplCopyWithImpl<_$ConnectionIntroImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() bluetoothOn,
    required TResult Function() bluetoothOff,
    required TResult Function() checkingRecentDevice,
    required TResult Function() fetchingSavedDevices,
    required TResult Function() bluetoothSearching,
    required TResult Function() connecting,
    required TResult Function(BluetoothDevice device) reconnecting,
    required TResult Function(List<DeviceModel> devices, int version)
        savedDevices,
    required TResult Function(List<BluetoothDevice?>? devices)
        bluetoothAvailableTypeDevices,
    required TResult Function(BluetoothDevice device) connected,
    required TResult Function(BluetoothDevice device) disconnected,
    required TResult Function(BluetoothFailure failure) bluetoothError,
    required TResult Function(BluetoothDevice device) connectionIntro,
  }) {
    return connectionIntro(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? bluetoothOn,
    TResult? Function()? bluetoothOff,
    TResult? Function()? checkingRecentDevice,
    TResult? Function()? fetchingSavedDevices,
    TResult? Function()? bluetoothSearching,
    TResult? Function()? connecting,
    TResult? Function(BluetoothDevice device)? reconnecting,
    TResult? Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult? Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult? Function(BluetoothDevice device)? connected,
    TResult? Function(BluetoothDevice device)? disconnected,
    TResult? Function(BluetoothFailure failure)? bluetoothError,
    TResult? Function(BluetoothDevice device)? connectionIntro,
  }) {
    return connectionIntro?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? bluetoothOn,
    TResult Function()? bluetoothOff,
    TResult Function()? checkingRecentDevice,
    TResult Function()? fetchingSavedDevices,
    TResult Function()? bluetoothSearching,
    TResult Function()? connecting,
    TResult Function(BluetoothDevice device)? reconnecting,
    TResult Function(List<DeviceModel> devices, int version)? savedDevices,
    TResult Function(List<BluetoothDevice?>? devices)?
        bluetoothAvailableTypeDevices,
    TResult Function(BluetoothDevice device)? connected,
    TResult Function(BluetoothDevice device)? disconnected,
    TResult Function(BluetoothFailure failure)? bluetoothError,
    TResult Function(BluetoothDevice device)? connectionIntro,
    required TResult orElse(),
  }) {
    if (connectionIntro != null) {
      return connectionIntro(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(BluetoothOn value) bluetoothOn,
    required TResult Function(BluetoothOff value) bluetoothOff,
    required TResult Function(CheckingRecentDevice value) checkingRecentDevice,
    required TResult Function(FetchingSavedDevices value) fetchingSavedDevices,
    required TResult Function(BluetoothSearching value) bluetoothSearching,
    required TResult Function(Connecting value) connecting,
    required TResult Function(Reconnecting value) reconnecting,
    required TResult Function(SavedDevices value) savedDevices,
    required TResult Function(BluetoothAvailableTypeDevices value)
        bluetoothAvailableTypeDevices,
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(BluetoothError value) bluetoothError,
    required TResult Function(ConnectionIntro value) connectionIntro,
  }) {
    return connectionIntro(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(BluetoothOn value)? bluetoothOn,
    TResult? Function(BluetoothOff value)? bluetoothOff,
    TResult? Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult? Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult? Function(BluetoothSearching value)? bluetoothSearching,
    TResult? Function(Connecting value)? connecting,
    TResult? Function(Reconnecting value)? reconnecting,
    TResult? Function(SavedDevices value)? savedDevices,
    TResult? Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(BluetoothError value)? bluetoothError,
    TResult? Function(ConnectionIntro value)? connectionIntro,
  }) {
    return connectionIntro?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(BluetoothOn value)? bluetoothOn,
    TResult Function(BluetoothOff value)? bluetoothOff,
    TResult Function(CheckingRecentDevice value)? checkingRecentDevice,
    TResult Function(FetchingSavedDevices value)? fetchingSavedDevices,
    TResult Function(BluetoothSearching value)? bluetoothSearching,
    TResult Function(Connecting value)? connecting,
    TResult Function(Reconnecting value)? reconnecting,
    TResult Function(SavedDevices value)? savedDevices,
    TResult Function(BluetoothAvailableTypeDevices value)?
        bluetoothAvailableTypeDevices,
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(BluetoothError value)? bluetoothError,
    TResult Function(ConnectionIntro value)? connectionIntro,
    required TResult orElse(),
  }) {
    if (connectionIntro != null) {
      return connectionIntro(this);
    }
    return orElse();
  }
}

abstract class ConnectionIntro implements BluetoothServiceState {
  const factory ConnectionIntro(final BluetoothDevice device) =
      _$ConnectionIntroImpl;

  BluetoothDevice get device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConnectionIntroImplCopyWith<_$ConnectionIntroImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
