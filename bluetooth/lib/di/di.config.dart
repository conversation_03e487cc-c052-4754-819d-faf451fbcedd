// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:bluetooth/application/bluetooth_service_bloc/bluetooth_service_bloc.dart'
    as _i527;
import 'package:bluetooth/domain/facade/bluetooth_facade.dart' as _i909;
import 'package:bluetooth/repository/bluetooth_service.dart' as _i966;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.lazySingleton<_i909.IBluetoothFacade>(
        () => _i966.BluetoothServiceRepository());
    gh.factory<_i527.BluetoothServiceBloc>(
        () => _i527.BluetoothServiceBloc(gh<_i909.IBluetoothFacade>()));
    return this;
  }
}
