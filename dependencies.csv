Dependency,Version
_fe_analyzer_shared,61.0.0
_flutterfire_internals,1.3.16
analyzer,5.13.0
another_xlider,3.0.2
archive,3.4.10
args,2.4.2
async,2.11.0
authentication,0.0.1
bloc,8.1.4
bloc_test,9.1.7
boolean_selector,2.1.1
build,2.4.1
build_config,1.1.1
build_daemon,4.0.1
build_resolvers,2.4.2
build_runner,2.4.8
build_runner_core,7.3.0
built_collection,5.1.1
built_value,8.9.1
change_app_package_name,1.1.0
characters,1.3.0
checked_yaml,2.0.3
cli_util,0.4.1
clock,1.1.1
code_builder,4.10.0
collection,1.18.0
convert,3.1.1
coverage,1.7.2
crypto,3.0.3
cupertino_icons,1.0.6
dart_style,2.3.2
design_system,1.0.0
diff_match_patch,0.4.1
equatable,2.0.5
fake_async,1.3.1
ffi,2.1.0
file,6.1.4
firebase_auth,4.16.0
firebase_auth_platform_interface,7.0.9
firebase_auth_web,5.8.13
firebase_core,2.24.2
firebase_core_platform_interface,5.0.0
firebase_core_web,2.10.0
fixnum,1.1.0
fl_chart,0.64.0
flutter,0.0.0
flutter_bloc,8.1.5
flutter_driver,0.0.0
flutter_launcher_icons,0.11.0
flutter_lints,2.0.3
flutter_screenutil,5.9.0
flutter_svg,1.0.3
flutter_test,0.0.0
flutter_web_plugins,0.0.0
fpdart,1.1.0
freezed,2.4.7
freezed_annotation,2.4.1
frontend_server_client,3.2.0
fuchsia_remote_debug_protocol,0.0.0
get,5.0.0-release-candidate-5
get_it,7.6.7
glob,2.1.2
google_fonts,6.1.0
google_identity_services_web,0.2.2
google_sign_in,5.0.0-nullsafety
google_sign_in_android,6.1.22
google_sign_in_ios,5.6.3
google_sign_in_platform_interface,2.4.5
google_sign_in_web,0.12.2+1
graphs,2.3.1
http,1.1.0
http_multi_server,3.2.1
http_parser,4.0.2
image,3.1.3
injectable,2.3.5
injectable_generator,2.4.2
injector,3.0.0
integration_test,0.0.0
intl,0.17.0
io,1.0.4
js,0.6.7
json_annotation,4.8.1
junitreport,2.0.2
leak_tracker,10.0.0
leak_tracker_flutter_testing,2.0.1
leak_tracker_testing,2.0.1
lints,2.1.1
logging,1.2.0
matcher,0.12.16+1
material_color_utilities,0.8.0
meta,1.11.0
mime,1.0.4
mocktail,1.0.3
nested,1.0.0
node_preamble,2.0.2
package_config,2.1.0
path,1.9.0
path_drawing,1.0.1
path_parsing,1.0.1
path_provider,2.1.2
path_provider_android,2.2.2
path_provider_foundation,2.3.2
path_provider_linux,2.2.1
path_provider_platform_interface,2.1.2
path_provider_windows,2.2.1
patrol,3.6.1
patrol_finders,2.0.2
petitparser,5.4.0
platform,3.1.4
plugin_platform_interface,2.1.8
pointycastle,3.7.4
pool,1.5.1
process,4.2.4
provider,6.1.2
pub_semver,2.1.4
pubspec_parse,1.2.3
recase,4.1.0
shelf,1.4.1
shelf_packages_handler,3.0.2
shelf_static,1.1.2
shelf_web_socket,1.0.4
sign_in_with_apple,5.0.0
sign_in_with_apple_platform_interface,1.0.0
sign_in_with_apple_web,1.0.1
sky_engine,0.0.99
source_gen,1.5.0
source_map_stack_trace,2.1.1
source_maps,0.10.12
source_span,1.10.0
sprintf,7.0.0
stack_trace,1.11.1
stream_channel,2.1.2
stream_transform,2.1.0
string_scanner,1.2.0
sync_http,0.3.1
term_glyph,1.2.1
test,1.24.9
test_api,0.6.1
test_core,0.5.9
testreport,2.0.1
timing,1.0.1
typed_data,1.3.2
uuid,4.2.2
vector_math,2.1.4
vm_service,13.0.0
watcher,1.1.0
web_socket_channel,2.4.0
webdriver,3.0.3
webkit_inspection_protocol,1.2.1
win32,5.1.1
xdg_directories,1.0.4
xml,5.4.1
yaml,3.1.2
ansi_styles,0.3.2+1
charcode,1.3.1
cli_launcher,0.3.1
conventional_commit,0.6.0+1
json_serializable,6.7.1
melos,3.4.0
mustache_template,2.0.0
prompts,2.0.0
pub_updater,0.3.1
pubspec,2.3.0
quiver,3.2.1
source_helper,1.3.4
uri,1.0.0
yaml_edit,2.2.0
