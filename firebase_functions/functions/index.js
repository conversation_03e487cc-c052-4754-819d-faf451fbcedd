const functions = require("firebase-functions");
const admin = require("firebase-admin");
const sgMail = require("@sendgrid/mail");

admin.initializeApp();
const db = admin.firestore();
const SENDGRID_API_KEY = functions.config().sendgrid.api_key;

sgMail.setApiKey(SENDGRID_API_KEY);


exports.sendWelcomeEmailAndCreateUserDoc = functions.auth.user().onCreate(async (user) => {
  const userEmail = user.email;
  const userName = user.displayName || "User";
  const userDoc = {
    uid: user.uid,
    userEmail: userEmail,
    userName: userName,
    photoURL: user.photoURL,

    providerData: user.providerData,
    signUpTimeStamp: user.metadata.creationTime, // Current time as sign-up timestamp
    loginTimeStamp: user.metadata.lastSignInTime,

    // Initialize as null
  };

  // Attempt to create the user document in Firestore
  try {
    const usersCollection = db.collection("users");
    await usersCollection.doc(userDoc.uid).set(userDoc, {merge: true});
  } catch (error) {
    console.error("Error during Firestore operation:", error);
    console.error("Failed Firestore path:", `users/${user.uid}`);
    console.error("Error details:", error);
  }

  // Configure the email message
  const msg = {
    to: userEmail,
    from: {"email": "<EMAIL>", "name": "Juno Technologies"},
    templateId: "d-4cc107f257aa4434aced7519b56eeef5",
    dynamicTemplateData: {
      username: userName,
    },
  };

  // Attempt to send the welcome email
  try {
    await sgMail.send(msg);
    console.log("Welcome email sent successfully to:", userEmail);
  } catch (error) {
    console.error("Failed to send welcome email:", error);
    console.error("Response body:", error.response.body);
  }
});
