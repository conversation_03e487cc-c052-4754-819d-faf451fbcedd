{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "name": "Dart",
            "type": "dart",
            "request": "launch",
            "program": "bin/main.dart"
        },
        {
            "name": "JunoPlus",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "account_management",
            "cwd": "account_management",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "account_management (profile mode)",
            "cwd": "account_management",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "account_management (release mode)",
            "cwd": "account_management",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "app",
            "cwd": "app",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "app (profile mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "app (release mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "authentication",
            "cwd": "authentication",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "authentication (profile mode)",
            "cwd": "authentication",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "authentication (release mode)",
            "cwd": "authentication",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "bluetooth",
            "cwd": "bluetooth",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "bluetooth (profile mode)",
            "cwd": "bluetooth",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "bluetooth (release mode)",
            "cwd": "bluetooth",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "design_system",
            "cwd": "design_system",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "design_system (profile mode)",
            "cwd": "design_system",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "design_system (release mode)",
            "cwd": "design_system",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "help_center",
            "cwd": "help_center",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "help_center (profile mode)",
            "cwd": "help_center",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "help_center (release mode)",
            "cwd": "help_center",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "notifications",
            "cwd": "notifications",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "notifications (profile mode)",
            "cwd": "notifications",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "notifications (release mode)",
            "cwd": "notifications",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "remote",
            "cwd": "remote",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "remote (profile mode)",
            "cwd": "remote",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "remote (release mode)",
            "cwd": "remote",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}