// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'remote_failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RemoteFailure {
  String get failureMessage => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) heatLevelFailure,
    required TResult Function(String failureMessage) tensLevelFailure,
    required TResult Function(String failureMessage) tensModeFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? heatLevelFailure,
    TResult? Function(String failureMessage)? tensLevelFailure,
    TResult? Function(String failureMessage)? tensModeFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? heatLevelFailure,
    TResult Function(String failureMessage)? tensLevelFailure,
    TResult Function(String failureMessage)? tensModeFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(HeatLevelFailure value) heatLevelFailure,
    required TResult Function(TensLevelFailure value) tensLevelFailure,
    required TResult Function(TensModeFailure value) tensModeFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(HeatLevelFailure value)? heatLevelFailure,
    TResult? Function(TensLevelFailure value)? tensLevelFailure,
    TResult? Function(TensModeFailure value)? tensModeFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(HeatLevelFailure value)? heatLevelFailure,
    TResult Function(TensLevelFailure value)? tensLevelFailure,
    TResult Function(TensModeFailure value)? tensModeFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RemoteFailureCopyWith<RemoteFailure> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RemoteFailureCopyWith<$Res> {
  factory $RemoteFailureCopyWith(
          RemoteFailure value, $Res Function(RemoteFailure) then) =
      _$RemoteFailureCopyWithImpl<$Res, RemoteFailure>;
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$RemoteFailureCopyWithImpl<$Res, $Val extends RemoteFailure>
    implements $RemoteFailureCopyWith<$Res> {
  _$RemoteFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_value.copyWith(
      failureMessage: null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ServerErrorImplCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory _$$ServerErrorImplCopyWith(
          _$ServerErrorImpl value, $Res Function(_$ServerErrorImpl) then) =
      __$$ServerErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$ServerErrorImplCopyWithImpl<$Res>
    extends _$RemoteFailureCopyWithImpl<$Res, _$ServerErrorImpl>
    implements _$$ServerErrorImplCopyWith<$Res> {
  __$$ServerErrorImplCopyWithImpl(
      _$ServerErrorImpl _value, $Res Function(_$ServerErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$ServerErrorImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ServerErrorImpl implements ServerError {
  const _$ServerErrorImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'RemoteFailure.serverError(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServerErrorImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ServerErrorImplCopyWith<_$ServerErrorImpl> get copyWith =>
      __$$ServerErrorImplCopyWithImpl<_$ServerErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) heatLevelFailure,
    required TResult Function(String failureMessage) tensLevelFailure,
    required TResult Function(String failureMessage) tensModeFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return serverError(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? heatLevelFailure,
    TResult? Function(String failureMessage)? tensLevelFailure,
    TResult? Function(String failureMessage)? tensModeFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return serverError?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? heatLevelFailure,
    TResult Function(String failureMessage)? tensLevelFailure,
    TResult Function(String failureMessage)? tensModeFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(HeatLevelFailure value) heatLevelFailure,
    required TResult Function(TensLevelFailure value) tensLevelFailure,
    required TResult Function(TensModeFailure value) tensModeFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(HeatLevelFailure value)? heatLevelFailure,
    TResult? Function(TensLevelFailure value)? tensLevelFailure,
    TResult? Function(TensModeFailure value)? tensModeFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(HeatLevelFailure value)? heatLevelFailure,
    TResult Function(TensLevelFailure value)? tensLevelFailure,
    TResult Function(TensModeFailure value)? tensModeFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class ServerError implements RemoteFailure {
  const factory ServerError(final String failureMessage) = _$ServerErrorImpl;

  @override
  String get failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ServerErrorImplCopyWith<_$ServerErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NoInternetConnectionImplCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory _$$NoInternetConnectionImplCopyWith(_$NoInternetConnectionImpl value,
          $Res Function(_$NoInternetConnectionImpl) then) =
      __$$NoInternetConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$NoInternetConnectionImplCopyWithImpl<$Res>
    extends _$RemoteFailureCopyWithImpl<$Res, _$NoInternetConnectionImpl>
    implements _$$NoInternetConnectionImplCopyWith<$Res> {
  __$$NoInternetConnectionImplCopyWithImpl(_$NoInternetConnectionImpl _value,
      $Res Function(_$NoInternetConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$NoInternetConnectionImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NoInternetConnectionImpl implements NoInternetConnection {
  const _$NoInternetConnectionImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'RemoteFailure.noInternetConnection(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NoInternetConnectionImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NoInternetConnectionImplCopyWith<_$NoInternetConnectionImpl>
      get copyWith =>
          __$$NoInternetConnectionImplCopyWithImpl<_$NoInternetConnectionImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) heatLevelFailure,
    required TResult Function(String failureMessage) tensLevelFailure,
    required TResult Function(String failureMessage) tensModeFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return noInternetConnection(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? heatLevelFailure,
    TResult? Function(String failureMessage)? tensLevelFailure,
    TResult? Function(String failureMessage)? tensModeFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return noInternetConnection?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? heatLevelFailure,
    TResult Function(String failureMessage)? tensLevelFailure,
    TResult Function(String failureMessage)? tensModeFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (noInternetConnection != null) {
      return noInternetConnection(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(HeatLevelFailure value) heatLevelFailure,
    required TResult Function(TensLevelFailure value) tensLevelFailure,
    required TResult Function(TensModeFailure value) tensModeFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return noInternetConnection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(HeatLevelFailure value)? heatLevelFailure,
    TResult? Function(TensLevelFailure value)? tensLevelFailure,
    TResult? Function(TensModeFailure value)? tensModeFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return noInternetConnection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(HeatLevelFailure value)? heatLevelFailure,
    TResult Function(TensLevelFailure value)? tensLevelFailure,
    TResult Function(TensModeFailure value)? tensModeFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (noInternetConnection != null) {
      return noInternetConnection(this);
    }
    return orElse();
  }
}

abstract class NoInternetConnection implements RemoteFailure {
  const factory NoInternetConnection(final String failureMessage) =
      _$NoInternetConnectionImpl;

  @override
  String get failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NoInternetConnectionImplCopyWith<_$NoInternetConnectionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HeatLevelFailureImplCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory _$$HeatLevelFailureImplCopyWith(_$HeatLevelFailureImpl value,
          $Res Function(_$HeatLevelFailureImpl) then) =
      __$$HeatLevelFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$HeatLevelFailureImplCopyWithImpl<$Res>
    extends _$RemoteFailureCopyWithImpl<$Res, _$HeatLevelFailureImpl>
    implements _$$HeatLevelFailureImplCopyWith<$Res> {
  __$$HeatLevelFailureImplCopyWithImpl(_$HeatLevelFailureImpl _value,
      $Res Function(_$HeatLevelFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$HeatLevelFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$HeatLevelFailureImpl implements HeatLevelFailure {
  const _$HeatLevelFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'RemoteFailure.heatLevelFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HeatLevelFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HeatLevelFailureImplCopyWith<_$HeatLevelFailureImpl> get copyWith =>
      __$$HeatLevelFailureImplCopyWithImpl<_$HeatLevelFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) heatLevelFailure,
    required TResult Function(String failureMessage) tensLevelFailure,
    required TResult Function(String failureMessage) tensModeFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return heatLevelFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? heatLevelFailure,
    TResult? Function(String failureMessage)? tensLevelFailure,
    TResult? Function(String failureMessage)? tensModeFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return heatLevelFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? heatLevelFailure,
    TResult Function(String failureMessage)? tensLevelFailure,
    TResult Function(String failureMessage)? tensModeFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (heatLevelFailure != null) {
      return heatLevelFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(HeatLevelFailure value) heatLevelFailure,
    required TResult Function(TensLevelFailure value) tensLevelFailure,
    required TResult Function(TensModeFailure value) tensModeFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return heatLevelFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(HeatLevelFailure value)? heatLevelFailure,
    TResult? Function(TensLevelFailure value)? tensLevelFailure,
    TResult? Function(TensModeFailure value)? tensModeFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return heatLevelFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(HeatLevelFailure value)? heatLevelFailure,
    TResult Function(TensLevelFailure value)? tensLevelFailure,
    TResult Function(TensModeFailure value)? tensModeFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (heatLevelFailure != null) {
      return heatLevelFailure(this);
    }
    return orElse();
  }
}

abstract class HeatLevelFailure implements RemoteFailure {
  const factory HeatLevelFailure(final String failureMessage) =
      _$HeatLevelFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HeatLevelFailureImplCopyWith<_$HeatLevelFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TensLevelFailureImplCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory _$$TensLevelFailureImplCopyWith(_$TensLevelFailureImpl value,
          $Res Function(_$TensLevelFailureImpl) then) =
      __$$TensLevelFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$TensLevelFailureImplCopyWithImpl<$Res>
    extends _$RemoteFailureCopyWithImpl<$Res, _$TensLevelFailureImpl>
    implements _$$TensLevelFailureImplCopyWith<$Res> {
  __$$TensLevelFailureImplCopyWithImpl(_$TensLevelFailureImpl _value,
      $Res Function(_$TensLevelFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$TensLevelFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$TensLevelFailureImpl implements TensLevelFailure {
  const _$TensLevelFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'RemoteFailure.tensLevelFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TensLevelFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TensLevelFailureImplCopyWith<_$TensLevelFailureImpl> get copyWith =>
      __$$TensLevelFailureImplCopyWithImpl<_$TensLevelFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) heatLevelFailure,
    required TResult Function(String failureMessage) tensLevelFailure,
    required TResult Function(String failureMessage) tensModeFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return tensLevelFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? heatLevelFailure,
    TResult? Function(String failureMessage)? tensLevelFailure,
    TResult? Function(String failureMessage)? tensModeFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return tensLevelFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? heatLevelFailure,
    TResult Function(String failureMessage)? tensLevelFailure,
    TResult Function(String failureMessage)? tensModeFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (tensLevelFailure != null) {
      return tensLevelFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(HeatLevelFailure value) heatLevelFailure,
    required TResult Function(TensLevelFailure value) tensLevelFailure,
    required TResult Function(TensModeFailure value) tensModeFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return tensLevelFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(HeatLevelFailure value)? heatLevelFailure,
    TResult? Function(TensLevelFailure value)? tensLevelFailure,
    TResult? Function(TensModeFailure value)? tensModeFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return tensLevelFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(HeatLevelFailure value)? heatLevelFailure,
    TResult Function(TensLevelFailure value)? tensLevelFailure,
    TResult Function(TensModeFailure value)? tensModeFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (tensLevelFailure != null) {
      return tensLevelFailure(this);
    }
    return orElse();
  }
}

abstract class TensLevelFailure implements RemoteFailure {
  const factory TensLevelFailure(final String failureMessage) =
      _$TensLevelFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TensLevelFailureImplCopyWith<_$TensLevelFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TensModeFailureImplCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory _$$TensModeFailureImplCopyWith(_$TensModeFailureImpl value,
          $Res Function(_$TensModeFailureImpl) then) =
      __$$TensModeFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$TensModeFailureImplCopyWithImpl<$Res>
    extends _$RemoteFailureCopyWithImpl<$Res, _$TensModeFailureImpl>
    implements _$$TensModeFailureImplCopyWith<$Res> {
  __$$TensModeFailureImplCopyWithImpl(
      _$TensModeFailureImpl _value, $Res Function(_$TensModeFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$TensModeFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$TensModeFailureImpl implements TensModeFailure {
  const _$TensModeFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'RemoteFailure.tensModeFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TensModeFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TensModeFailureImplCopyWith<_$TensModeFailureImpl> get copyWith =>
      __$$TensModeFailureImplCopyWithImpl<_$TensModeFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) heatLevelFailure,
    required TResult Function(String failureMessage) tensLevelFailure,
    required TResult Function(String failureMessage) tensModeFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return tensModeFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? heatLevelFailure,
    TResult? Function(String failureMessage)? tensLevelFailure,
    TResult? Function(String failureMessage)? tensModeFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return tensModeFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? heatLevelFailure,
    TResult Function(String failureMessage)? tensLevelFailure,
    TResult Function(String failureMessage)? tensModeFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (tensModeFailure != null) {
      return tensModeFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(HeatLevelFailure value) heatLevelFailure,
    required TResult Function(TensLevelFailure value) tensLevelFailure,
    required TResult Function(TensModeFailure value) tensModeFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return tensModeFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(HeatLevelFailure value)? heatLevelFailure,
    TResult? Function(TensLevelFailure value)? tensLevelFailure,
    TResult? Function(TensModeFailure value)? tensModeFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return tensModeFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(HeatLevelFailure value)? heatLevelFailure,
    TResult Function(TensLevelFailure value)? tensLevelFailure,
    TResult Function(TensModeFailure value)? tensModeFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (tensModeFailure != null) {
      return tensModeFailure(this);
    }
    return orElse();
  }
}

abstract class TensModeFailure implements RemoteFailure {
  const factory TensModeFailure(final String failureMessage) =
      _$TensModeFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TensModeFailureImplCopyWith<_$TensModeFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnexpectedFailureImplCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory _$$UnexpectedFailureImplCopyWith(_$UnexpectedFailureImpl value,
          $Res Function(_$UnexpectedFailureImpl) then) =
      __$$UnexpectedFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class __$$UnexpectedFailureImplCopyWithImpl<$Res>
    extends _$RemoteFailureCopyWithImpl<$Res, _$UnexpectedFailureImpl>
    implements _$$UnexpectedFailureImplCopyWith<$Res> {
  __$$UnexpectedFailureImplCopyWithImpl(_$UnexpectedFailureImpl _value,
      $Res Function(_$UnexpectedFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_$UnexpectedFailureImpl(
      null == failureMessage
          ? _value.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UnexpectedFailureImpl implements UnexpectedFailure {
  const _$UnexpectedFailureImpl(this.failureMessage);

  @override
  final String failureMessage;

  @override
  String toString() {
    return 'RemoteFailure.unexpectedFailure(failureMessage: $failureMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnexpectedFailureImpl &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnexpectedFailureImplCopyWith<_$UnexpectedFailureImpl> get copyWith =>
      __$$UnexpectedFailureImplCopyWithImpl<_$UnexpectedFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) heatLevelFailure,
    required TResult Function(String failureMessage) tensLevelFailure,
    required TResult Function(String failureMessage) tensModeFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    return unexpectedFailure(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? heatLevelFailure,
    TResult? Function(String failureMessage)? tensLevelFailure,
    TResult? Function(String failureMessage)? tensModeFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    return unexpectedFailure?.call(failureMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? heatLevelFailure,
    TResult Function(String failureMessage)? tensLevelFailure,
    TResult Function(String failureMessage)? tensModeFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (unexpectedFailure != null) {
      return unexpectedFailure(failureMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(HeatLevelFailure value) heatLevelFailure,
    required TResult Function(TensLevelFailure value) tensLevelFailure,
    required TResult Function(TensModeFailure value) tensModeFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    return unexpectedFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(HeatLevelFailure value)? heatLevelFailure,
    TResult? Function(TensLevelFailure value)? tensLevelFailure,
    TResult? Function(TensModeFailure value)? tensModeFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    return unexpectedFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(HeatLevelFailure value)? heatLevelFailure,
    TResult Function(TensLevelFailure value)? tensLevelFailure,
    TResult Function(TensModeFailure value)? tensModeFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    if (unexpectedFailure != null) {
      return unexpectedFailure(this);
    }
    return orElse();
  }
}

abstract class UnexpectedFailure implements RemoteFailure {
  const factory UnexpectedFailure(final String failureMessage) =
      _$UnexpectedFailureImpl;

  @override
  String get failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnexpectedFailureImplCopyWith<_$UnexpectedFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
