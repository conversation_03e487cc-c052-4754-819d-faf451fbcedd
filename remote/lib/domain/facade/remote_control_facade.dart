import 'package:analytics/domain/models/therapy_session_event_model.dart';
import 'package:fpdart/fpdart.dart';

import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/heat_level.dart';

import '../model/device_model.dart';
import '../model/tens_level_model.dart';

abstract class RemoteControlFacade {
  Future<Either<RemoteFailure, Unit>> increaseHeat();
  Future<Either<RemoteFailure, Unit>> decreaseHeat();
  Stream<Either<RemoteFailure, HeatLevelModel>> watchHeatLevel();
  Future<Either<RemoteFailure, Unit>> increaseTens();
  Future<Either<RemoteFailure, Unit>> decreaseTens();
  Stream<Either<RemoteFailure, TensLevelModel>> watchTensLevel();
  Future<Either<RemoteFailure, Unit>> changeMode(int mode);
  Stream<Either<RemoteFailure, DeviceModel>> getDeviceStatus();
  Future<Either<RemoteFailure, DeviceModel>> getDeviceInformation();

  // New functions for device control
  Future<Either<RemoteFailure, Unit>> powerOffDevice();
  Future<Either<RemoteFailure, Unit>> toggleTherapy();
  Stream<Either<RemoteFailure, bool>> watchTherapyState();

  // Setting change events stream for analytics
  Stream<TherapySessionEventModel> get settingChangeStream;
}
