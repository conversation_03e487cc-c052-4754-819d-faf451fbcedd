// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceModel _$DeviceModelFromJson(Map<String, dynamic> json) => DeviceModel(
      deviceInfo:
          DeviceInfoModel.fromJson(json['deviceInfo'] as Map<String, dynamic>),
      isDeviceConnected: json['isDeviceConnected'] as bool?,
      isDeviceOn: json['isDeviceOn'] as bool? ?? false,
      isDeviceReady: json['isDeviceReady'] as bool?,
      isDeviceError: json['isDeviceError'] as bool?,
      isDeviceBusy: json['isDeviceBusy'] as bool?,
      isDeviceOff: json['isDeviceOff'] as bool?,
      isDeviceDisconnected: json['isDeviceDisconnected'] as bool?,
      heatLevel: json['heatLevel'] == null
          ? null
          : HeatLevelModel.fromJson(json['heatLevel'] as Map<String, dynamic>),
      tensLevel: json['tensLevel'] == null
          ? null
          : TensLevelModel.fromJson(json['tensLevel'] as Map<String, dynamic>),
      recentCommands: (json['recentCommands'] as List<dynamic>?)
          ?.map((e) => Commands.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastConnected: json['lastConnected'] == null
          ? null
          : DateTime.parse(json['lastConnected'] as String),
    );

Map<String, dynamic> _$DeviceModelToJson(DeviceModel instance) =>
    <String, dynamic>{
      'deviceInfo': instance.deviceInfo.toJson(),
      'isDeviceConnected': instance.isDeviceConnected,
      'isDeviceOn': instance.isDeviceOn,
      'isDeviceReady': instance.isDeviceReady,
      'isDeviceError': instance.isDeviceError,
      'isDeviceBusy': instance.isDeviceBusy,
      'isDeviceOff': instance.isDeviceOff,
      'isDeviceDisconnected': instance.isDeviceDisconnected,
      'lastConnected': instance.lastConnected?.toIso8601String(),
      'heatLevel': instance.heatLevel?.toJson(),
      'tensLevel': instance.tensLevel?.toJson(),
      'recentCommands':
          instance.recentCommands?.map((e) => e.toJson()).toList(),
    };
