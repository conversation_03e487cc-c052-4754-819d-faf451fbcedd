import 'package:freezed_annotation/freezed_annotation.dart';

part 'tens_level_model.g.dart';

@JsonSerializable(explicitToJson: true)
class TensLevelModel {
  final int? selectedTensLevel;
  final int actualTensLevel;
  final int? applyingLevel;
  final int mode;
  TensLevelModel({
    this.selectedTensLevel,
    required this.actualTensLevel,
    this.applyingLevel,
    required this.mode,
  });

  TensLevelModel copyWith({
    int? selectedTensLevel,
    int? actualTensLevel,
    int? applyingLevel,
    int? mode,
  }) {
    return TensLevelModel(
      selectedTensLevel: selectedTensLevel ?? this.selectedTensLevel,
      actualTensLevel: actualTensLevel ?? this.actualTensLevel,
      applyingLevel: applyingLevel,
      mode: mode ?? this.mode,
    );
  }

  // Method to convert a TensLevelModel instance into a JSON map.
  factory TensLevelModel.fromJson(Map<String, dynamic> json) =>
      _$TensLevelModelFromJson(json);
  // Method to convert a TensLevelModel instance into a JSON map.
  Map<String, dynamic> toJson() => _$TensLevelModelToJson(this);
}
