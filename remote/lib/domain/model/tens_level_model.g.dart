// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tens_level_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TensLevelModel _$TensLevelModelFromJson(Map<String, dynamic> json) =>
    TensLevelModel(
      selectedTensLevel: (json['selectedTensLevel'] as num?)?.toInt(),
      actualTensLevel: (json['actualTensLevel'] as num).toInt(),
      applyingLevel: (json['applyingLevel'] as num?)?.toInt(),
      mode: (json['mode'] as num).toInt(),
    );

Map<String, dynamic> _$TensLevelModelToJson(TensLevelModel instance) =>
    <String, dynamic>{
      'selectedTensLevel': instance.selectedTensLevel,
      'actualTensLevel': instance.actualTensLevel,
      'applyingLevel': instance.applyingLevel,
      'mode': instance.mode,
    };
