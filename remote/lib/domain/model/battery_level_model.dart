import 'package:freezed_annotation/freezed_annotation.dart';
part 'battery_level_model.g.dart';



@JsonSerializable(explicitToJson: true)
class BatteryLevelModel {
  final int batteryLevel;

  BatteryLevelModel({required this.batteryLevel});

copyWith({
    int? batteryLevel,
  }) {
    return BatteryLevelModel(
      batteryLevel: batteryLevel ?? this.batteryLevel,
    );
  }
  // Method to convert a BatteryLevelModel instance into a JSON map.
  factory BatteryLevelModel.fromJson(Map<String, dynamic> json) =>
      _$BatteryLevelModelFromJson(json);
  // Method to convert a BatteryLevelModel instance into a JSON map.
  Map<String, dynamic> toJson() => _$BatteryLevelModelToJson(this);
}