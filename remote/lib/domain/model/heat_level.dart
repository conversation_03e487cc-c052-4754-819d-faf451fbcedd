// heat_level.dart (Value Object)
import 'package:freezed_annotation/freezed_annotation.dart';

part 'heat_level.g.dart';

@JsonSerializable(explicitToJson: true)

class HeatLevelModel {
  final int selectedHeatLevel;
  final int actualHeatLevel;
  HeatLevelModel({
    required this.selectedHeatLevel,
    required this.actualHeatLevel,
  });

  HeatLevelModel copyWith({
    int? selectedHeatLevel,
    int? actualHeatLevel,
  }) {
    return HeatLevelModel(
      selectedHeatLevel: selectedHeatLevel ?? this.selectedHeatLevel,
      actualHeatLevel: actualHeatLevel ?? this.actualHeatLevel,
    );
  }

  factory HeatLevelModel.fromJson(Map<String, dynamic> json) =>
      _$HeatLevelModelFromJson(json);
  // Method to convert a HeatLevelModel instance into a JSON map.
  Map<String, dynamic> toJson() => _$HeatLevelModelToJson(this);
}