import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';
part 'commands_model.g.dart';
@JsonSerializable(explicitToJson: true)
class Commands {
  int value;
  String commandType;
  DateTime commandTime;

  Commands({
    required this.value,
    required this.commandType,
    required this.commandTime
  });

  Commands copyWith({
    int? value,
    String? commandType,
    DateTime? commandTime
  }) {
    return Commands(
        value: value ?? this.value,
        commandType: commandType ?? this.commandType,
        commandTime: commandTime ?? this.commandTime
    );
  }
  // Method to convert a Commands instance into a JSON map.
  factory Commands.fromJson(Map<String, dynamic> json) =>
      _$CommandsFromJson(json);
  // Method to convert a Commands instance into a JSON map.
  Map<String, dynamic> toJson() => _$CommandsToJson(this);
}