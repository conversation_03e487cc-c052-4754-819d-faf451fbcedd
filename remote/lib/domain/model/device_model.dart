import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:remote/domain/model/battery_level_model.dart';
import 'package:remote/domain/model/heat_level.dart';
import 'package:remote/domain/model/tens_level_model.dart';

import 'commands_model.dart';
import 'device_info_model.dart';
part 'device_model.g.dart';

@JsonSerializable(explicitToJson: true)
class DeviceModel {
  DeviceInfoModel deviceInfo;
  bool? isDeviceConnected;
  bool isDeviceOn;
  bool? isDeviceReady;
  bool? isDeviceError;
  bool? isDeviceBusy;
  bool? isDeviceOff;
  bool? isDeviceDisconnected;
  DateTime? lastConnected;
  HeatLevelModel? heatLevel;
  TensLevelModel? tensLevel;
  List<Commands>? recentCommands;


  DeviceModel({

    required this.deviceInfo,
    this.isDeviceConnected,
    this.isDeviceOn = false,
    this.isDeviceReady,
    this.isDeviceError,
    this.isDeviceBusy,
    this.isDeviceOff,
    this.isDeviceDisconnected,
    this.heatLevel,
    this.tensLevel,
    this.recentCommands,
    this.lastConnected

  });

  DeviceModel copyWith({
    DeviceInfoModel? deviceInfo,
    bool? isDeviceConnected,
    bool? isDeviceOn,
    bool? isDeviceReady,
    bool? isDeviceError,
    bool? isDeviceBusy,
    bool? isDeviceOff,
    bool? isDeviceDisconnected,
    HeatLevelModel? heatLevel,
    TensLevelModel? tensLevel,
    List<Commands>? recentCommands,
    DateTime? lastConnected
  }) {
    return DeviceModel(
        deviceInfo: deviceInfo ?? this.deviceInfo,
        isDeviceConnected: isDeviceConnected ?? this.isDeviceConnected,
        isDeviceOn: isDeviceOn ?? this.isDeviceOn,
        isDeviceReady: isDeviceReady ?? this.isDeviceReady,
        isDeviceError: isDeviceError ?? this.isDeviceError,
        isDeviceBusy: isDeviceBusy ?? this.isDeviceBusy,
        isDeviceOff: isDeviceOff ?? this.isDeviceOff,
        isDeviceDisconnected: isDeviceDisconnected ?? this.isDeviceDisconnected,
        heatLevel: heatLevel ?? this.heatLevel,
        tensLevel: tensLevel ?? this.tensLevel,
        recentCommands: recentCommands ?? this.recentCommands,
        lastConnected: lastConnected ?? this.lastConnected
    );
  }

  Map<String, dynamic> toJson() => _$DeviceModelToJson(this);
  factory DeviceModel.fromJson(Map<String, dynamic> json) =>
      _$DeviceModelFromJson(json);

}


