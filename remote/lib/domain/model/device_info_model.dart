import 'package:freezed_annotation/freezed_annotation.dart';
  import 'battery_level_model.dart';

  part 'device_info_model.g.dart';

  @JsonSerializable(explicitToJson: true)
  class DeviceInfoModel {
    String? deviceName;
    String? deviceAddress;
    String? deviceType;
    String? deviceId;
    BatteryLevelModel? batteryLevel;

    DeviceInfoModel({
       this.deviceName,
       this.deviceAddress,
       this.deviceType,
       this.deviceId,
       required this.batteryLevel,
    });

    DeviceInfoModel copyWith({
      String? deviceName,
      String? deviceAddress,
      String? deviceType,
      String? deviceId,
      required BatteryLevelModel batteryLevel,
    }) {
      return DeviceInfoModel(
        deviceName: deviceName ?? this.deviceName,
        deviceAddress: deviceAddress ?? this.deviceAddress,
        deviceType: deviceType ?? this.deviceType,
        deviceId: deviceId ?? this.deviceId,
        batteryLevel: batteryLevel ,
      );
    }

    Map<String, dynamic> toJson() => _$DeviceInfoModelToJson(this);
    factory DeviceInfoModel.fromJson(Map<String, dynamic> json) =>
        _$DeviceInfoModelFromJson(json);
  }