import 'dart:async';
import 'dart:math';

import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:fpdart/src/unit.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/repository/managers/device_data_fetcher.dart';
import 'package:remote/repository/managers/device_state_manager.dart';
import 'package:remote/repository/models/queued_command.dart';
import 'package:remote/repository/utils/bluetooth_data_utils.dart';

/// Manages command processing queues for the remote repository
///
/// Responsible for sending commands to the device and tracking their completion
class CommandProcessor {
  final IBluetoothFacade _bluetoothFacade;
  final List<QueuedCommand> _heatCommandQueue = [];
  final List<QueuedCommand> _tensCommandQueue = [];
  final DeviceStateManager stateManager;
  final DeviceDataFetcher _deviceDataFetcher;
  
  static const int _firstTimeoutSeconds = 40;
  static const int _extendedTimeoutSeconds = 20;

  bool _isProcessingHeatCommands = false;
  bool _isProcessingTensCommands = false;
  int? _expectedHeatLevel;
  int? _expectedTensLevel;
  Timer? _tensSafetyTimeoutTimer;


  // get for heat and tens command queue length
  int get heatCommandQueueLength => _heatCommandQueue.length;
  int get tensCommandQueueLength => _tensCommandQueue.length;


  CommandProcessor(this._bluetoothFacade, this.stateManager)
      : _deviceDataFetcher = DeviceDataFetcher(_bluetoothFacade);

  /// Helper method to check if current intensity is between last and expected levels
  bool _isIntensityProgressing(int currentIntensity, int lastLevel, int expectedLevel) {
    final min = lastLevel < expectedLevel ? lastLevel : expectedLevel;  
    final max = lastLevel < expectedLevel ? expectedLevel : lastLevel;
    return currentIntensity > min && currentIntensity < max;
  }

  /// Handle TENS command timeout with intensity checking
  Future<void> _handleTensTimeout(QueuedCommand queuedCommand, bool isExtendedTimeout) async {
    if (_tensCommandQueue.isEmpty || 
        _tensCommandQueue.first != queuedCommand ||
        queuedCommand.completer.isCompleted) {
      return;
    }

    try {
      final currentIntensity = await _deviceDataFetcher.fetchTensIntensity();
      final lastLevel = microAmpLevels[stateManager.currentTensLevel] ?? 0;
      final expectedLevel = microAmpLevels[_expectedTensLevel ?? 0] ?? 0;

      debugPrint('Timeout check - Current: ${currentIntensity}mA, Last: ${lastLevel}mA, Expected: ${expectedLevel}mA');

      if (!isExtendedTimeout && _isIntensityProgressing(currentIntensity, lastLevel, expectedLevel)) {
        debugPrint('mA intensity is progressing, extending timeout by $_extendedTimeoutSeconds seconds');
        _tensSafetyTimeoutTimer?.cancel();
        _tensSafetyTimeoutTimer = Timer(
          Duration(seconds: _extendedTimeoutSeconds),
          () => _handleTensTimeout(queuedCommand, true)
        );
        return;
      }

      debugPrint('mA intensity not at target level, handling as failure');
      clearTensCommandQueue();
      stateManager.adjustTensLevelOnFailure();
      
      if (!queuedCommand.completer.isCompleted) {
        queuedCommand.completer.complete(Left(RemoteFailure.serverError(
          'Failed to reach target intensity after ${_firstTimeoutSeconds + _extendedTimeoutSeconds} seconds'
        )));
      }
    } catch (e) {
      debugPrint('Error during intensity check: $e');
      _tensCommandQueue.removeAt(0);
      _expectedTensLevel = null;
      
      if (!queuedCommand.completer.isCompleted) {
        queuedCommand.completer.complete(Right(unit));
      }

      if (_tensCommandQueue.isNotEmpty) {
        Future.delayed(Duration(milliseconds: 500), _processNextTensCommand);
      } else {
        _isProcessingTensCommands = false;
      }
    }
  }

  /// Check if current mA is between expected levels
  bool _isIntensityBetweenLevels(int currentIntensity, int lastLevel, int expectedLevel) {
    final minValue = min(lastLevel, expectedLevel);
    final maxValue = max(lastLevel, expectedLevel);
    return currentIntensity > minValue && currentIntensity < maxValue;
  }

  /// Adds a heat command to the queue and begins processing
  Future<Either<RemoteFailure, Unit>> enqueueHeatCommand(
      Commands command) async {
    final queuedCommand =
        QueuedCommand(command, Completer<Either<RemoteFailure, Unit>>());
    _heatCommandQueue.add(queuedCommand);
    _processHeatCommandQueue();
    return queuedCommand.completer.future;
  }

  /// Adds a TENS command to the queue and begins processing
  Future<Either<RemoteFailure, Unit>> enqueueTensCommand(
      Commands command) async {
    final queuedCommand =
        QueuedCommand(command, Completer<Either<RemoteFailure, Unit>>());
    _tensCommandQueue.add(queuedCommand);
    _processTensCommandQueue();
    return queuedCommand.completer.future;
  }

  /// Returns true if there are any TENS commands in the queue
  bool hasTensCommandsOfType(String commandType) {
    return _tensCommandQueue
        .any((cmd) => cmd.command.commandType == commandType);
  }

  /// Clears all TENS commands from the queue
  void clearTensCommandQueue() {

    _isProcessingTensCommands=false;
    _tensCommandQueue.clear();
  }

  /// Process the heat command queue
  Future<void> _processHeatCommandQueue() async {
    if (_isProcessingHeatCommands || _heatCommandQueue.isEmpty) return;

    _isProcessingHeatCommands = true;
    _processNextHeatCommand();
  }


  Future<void> _processNextHeatCommand() async {
    if (_heatCommandQueue.isEmpty) {
      _isProcessingHeatCommands = false;
      return;
    }

    final queuedCommand = _heatCommandQueue.first;
    try {
      int expectedLevel = queuedCommand.command.value;
      _expectedHeatLevel = expectedLevel;

      // Send command to device
      final result = await _bluetoothFacade.sendCommand(queuedCommand.command);

      // Handle command result
      result.mapBoth(
        onLeft: (bluetoothFailure) {
          _heatCommandQueue.removeAt(0);
          if (!queuedCommand.completer.isCompleted) {
            // adjust selected level depending on the command type
            if (queuedCommand.command.commandType == 'increaseHeat') {
              stateManager.adjustHeatlevel(1);
            } else if (queuedCommand.command.commandType == 'decreaseHeat') {
             stateManager.adjustHeatlevel(-1);
            } else {
              _expectedHeatLevel = null;
            }

            queuedCommand.completer.complete(Left(RemoteFailure.serverError(bluetoothFailure.toString())));
          }

          // Move to the next command
          if (_heatCommandQueue.isNotEmpty) {
            _processNextHeatCommand();
          } else {
            _isProcessingHeatCommands = false;
          }
        },
        onRight: (_) {
          // Start a safety timeout for when indications might not arrive
          Timer(const Duration(seconds: 5), () {
            // Only proceed if this command is still at the front of the queue
            if (_heatCommandQueue.isNotEmpty &&
                _heatCommandQueue.first == queuedCommand &&
                !queuedCommand.completer.isCompleted) {
              debugPrint("Heat indication safety timeout triggered, proceeding with command");

              // Remove from queue and complete
              _heatCommandQueue.removeAt(0);
              queuedCommand.completer.complete(Right(unit));

              // Reset expected level
              _expectedHeatLevel = null;

              // Continue processing
              if (_heatCommandQueue.isNotEmpty) {
                _processNextHeatCommand();
              } else {
                _isProcessingHeatCommands = false;
              }
            }
          });
        },
      );
    } catch (e) {
      debugPrint('Error processing HEAT command: $e');

      // Safety check before removing from queue
      if (_heatCommandQueue.isNotEmpty &&
          _heatCommandQueue.first == queuedCommand) {
        _heatCommandQueue.removeAt(0);

        // Only complete if not already completed
        if (!queuedCommand.completer.isCompleted) {
          queuedCommand.completer
              .complete(Left(RemoteFailure.serverError(e.toString())));
        }

        // Reset expected level
        _expectedHeatLevel = null;
      }

      // Continue processing the queue if there are more commands
      if (_heatCommandQueue.isNotEmpty) {
        _processNextHeatCommand();
      } else {
        _isProcessingHeatCommands = false;
      }
    }
  }

  /// Process the TENS command queue
  Future<void> _processTensCommandQueue() async {
    if (!_isProcessingTensCommands && _tensCommandQueue.isNotEmpty) {
      _isProcessingTensCommands = true;
      _processNextTensCommand();
    }
  }

  Future<void> _processNextTensCommand() async {
    if (_tensCommandQueue.isEmpty) {
      _isProcessingTensCommands = false;
      return;
    }

    final queuedCommand = _tensCommandQueue.first;
    int expectedLevel = queuedCommand.command.value;
    _expectedTensLevel = expectedLevel;
    try {      final commandValue = queuedCommand.command.commandType == 'increaseTens'
        ? 1
        : queuedCommand.command.commandType == 'decreaseTens'
        ? -1
        : queuedCommand.command.value;

    final result = await _bluetoothFacade
        .sendCommand(queuedCommand.command.copyWith(value: commandValue));// Handle command result
      result.mapBoth(
        onLeft: (bluetoothFailure) {
          _tensCommandQueue.removeAt(0);
          if (!queuedCommand.completer.isCompleted) {
            if (queuedCommand.command.commandType == 'increaseTens') {
              stateManager.adjustTensLevel(1);
            } else if (queuedCommand.command.commandType == 'decreaseTens') {
              stateManager.adjustTensLevel(-1);
            } else {
              _expectedTensLevel = null;
            }
            queuedCommand.completer.complete(Left(RemoteFailure.serverError(bluetoothFailure.toString())));
          }
          if (_tensCommandQueue.isNotEmpty) {
            _processNextTensCommand();
          } else {
            _isProcessingTensCommands = false;
          }
        },
        onRight: (_) {
          _tensSafetyTimeoutTimer?.cancel();
          _tensSafetyTimeoutTimer = Timer(
            Duration(seconds: _firstTimeoutSeconds),
            () => _handleTensTimeout(queuedCommand, false)
          );
        },
      );
    } catch (e) {
      debugPrint('Error processing TENS command: $e');

      // Safety check before removing from queue
      if (_tensCommandQueue.isNotEmpty &&
          _tensCommandQueue.first == queuedCommand) {
        _tensCommandQueue.removeAt(0);

        // Only complete if not already completed
        if (!queuedCommand.completer.isCompleted) {
          queuedCommand.completer
              .complete(Left(RemoteFailure.serverError(e.toString())));
        }

        // Reset expected level
        _expectedTensLevel = null;
      }

      // Continue processing the queue if there are more commands
      if (_tensCommandQueue.isNotEmpty) {
        _processNextTensCommand();
      } else {
        _isProcessingTensCommands = false;
      }
    }
  }

  /// Process TENS indication and update command queue as needed
  void handleTensIndication(int indicationValue, int currentValue) {
    // Add debug log to track where zero might be coming from

    // Check if we have an expected level and a command in the queue

    if (_expectedTensLevel != null && _tensCommandQueue.isNotEmpty) {
      // Get the current command
      final currentCommand = _tensCommandQueue.first;

      // Check if we should consider this level change as a response to our command
      bool isResponseToCommand = false;

     //  For "increase" commands, any increase is considered success
      if (currentCommand.command.commandType == 'increaseTens' &&
          indicationValue > currentValue  ) {
        // debugPrint(
        //     "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^Indication matches increase response for command: ${currentCommand.command}, "
        //         "Expected Level: $_expectedTensLevel, Actual Level: $indicationValue,currentValue:$currentValue");
        // isResponseToCommand = true;
      }
      // For "decrease" commands, any decrease is considered success
        if (currentCommand.command.commandType == 'decreaseTens' &&
          indicationValue < currentValue) {
        // debugPrint(
        //     "Indication matches decrease response for command: ${currentCommand.command}, "
        //         "Expected Level: $_expectedTensLevel, Actual Level: $indicationValue");
        isResponseToCommand = true;
      }
      // For specific level commands, check if we're at or close to the expected level
      else if (indicationValue == _expectedTensLevel) {
        // debugPrint(
        //     "Indication matches equal response for command: ${currentCommand.command}, "
        //         "Expected Level: $_expectedTensLevel, Actual Level: $indicationValue");
        isResponseToCommand = true;
      }

      if (isResponseToCommand && !currentCommand.completer.isCompleted) {
        // debugPrint(
        //     "Indication matches expected response for command: ${currentCommand.command.commandType}");

        // Remove the command from the queue and complete it
        _tensCommandQueue.removeAt(0);
        currentCommand.completer.complete(Right(unit));

        // Cancel any safety timeout
        _tensSafetyTimeoutTimer?.cancel();
        _tensSafetyTimeoutTimer = null;

        // Reset expected level
        _expectedTensLevel = null;

        // Process the next command if any
        if (_tensCommandQueue.isNotEmpty) {
          _processNextTensCommand();
        } else {
          _isProcessingTensCommands = false;
        }
      }
    }
  }

  /// Process heat indication and update command queue as needed
  void handleHeatIndication(int heatLevel, int currentLevel) {
    if (_expectedHeatLevel != null && _heatCommandQueue.isNotEmpty) {
      // Get the current command
      final currentCommand = _heatCommandQueue.first;

      // Check if we should consider this level change as a response to our command
      bool isResponseToCommand = false;

      // For "increase" commands, any increase is considered success
      if (currentCommand.command.commandType == 'increaseHeat' &&
          heatLevel > currentLevel) {
        isResponseToCommand = true;
      }
      // For "decrease" commands, any decrease is considered success
      else if (currentCommand.command.commandType == 'decreaseHeat' &&
          heatLevel < currentLevel) {
        isResponseToCommand = true;
      }
      // For specific level commands, check if we're at the expected level
      else if (heatLevel == _expectedHeatLevel) {
        isResponseToCommand = true;
      }

      if (isResponseToCommand && !currentCommand.completer.isCompleted) {
        // Remove the command from the queue and complete it
        _heatCommandQueue.removeAt(0);
        currentCommand.completer.complete(Right(unit));

        // Reset expected level
        _expectedHeatLevel = null;

        // Process the next command if any
        if (_heatCommandQueue.isNotEmpty) {
          _processNextHeatCommand();
        } else {
          _isProcessingHeatCommands = false;
        }
      }
    }
  }

  /// Get the expected heat level from the current command
  int? get expectedHeatLevel => _expectedHeatLevel;

  /// Get the expected TENS level from the current command
  int? get expectedTensLevel => _expectedTensLevel;

  /// Reset expected TENS level
  void resetExpectedTensLevel() {
    _expectedTensLevel = null;
  }

  /// Reset expected heat level
  void resetExpectedHeatLevel() {
    _expectedHeatLevel = null;
  }

  /// Clear all command queues
  void clearAllCommandQueues() {
    debugPrint('Clearing all command queues on reconnection');

    // Complete any pending commands with failure
    for (final command in _heatCommandQueue) {
      if (!command.completer.isCompleted) {
        command.completer.complete(Left(RemoteFailure.serverError(
            'Device reconnected - clearing command queue')));
      }
    }

    for (final command in _tensCommandQueue) {
      if (!command.completer.isCompleted) {
        command.completer.complete(Left(RemoteFailure.serverError(
            'Device reconnected - clearing command queue')));
      }
    }

    // Clear queues
    _heatCommandQueue.clear();
    _tensCommandQueue.clear();

    // Reset processing flags
    _isProcessingHeatCommands = false;
    _isProcessingTensCommands = false;

    // Cancel any timers
    _tensSafetyTimeoutTimer?.cancel();
    _tensSafetyTimeoutTimer = null;

    // Reset expected levels
    _expectedHeatLevel = null;
    _expectedTensLevel = null;
  }

  /// Cleanup resources
  void dispose() {
    _tensSafetyTimeoutTimer?.cancel();

    // Complete any pending commands with failure
    for (final command in _heatCommandQueue) {
      if (!command.completer.isCompleted) {
        command.completer.complete(Left(RemoteFailure.serverError(
            'Repository disposed before command could complete')));
      }
    }
    _heatCommandQueue.clear();

    for (final command in _tensCommandQueue) {
      if (!command.completer.isCompleted) {
        command.completer.complete(Left(RemoteFailure.serverError(
            'Repository disposed before command could complete')));
      }
    }
    _tensCommandQueue.clear();
  }
}
