import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/repository/utils/bluetooth_data_utils.dart';

import '../../domain/failures/remote_failures.dart';
import '../../domain/model/tens_level_model.dart';

/// <PERSON>les fetching data from the Bluetooth device
///
/// Responsible for reading characteristics and parsing the results
class DeviceDataFetcher {
  final IBluetoothFacade _bluetoothFacade;

  // Error handling
  static const int _maxRetries = 3;
  final Map<String, int> _retryCount = {};

  DeviceDataFetcher(this._bluetoothFacade);

  /// Fetch the current heat level from the device
  Future<int> fetchHeatLevel() async {
    try {
      final currHeatCommand = Commands(
          value: 0, commandType: 'currentTemp', commandTime: DateTime.now());
      final targetHeatLevel = Commands(
          value: 0, commandType: 'targetTemp', commandTime: DateTime.now());

      final result = await _retryOperation(() async {
        final results = await Future.wait([
          _bluetoothFacade.readDeviceCharacteristics(currHeatCommand),
          _bluetoothFacade.readDeviceCharacteristics(targetHeatLevel),
        ]);
        return results;
      }, 'heat_level');

      // Then map the Celsius value to a heat level
    print('Fetched heat level results: ${result}');
      for (final res in result) {
        final failure = res.getLeft().getOrNull();
        if (failure != null) {
          return 0; // Return the first failure found
        }
      }
      final heatLevel = result[0].getOrElse((_) => [-1]).first;
      final targetHeat = result[1].getOrElse((_) => [-1]).first;

      // debugPrint(
      //     "Mapped to heat level====================================: $heatLevel,targetLevel: $targetHeat");
      return heatLevel;
    } catch (e) {
      debugPrint('Error fetching heat level: $e');
      rethrow;
    }
  }

  /// Fetch the current TENS level and mode from the device
  Future<Either<RemoteFailure, TensLevelModel>> fetchTensLevelAndMode() async {
    try {
      final currTensCommand = Commands(
          value: 0, commandType: 'activeTens', commandTime: DateTime.now());
      final tensModeCommand = Commands(
          value: 0, commandType: 'setTensMode', commandTime: DateTime.now());
      final targetTensCommand = Commands(
          value: 0, commandType: 'targetTens', commandTime: DateTime.now());

      final result = await _retryOperation(() async {
        final results = await Future.wait([
          _bluetoothFacade.readDeviceCharacteristics(currTensCommand),
          _bluetoothFacade.readDeviceCharacteristics(tensModeCommand),
          _bluetoothFacade.readDeviceCharacteristics(targetTensCommand),
        ]);

        return results;
      }, 'tens_level');

      // Check for any failure
      // Check each result for failure using getLeft().toNullable()
      for (final res in result) {
        final failure = res.getLeft().getOrNull();
        if (failure != null) {
          return Left(RemoteFailure.tensLevelFailure(
              '------')); // Return the first failure found
        }
      }
      final actualTensLevel = result[0].getOrElse((_) => [-1]).first;
      final mode = result[1].getOrElse((_) => [-1]).first;
      final targetTensLevel = result[2].getOrElse((_) => [-1]).first;

      // print(
      //     "=====================================================Actual:$actualTensLevel,Mode:$mode,targetTensLevel$targetTensLevel");

      // Return success with TensLevelModel
      return Right(TensLevelModel(
          actualTensLevel: actualTensLevel,
          mode: mode,
          selectedTensLevel: targetTensLevel));
    } catch (e) {
      debugPrint('Error fetching TENS level: $e');
      return Left(RemoteFailure.tensLevelFailure(
          'Failed to fetch TENS level and mode'));
    }
  }

  /// Generic retry mechanism for device operations
  Future<T> _retryOperation<T>(
      Future<T> Function() operation, String operationKey) async {
    int retries = _retryCount[operationKey] ?? 0;

    try {
      final result = await operation();
      _resetRetryCount(operationKey);
      return result;
    } catch (e) {
      retries++;
      _retryCount[operationKey] = retries;

      if (retries < _maxRetries) {
        // Wait with exponential backoff
        await Future.delayed(Duration(milliseconds: 200 * retries));
        return _retryOperation(operation, operationKey);
      } else {
        // Max retries reached, reset counter and rethrow
        _resetRetryCount(operationKey);
        throw e;
      }
    }
  }

  /// Reset the retry counter for a specific operation
  void _resetRetryCount(String operationKey) {
    _retryCount[operationKey] = 0;
  }

  // fetch the tens mA Intensity from the device
  Future<int> fetchTensIntensity() async {
    try {
      final tensIntensityCommand = Commands(
          value: 0,
          commandType: 'measuredIntensity',
          commandTime: DateTime.now());

      final result = await _retryOperation(() async {
        return await _bluetoothFacade
            .readDeviceCharacteristics(tensIntensityCommand);
      }, 'measured_intensity');

      // Check for failure
      final failure = result.getLeft().getOrNull();
      if (failure != null) {
        return 0; // Return 0 on failure
      }

      final intensity = listToUint32(result.getOrElse((_) => []));
      // debugPrint("Fetched TENS intensity: $intensity");
      return intensity;
    } catch (e) {
      debugPrint('Error fetching TENS intensity: $e');
      rethrow;
    }
  }
}
