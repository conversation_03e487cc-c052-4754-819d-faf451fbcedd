import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:remote/domain/model/device_info_model.dart';
import 'package:remote/repository/utils/update_states.dart';
import 'package:rxdart/rxdart.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/battery_level_model.dart';
import 'package:remote/domain/model/device_model.dart';
import 'package:remote/domain/model/heat_level.dart';
import 'package:remote/domain/model/tens_level_model.dart';

/// Manages the state of the connected device
///
/// Responsible for tracking and updating device state including
/// heat levels, TENS levels, and connection status
class DeviceStateManager {
  // BehaviorSubjects for reactive state updates
  final _heatLevelSubject =
      BehaviorSubject<Either<RemoteFailure, HeatLevelModel>>();
  final _tensLevelSubject =
      BehaviorSubject<Either<RemoteFailure, TensLevelModel>>();
  final _deviceModelSubject =
      BehaviorSubject<Either<RemoteFailure, DeviceModel>>();

  // Initial device model state
  DeviceModel _deviceModel = DeviceModel(
    deviceInfo: DeviceInfoModel(
      deviceName: 'Unknown Device',
      deviceAddress: '00:00:00:00:00:00',
      deviceType: 'Unknown Type',
      deviceId: '00000000-0000-0000-0000-000000000000',
      batteryLevel: BatteryLevelModel(batteryLevel: 100),
    ),
    isDeviceOn: true,

    heatLevel: HeatLevelModel(selectedHeatLevel: 0, actualHeatLevel: 0),
    tensLevel:
        TensLevelModel(selectedTensLevel: 0, actualTensLevel: 0, mode: 0),
    recentCommands: [],
  );

  // Flags to track first read of each feature
  bool _isFirstHeatRead = false;
  bool _isFirstTensRead = false;

  // Flag to prevent incorrect state resets during connection transitions
  bool _isProcessingDisconnect = false;

  // Queue to process state updates sequentially
  final _updateQueue = <Function>[];
  bool _isProcessingQueue = false;

  // Track last logged zero times to prevent log flooding
  DateTime? _lastLoggedHeatZeroTime;
  DateTime? _lastLoggedTensZeroTime;

  DeviceStateManager();

  /// Get the current device model
  DeviceModel get deviceModel => _deviceModel;

  /// Get the stream of heat level updates
  Stream<Either<RemoteFailure, HeatLevelModel>> get heatLevelStream =>
      _heatLevelSubject.stream;

  /// Get the stream of TENS level updates
  Stream<Either<RemoteFailure, TensLevelModel>> get tensLevelStream =>
      _tensLevelSubject.stream;

  /// Get the stream of device model updates
  Stream<Either<RemoteFailure, DeviceModel>> get deviceModelStream =>
      _deviceModelSubject.stream;

  /// Process the queue of pending updates
  void _processUpdateQueue() async {
    if (_isProcessingQueue || _updateQueue.isEmpty) return;

    _isProcessingQueue = true;

    try {
      while (_updateQueue.isNotEmpty) {
        final update = _updateQueue.removeAt(0);
        update();

        // Add a small delay between updates to allow UI to render
        await Future.delayed(Duration(milliseconds: 5));
      }
    } finally {
      _isProcessingQueue = false;
    }
  }

  /// Add an update to the queue
  void _enqueueUpdate(Function update) {
    _updateQueue.add(update);
    _processUpdateQueue();
  }

  /// Resets the first read flags to force new readings to be treated as initial
  void resetFirstReadFlags() {
    _isFirstHeatRead = false;
    _isFirstTensRead = false;
    debugPrint('First read flags reset for reconnection');
  }

  /// Update the heat level in the device state
  void updateHeatLevel({
    int? selectedLevel,
    int? actualLevel,
    bool emitUpdate = true,
    bool isInitialRead = false,
  }) {
    // Skip updates during device disconnection processing
    if (_isProcessingDisconnect) {
      debugPrint('Skipping heat level update during disconnect processing');
      return;
    }

    // Skip updates with abnormal values (like 242 which appears in logs)
    if (actualLevel != null && actualLevel > 100) {
      debugPrint('Ignoring abnormal heat level value: $actualLevel');
      return;
    }

    _enqueueUpdate(() {
      // Handle first read case or explicit initial read
      if ((!_isFirstHeatRead || isInitialRead) && actualLevel != null) {
        _deviceModel = _deviceModel.copyWith(
          heatLevel: _deviceModel.heatLevel?.copyWith(
            selectedHeatLevel:
                actualLevel, // Set selected to match actual on first read
            actualHeatLevel: actualLevel,
          ),
        );
        _isFirstHeatRead = true;
        debugPrint(
            'Initial heat level sync - setting selected and actual to $actualLevel');
      } else {
        // Regular update
        _deviceModel = _deviceModel.copyWith(
          heatLevel: _deviceModel.heatLevel?.copyWith(
            selectedHeatLevel:
                selectedLevel ?? _deviceModel.heatLevel?.selectedHeatLevel,
            actualHeatLevel:
                actualLevel ?? _deviceModel.heatLevel?.actualHeatLevel,
          ),
        );
      }

      // Emit update if requested
      if (emitUpdate) {
        _heatLevelSubject.add(Right(_deviceModel.heatLevel!));
      }

      //   debugPrint(
      //       // 'Updated heat level: selected=${_deviceModel.heatLevel?.selectedHeatLevel}, actual=${_deviceModel.heatLevel?.actualHeatLevel}');
    });
  }

  /// Update the TENS level in the device state
  void updateTensLevel({
    int? selectedLevel,
    int? actualLevel,
    int? mode,
    bool emitUpdate = true,
    String? calledFrom,
    required UpdateState updateState,
  }) {
    // Debug log to track updateTensLevel calls with key parameters
    // debugPrint(
    //     '[TENS_DEBUG] updateTensLevel called with: selectedLevel=$selectedLevel, actualLevel=$actualLevel, mode=$mode, readState=${updateState}, calledFrom=$calledFrom');
    // debugPrint(
    //     '[TENS_DEBUG] Current device state: selectedLevel=${_deviceModel
    //         .tensLevel?.selectedTensLevel}, actualLevel=${_deviceModel.tensLevel
    //         ?.actualTensLevel}, mode=${_deviceModel.tensLevel?.mode}');

    // Skip updates during device disconnection processing unless forced
    if (_isProcessingDisconnect) {
      debugPrint('Skipping TENS level update during disconnect processing');
      return;
    }

    // NEW: Track potential selected level resets to zero
    if (selectedLevel == 0 &&
        _deviceModel.tensLevel?.selectedTensLevel != null &&
        _deviceModel.tensLevel!.selectedTensLevel! > 0) {
      debugPrint(
          '[TENS_DEBUG] WARNING: Selected TENS level is being reset from ${_deviceModel.tensLevel?.selectedTensLevel} to 0');
      // Print a stack trace to see where this is being called from
      // try {
      //   throw Exception('Stack trace for selected TENS level reset');
      // } catch (e, stackTrace) {
      //   debugPrint(
      //       '[TENS_DEBUG] ${stackTrace.toString().split('\n').take(10).join(
      //           '\n')}');
      // }
    }

    // Skip duplicate updates
    if (actualLevel != null &&
        actualLevel == _deviceModel.tensLevel?.actualTensLevel &&
        selectedLevel == null &&
        mode != null &&
        mode == _deviceModel.tensLevel?.mode &&
        updateState != UpdateState.initial) {
      print(
          '-------------------------------Ignoring duplicate TENS level update: actualLevel=$actualLevel, selectedLevel=$selectedLevel, mode=$mode');
      return;
    }

    _enqueueUpdate(() {
      // Handle first read case or explicit initial read
      if (updateState == UpdateState.initial && actualLevel != null) {
        _deviceModel = _deviceModel.copyWith(
          tensLevel: _deviceModel.tensLevel?.copyWith(
            selectedTensLevel:
                actualLevel, // Set selected to match actual on first read
            actualTensLevel: actualLevel,
            mode: mode ?? _deviceModel.tensLevel?.mode,
          ),
        );
        _isFirstTensRead = true;
        // debugPrint(
        //     'Initial TENS level sync - setting selected and actual to $actualLevel, mode: $mode');
      } else if (updateState == UpdateState.indicate && actualLevel != null) {
        // Regular update
        _deviceModel = _deviceModel.copyWith(
          tensLevel: _deviceModel.tensLevel?.copyWith(
            selectedTensLevel: selectedLevel,
            actualTensLevel: actualLevel,
          ),
        );
      } else if (updateState == UpdateState.read) {
        // Regular update
        _deviceModel = _deviceModel.copyWith(
          tensLevel: _deviceModel.tensLevel?.copyWith(
            actualTensLevel:
                actualLevel ?? _deviceModel.tensLevel?.actualTensLevel,
            mode: mode ?? _deviceModel.tensLevel?.mode,
          ),
        );
      } else if (updateState == UpdateState.write) {
        // Regular update
        _deviceModel = _deviceModel.copyWith(
          tensLevel: _deviceModel.tensLevel?.copyWith(
            selectedTensLevel:
                selectedLevel ?? _deviceModel.tensLevel?.selectedTensLevel,
          ),
        );
      } else if (updateState == UpdateState.modeUpdate) {
        // Regular update
        _deviceModel = _deviceModel.copyWith(
          tensLevel: _deviceModel.tensLevel?.copyWith(
            selectedTensLevel: 0,
            actualTensLevel: 0,
            mode: mode ?? _deviceModel.tensLevel?.mode,
          ),
        );
      } else {
        // Regular update
        _deviceModel = _deviceModel.copyWith(
          tensLevel: _deviceModel.tensLevel?.copyWith(
            selectedTensLevel:
                selectedLevel ?? _deviceModel.tensLevel?.selectedTensLevel,
            actualTensLevel:
                actualLevel ?? _deviceModel.tensLevel?.actualTensLevel,
            mode: mode ?? _deviceModel.tensLevel?.mode,
          ),
        );
      }

      // Emit update if requested
      if (emitUpdate) {
        _tensLevelSubject.add(Right(_deviceModel.tensLevel!));
      }

      // debugPrint(
      //     'Updated TENS level: selected=${_deviceModel.tensLevel
      //         ?.selectedTensLevel}, actual=${_deviceModel.tensLevel
      //         ?.actualTensLevel}, mode=${_deviceModel.tensLevel?.mode}');
    });
  }

  /// Update the device model with new information
  void updateDeviceModel(DeviceModel deviceModel) {
    // Skip updates during device disconnection processing
    if (_isProcessingDisconnect) {
      debugPrint('Skipping device model update during disconnect processing');
      return;
    }

    _enqueueUpdate(() {
      _deviceModel = _deviceModel.copyWith(
        deviceInfo: deviceModel.deviceInfo,
        isDeviceOn: deviceModel.isDeviceOn,

      );
      _deviceModelSubject.add(Right(_deviceModel));
    });
  }

  /// Signal a device connection
  void signalDeviceConnected() {
    _isProcessingDisconnect = false;

    // Reset first read flags to sync levels on connection
    _isFirstHeatRead = false;
    _isFirstTensRead = false;
  }

  /// Signal a device disconnection
  void signalDeviceDisconnected() {
    _isProcessingDisconnect = true;
  }

  /// Signal a device error
  void signalError(String errorMessage,
      {bool isHeatError = false, bool isTensError = false}) {
    _enqueueUpdate(() {
      if (isHeatError) {
        _heatLevelSubject.add(Left(RemoteFailure.serverError(errorMessage)));
      }
      if (isTensError) {
        _tensLevelSubject.add(Left(RemoteFailure.serverError(errorMessage)));
      }

      _deviceModelSubject.add(Left(RemoteFailure.serverError(errorMessage)));
    });
  }

  /// Get the current mode value
  int get currentMode => _deviceModel.tensLevel?.mode ?? 0;

  /// Get the current TENS level
  int get currentTensLevel => _deviceModel.tensLevel?.actualTensLevel ?? 0;

  /// Get the current heat level
  int get currentHeatLevel => _deviceModel.heatLevel?.actualHeatLevel ?? 0;

  /// Clean up resources
  void dispose() {
    _heatLevelSubject.close();
    _tensLevelSubject.close();
    _deviceModelSubject.close();
    _updateQueue.clear();
  }

  void changeMode(int mode) {
    // Update the mode in the device model
    _deviceModel = _deviceModel.copyWith(
      tensLevel: _deviceModel.tensLevel?.copyWith(mode: mode),
    );
    print(_deviceModel.toJson());
    // Emit the updated device model
    _tensLevelSubject.add(Right(_deviceModel.tensLevel!));
    _deviceModelSubject.add(Right(_deviceModel));

    // Log the mode change
    // debugPrint('Mode changed to $mode');
  }

  void adjustHeatlevel(int adjustment) {
    // Update the heat level in the device model
    _deviceModel = _deviceModel.copyWith(
      heatLevel: _deviceModel.heatLevel?.copyWith(
        selectedHeatLevel:
            _deviceModel.tensLevel!.selectedTensLevel! + adjustment,
      ),
    );

    // Emit the updated device model
    _heatLevelSubject.add(Right(_deviceModel.heatLevel!));
    _deviceModelSubject.add(Right(_deviceModel));

    // Log the heat level change
    // debugPrint('Heat level changed to $adjustment');
  }

  void adjustTensLevel(int adjustment) {
    // Update the TENS level in the device model

    _deviceModel = _deviceModel.copyWith(
      tensLevel: _deviceModel.tensLevel?.copyWith(
        selectedTensLevel:
            _deviceModel.tensLevel!.selectedTensLevel! + adjustment,
      ),
    );
    _tensLevelSubject.add(Right(_deviceModel.tensLevel!));
    // Emit the updated device model
    _deviceModelSubject.add(Right(_deviceModel));

    // Log the TENS level change
    // debugPrint('TENS level changed to $adjustment');
  }

  void adjustTensLevelOnFailure() {
    // Update the TENS level in the device model

    _deviceModel = _deviceModel.copyWith(
      tensLevel: _deviceModel.tensLevel?.copyWith(
          selectedTensLevel: _deviceModel.tensLevel!.actualTensLevel),
    );
    _tensLevelSubject.add(Right(_deviceModel.tensLevel!));
    // Emit the updated device model
    _deviceModelSubject.add(Right(_deviceModel));
  }
}
