import 'dart:async';
import 'package:fpdart/fpdart.dart';
import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
import 'package:flutter/foundation.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/repository/managers/command_processor.dart';
import 'package:remote/repository/utils/bluetooth_data_utils.dart';

/// Manages Bluetooth indications for device communication
///
/// Handles the setup and processing of indications for both TENS and heat features
class IndicationManager {
  final IBluetoothFacade _bluetoothFacade;
  final CommandProcessor _commandProcessor;

  // Indication subscription management
  StreamSubscription<Either<BluetoothFailure, List<int>>>?
      _tensIndicationSubscription;
  StreamSubscription<Either<BluetoothFailure, List<int>>>?
      _heatIndicationSubscription;
  StreamSubscription<Either<BluetoothFailure, List<int>>>?
      _tensModeIndicationSubscription;

  // Track if listeners are active
  bool _isTensIndicationListenerActive = false;
  /// Track if heat indication listener is active
  bool _isHeatIndicationListenerActive = false;
  
  bool _isModeIndicationListenerActive = false;

  // Track if setup is in progress to prevent duplicate setups
  bool _isTensSetupInProgress = false;
  bool _isHeatSetupInProgress = false;

  // Debounce mechanism for TENS indications
  int _lastTensIndicationValue = -1;
  DateTime _lastTensIndicationTime =
      DateTime.now().subtract(Duration(minutes: 1));
  static const _debounceTimeMs = 500; // milliseconds

  IndicationManager(this._bluetoothFacade, this._commandProcessor);

  /// Initialize the TENS indication listener
  Future<void> initTensIndicationListener({
    required Function(int) onIndicationReceived,
    required int currentLevel,
  }) async {
    // If already active or setup in progress, don't duplicate
    if (_isTensIndicationListenerActive || _isTensSetupInProgress) {
      debugPrint('TENS indication listener already active or being set up');
      return;
    }

    _isTensSetupInProgress = true;

    try {
      // Cancel any existing subscription first
      cancelTensIndicationListener();

      debugPrint('Initializing TENS indication listener');
      Commands command = Commands(
          value: 0, commandType: 'activeTens', commandTime: DateTime.now());


      // Start listening to indications
      _tensIndicationSubscription =
          _bluetoothFacade.listenToIndication(command).listen((indication) {
        final indicationData = indication.getOrElse((_) => []);
        if (indicationData.isNotEmpty) {
          // Log the raw data for debugging
          // debugPrint('Received TENS indication value: $indicationData');

          // Process the indication with our improved utility
          final indicationValue = indicationData.first;
          // debugPrint('Received TENS indication: $indicationValue');

          // Enhanced validation for suspicious zero values
          bool isValidIndication = true;

          // Apply debouncing to avoid rapid identical updates
          final now = DateTime.now();
          final timeSinceLastIndication =
              now.difference(_lastTensIndicationTime).inMilliseconds;

          if (isValidIndication &&
              (indicationValue != _lastTensIndicationValue ||
                  timeSinceLastIndication > _debounceTimeMs)) {
            _lastTensIndicationValue = indicationValue;
            _lastTensIndicationTime = now;

            // Notify caller about the new indication value
            // onIndicationReceived(indicationValue);

            // Update command processor
            _commandProcessor.handleTensIndication(
                indicationValue, currentLevel);

            onIndicationReceived(indicationValue);
          }
        }
      }, onError: (error) {
        debugPrint('Error in TENS indication listener: $error');
        _isTensIndicationListenerActive = false;
      }, onDone: () {
        debugPrint('TENS indication listener closed');
        _isTensIndicationListenerActive = false;
      });

      _isTensIndicationListenerActive = true;
    } catch (e) {
      debugPrint('Failed to initialize TENS indication listener: $e');
      _isTensIndicationListenerActive = false;
    } finally {
      _isTensSetupInProgress = false;
    }
  }

  /// Cancel the TENS indication listener
  void cancelTensIndicationListener() {
    _tensIndicationSubscription?.cancel();
    _tensIndicationSubscription = null;
    _isTensIndicationListenerActive = false;
    _commandProcessor.resetExpectedTensLevel();
    _lastTensIndicationValue = -1;
  }

  /// Initialize the heat indication listener
  Future<void> initHeatIndicationListener({
    required Function(int) onIndicationReceived,
    required int currentLevel,
  }) async {
    // If already active or setup in progress, don't duplicate
    if (_isHeatIndicationListenerActive || _isHeatSetupInProgress) {
      debugPrint('Heat indication listener already active or being set up');
      return;
    }

    _isHeatSetupInProgress = true;

    try {
      // Cancel any existing subscription first
      cancelHeatIndicationListener();

      Commands command = Commands(
          value: 0, commandType: 'currentTemp', commandTime: DateTime.now());

      // Start listening to indications for heat
      _heatIndicationSubscription =
          _bluetoothFacade.listenToIndication(command).listen((indication) {
        final indicationData = indication.getOrElse((_) => []);
        if (indicationData.isNotEmpty) {
          // Log the raw data for debugging
          // debugPrint("Raw heat indication data: $indication");

          try {
            // First convert the raw value to Celsius

            // Then map the Celsius value to a heat level
            final heatLevel =indicationData.first;
            // debugPrint("Mapped to heat level: $heatLevel");

            // Additional validation for heat level
            if (heatLevel < 0 || heatLevel > 3) {
              debugPrint(
                  "Heat level out of valid range: $heatLevel - ignoring");
              return;
            }

            // Notify caller about the new indication value
            onIndicationReceived(heatLevel);

            // Update command processor
            _commandProcessor.handleHeatIndication(heatLevel, currentLevel);
          } catch (e) {
            debugPrint("Error processing temperature data: $e");
          }
        }
      }



      , onError: (error) {
        debugPrint('Error in HEAT indication listener: $error');
        _isHeatIndicationListenerActive = false;
      }, onDone: () {
        debugPrint('HEAT indication listener closed');
        _isHeatIndicationListenerActive = false;
      });

      _isHeatIndicationListenerActive = true;
    } catch (e) {
      debugPrint('Failed to initialize HEAT indication listener: $e');
      _isHeatIndicationListenerActive = false;
    } finally {
      _isHeatSetupInProgress = false;
    }
  }

  ///initialize the mode indication listener
  Future<void> initModeIndicationListener({
    required Function(int) onIndicationReceived,
  }) async {
    // If already active or setup in progress, don't duplicate
    if (_isModeIndicationListenerActive ) {
      debugPrint('Mode indication listener already active or being set up');
      return;
    }

    try {
      // Cancel any existing subscription first
      cancelTensModeIndicationListener();

      Commands command = Commands(
          value: 0, commandType: 'setTensMode', commandTime: DateTime.now());

      // Start listening to indications for mode
      _tensModeIndicationSubscription =
          _bluetoothFacade.listenToIndication(command).listen((indication) {
        final indicationData = indication.getOrElse((_) => []);
        if (indicationData.isNotEmpty) {
          // Log the raw data for debugging
          debugPrint('Received mode indication value: $indicationData');

          // Process the indication with our improved utility
          final indicationValue = indicationData.first;
          // debugPrint('Received mode indication: $indicationValue');

          // Notify caller about the new indication value
          onIndicationReceived(indicationValue);
        }
      }, onError: (error) {
        debugPrint('Error in mode indication listener: $error');
        _isModeIndicationListenerActive = false;
      }, onDone: () {
        debugPrint('Mode indication listener closed');
        _isModeIndicationListenerActive = false;
      });

      _isModeIndicationListenerActive = true;
    } catch (e) {
      debugPrint('Failed to initialize mode indication listener: $e');
      _isModeIndicationListenerActive = false;
    } finally {
    }
  }

  /// Cancel the heat indication listener
  void cancelHeatIndicationListener()
  {
    _heatIndicationSubscription?.cancel();
    _heatIndicationSubscription = null;
    _isHeatIndicationListenerActive = false;
    _commandProcessor.resetExpectedHeatLevel();
  }
  /// Cancel the mode indication listener
  void cancelTensModeIndicationListener() {
    _tensModeIndicationSubscription?.cancel();
    _tensModeIndicationSubscription = null;
    _isModeIndicationListenerActive = false;
  }

  /// Check if TENS indication listener is active
  bool get isTensIndicationActive => _isTensIndicationListenerActive;

  /// Check if heat indication listener is active
  bool get isHeatIndicationActive => _isHeatIndicationListenerActive;

  /// Cleanup all resources
  void dispose() {
    cancelTensIndicationListener();
    cancelHeatIndicationListener();
    cancelTensModeIndicationListener();
  }
}
