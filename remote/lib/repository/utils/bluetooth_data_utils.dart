// / Utility functions for handling Bluetooth data conversion
// /
// / Contains functions for converting raw Bluetooth data to usable values and back

// / Converts a list of 4 bytes to a uint32 value
int listToUint32(List<int> bytes) {
  if (bytes.length != 4) {
    throw ArgumentError('List must be exactly 4 bytes long.');
  }
  return bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24);
}

/// Mapping between microamp values and TENS levels
const Map<int, int> microAmpLevels = {
  // TENS levels to microamp values
  -1: -1,
  0: 0,
  1: 12000,
  2: 15000,
  3: 18000,
  4: 21000,
  5: 24000,
  6: 29000,
  7: 34000,
  8: 39000,
  9: 44000,
  10: 49000,
};


/// Takes Bluetooth data as List<int> and returns the current TENS level as int
///
/// Converts raw microamp reading to a user-friendly level between 0-10
int getCurrentLevelFromBytes(List<int> bytes) {
  int currentMicroAmps = listToUint32(bytes);

  final sortedLevels = microAmpLevels.keys.toList()..sort();

  int lastLevelMicroAmps = 0;

  for (final microAmp in sortedLevels) {
    if (currentMicroAmps < microAmp) {
      return microAmpLevels[lastLevelMicroAmps] ?? 0;
    }
    lastLevelMicroAmps = microAmp;
  }

  // If it's higher than highest microAmp, return highest level
  return microAmpLevels[lastLevelMicroAmps] ?? 0;
}

// / Convert raw temperature bytes to Celsius value
// /
// / According to the BLE specification, temperature is:
// / - int32_t (4 bytes) in little-endian format
// / - Temperature in Celsius × 100 (e.g., 4250 = 42.50 °C)
double convertRawToCelsius(List<int> bytes) {
  // Check if we have the required number of bytes
  if (bytes.length < 4) {
    throw ArgumentError(
        'Temperature data requires at least 4 bytes, got ${bytes.length}');
  }

  // Extract the raw temperature value from the bytes (4-byte int32 in little-endian)
  int rawTemp =
      bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24);

  // Convert to Celsius (divide by 100 as per the specification)
  return rawTemp / 100.0;
}

/// Map Celsius temperature to heat level (0-3)
int mapTemperatureToHeatLevel(double celsius) {
  // Use the target temperatures specified for each level:
  // Level 0: Less than 32°C
  // Level 1: Target of 37°C (range approximately 32-39°C)
  // Level 2: Target of 40°C (range approximately 39-41.5°C)
  // Level 3: Target of 42.50°C (range approximately 41.5°C and above)

  if (celsius < 32.0) {
    return 0; // Off or very low heat
  } else if (celsius < 39.0) {
    return 1; // Low heat - target 37°C
  } else if (celsius < 41.5) {
    return 2; // Medium heat - target 40°C
  } else {
    return 3; // High heat - target 42.5°C
  }
}
