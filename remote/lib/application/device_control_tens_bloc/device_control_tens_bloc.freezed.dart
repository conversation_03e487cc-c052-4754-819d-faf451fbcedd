// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_tens_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeviceControlTensEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseTens,
    required TResult Function() decreaseTens,
    required TResult Function(int mode) changeMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseTens,
    TResult? Function()? decreaseTens,
    TResult? Function(int mode)? changeMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseTens,
    TResult Function()? decreaseTens,
    TResult Function(int mode)? changeMode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseTens value) increaseTens,
    required TResult Function(DecreaseTens value) decreaseTens,
    required TResult Function(ChangeMode value) changeMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseTens value)? increaseTens,
    TResult? Function(DecreaseTens value)? decreaseTens,
    TResult? Function(ChangeMode value)? changeMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseTens value)? increaseTens,
    TResult Function(DecreaseTens value)? decreaseTens,
    TResult Function(ChangeMode value)? changeMode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlTensEventCopyWith<$Res> {
  factory $DeviceControlTensEventCopyWith(DeviceControlTensEvent value,
          $Res Function(DeviceControlTensEvent) then) =
      _$DeviceControlTensEventCopyWithImpl<$Res, DeviceControlTensEvent>;
}

/// @nodoc
class _$DeviceControlTensEventCopyWithImpl<$Res,
        $Val extends DeviceControlTensEvent>
    implements $DeviceControlTensEventCopyWith<$Res> {
  _$DeviceControlTensEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlTensEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$IncreaseTensImplCopyWith<$Res> {
  factory _$$IncreaseTensImplCopyWith(
          _$IncreaseTensImpl value, $Res Function(_$IncreaseTensImpl) then) =
      __$$IncreaseTensImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$IncreaseTensImplCopyWithImpl<$Res>
    extends _$DeviceControlTensEventCopyWithImpl<$Res, _$IncreaseTensImpl>
    implements _$$IncreaseTensImplCopyWith<$Res> {
  __$$IncreaseTensImplCopyWithImpl(
      _$IncreaseTensImpl _value, $Res Function(_$IncreaseTensImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$IncreaseTensImpl implements IncreaseTens {
  const _$IncreaseTensImpl();

  @override
  String toString() {
    return 'DeviceControlTensEvent.increaseTens()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$IncreaseTensImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseTens,
    required TResult Function() decreaseTens,
    required TResult Function(int mode) changeMode,
  }) {
    return increaseTens();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseTens,
    TResult? Function()? decreaseTens,
    TResult? Function(int mode)? changeMode,
  }) {
    return increaseTens?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseTens,
    TResult Function()? decreaseTens,
    TResult Function(int mode)? changeMode,
    required TResult orElse(),
  }) {
    if (increaseTens != null) {
      return increaseTens();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseTens value) increaseTens,
    required TResult Function(DecreaseTens value) decreaseTens,
    required TResult Function(ChangeMode value) changeMode,
  }) {
    return increaseTens(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseTens value)? increaseTens,
    TResult? Function(DecreaseTens value)? decreaseTens,
    TResult? Function(ChangeMode value)? changeMode,
  }) {
    return increaseTens?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseTens value)? increaseTens,
    TResult Function(DecreaseTens value)? decreaseTens,
    TResult Function(ChangeMode value)? changeMode,
    required TResult orElse(),
  }) {
    if (increaseTens != null) {
      return increaseTens(this);
    }
    return orElse();
  }
}

abstract class IncreaseTens implements DeviceControlTensEvent {
  const factory IncreaseTens() = _$IncreaseTensImpl;
}

/// @nodoc
abstract class _$$DecreaseTensImplCopyWith<$Res> {
  factory _$$DecreaseTensImplCopyWith(
          _$DecreaseTensImpl value, $Res Function(_$DecreaseTensImpl) then) =
      __$$DecreaseTensImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DecreaseTensImplCopyWithImpl<$Res>
    extends _$DeviceControlTensEventCopyWithImpl<$Res, _$DecreaseTensImpl>
    implements _$$DecreaseTensImplCopyWith<$Res> {
  __$$DecreaseTensImplCopyWithImpl(
      _$DecreaseTensImpl _value, $Res Function(_$DecreaseTensImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DecreaseTensImpl implements DecreaseTens {
  const _$DecreaseTensImpl();

  @override
  String toString() {
    return 'DeviceControlTensEvent.decreaseTens()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DecreaseTensImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseTens,
    required TResult Function() decreaseTens,
    required TResult Function(int mode) changeMode,
  }) {
    return decreaseTens();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseTens,
    TResult? Function()? decreaseTens,
    TResult? Function(int mode)? changeMode,
  }) {
    return decreaseTens?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseTens,
    TResult Function()? decreaseTens,
    TResult Function(int mode)? changeMode,
    required TResult orElse(),
  }) {
    if (decreaseTens != null) {
      return decreaseTens();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseTens value) increaseTens,
    required TResult Function(DecreaseTens value) decreaseTens,
    required TResult Function(ChangeMode value) changeMode,
  }) {
    return decreaseTens(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseTens value)? increaseTens,
    TResult? Function(DecreaseTens value)? decreaseTens,
    TResult? Function(ChangeMode value)? changeMode,
  }) {
    return decreaseTens?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseTens value)? increaseTens,
    TResult Function(DecreaseTens value)? decreaseTens,
    TResult Function(ChangeMode value)? changeMode,
    required TResult orElse(),
  }) {
    if (decreaseTens != null) {
      return decreaseTens(this);
    }
    return orElse();
  }
}

abstract class DecreaseTens implements DeviceControlTensEvent {
  const factory DecreaseTens() = _$DecreaseTensImpl;
}

/// @nodoc
abstract class _$$ChangeModeImplCopyWith<$Res> {
  factory _$$ChangeModeImplCopyWith(
          _$ChangeModeImpl value, $Res Function(_$ChangeModeImpl) then) =
      __$$ChangeModeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int mode});
}

/// @nodoc
class __$$ChangeModeImplCopyWithImpl<$Res>
    extends _$DeviceControlTensEventCopyWithImpl<$Res, _$ChangeModeImpl>
    implements _$$ChangeModeImplCopyWith<$Res> {
  __$$ChangeModeImplCopyWithImpl(
      _$ChangeModeImpl _value, $Res Function(_$ChangeModeImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mode = null,
  }) {
    return _then(_$ChangeModeImpl(
      null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ChangeModeImpl implements ChangeMode {
  const _$ChangeModeImpl(this.mode);

  @override
  final int mode;

  @override
  String toString() {
    return 'DeviceControlTensEvent.changeMode(mode: $mode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeModeImpl &&
            (identical(other.mode, mode) || other.mode == mode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mode);

  /// Create a copy of DeviceControlTensEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeModeImplCopyWith<_$ChangeModeImpl> get copyWith =>
      __$$ChangeModeImplCopyWithImpl<_$ChangeModeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseTens,
    required TResult Function() decreaseTens,
    required TResult Function(int mode) changeMode,
  }) {
    return changeMode(mode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseTens,
    TResult? Function()? decreaseTens,
    TResult? Function(int mode)? changeMode,
  }) {
    return changeMode?.call(mode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseTens,
    TResult Function()? decreaseTens,
    TResult Function(int mode)? changeMode,
    required TResult orElse(),
  }) {
    if (changeMode != null) {
      return changeMode(mode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseTens value) increaseTens,
    required TResult Function(DecreaseTens value) decreaseTens,
    required TResult Function(ChangeMode value) changeMode,
  }) {
    return changeMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseTens value)? increaseTens,
    TResult? Function(DecreaseTens value)? decreaseTens,
    TResult? Function(ChangeMode value)? changeMode,
  }) {
    return changeMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseTens value)? increaseTens,
    TResult Function(DecreaseTens value)? decreaseTens,
    TResult Function(ChangeMode value)? changeMode,
    required TResult orElse(),
  }) {
    if (changeMode != null) {
      return changeMode(this);
    }
    return orElse();
  }
}

abstract class ChangeMode implements DeviceControlTensEvent {
  const factory ChangeMode(final int mode) = _$ChangeModeImpl;

  int get mode;

  /// Create a copy of DeviceControlTensEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChangeModeImplCopyWith<_$ChangeModeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DeviceControlTensState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeTensLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeTensLevelFailure,
    required TResult Function() changeModeSuccess,
    required TResult Function(RemoteFailure remoteFailure) changeModeFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeTensLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult? Function()? changeModeSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeModeFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeTensLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult Function()? changeModeSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeModeFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeTensLevelSuccess value)
        changeTensLevelSuccess,
    required TResult Function(ChangeTensLevelFailure value)
        changeTensLevelFailure,
    required TResult Function(ChangeModeSuccess value) changeModeSuccess,
    required TResult Function(ChangeModeFailure value) changeModeFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult? Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult? Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult? Function(ChangeModeFailure value)? changeModeFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult Function(ChangeModeFailure value)? changeModeFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlTensStateCopyWith<$Res> {
  factory $DeviceControlTensStateCopyWith(DeviceControlTensState value,
          $Res Function(DeviceControlTensState) then) =
      _$DeviceControlTensStateCopyWithImpl<$Res, DeviceControlTensState>;
}

/// @nodoc
class _$DeviceControlTensStateCopyWithImpl<$Res,
        $Val extends DeviceControlTensState>
    implements $DeviceControlTensStateCopyWith<$Res> {
  _$DeviceControlTensStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$DeviceControlTensStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'DeviceControlTensState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeTensLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeTensLevelFailure,
    required TResult Function() changeModeSuccess,
    required TResult Function(RemoteFailure remoteFailure) changeModeFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeTensLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult? Function()? changeModeSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeModeFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeTensLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult Function()? changeModeSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeTensLevelSuccess value)
        changeTensLevelSuccess,
    required TResult Function(ChangeTensLevelFailure value)
        changeTensLevelFailure,
    required TResult Function(ChangeModeSuccess value) changeModeSuccess,
    required TResult Function(ChangeModeFailure value) changeModeFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult? Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult? Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult? Function(ChangeModeFailure value)? changeModeFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult Function(ChangeModeFailure value)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements DeviceControlTensState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$ChangeTensLevelSuccessImplCopyWith<$Res> {
  factory _$$ChangeTensLevelSuccessImplCopyWith(
          _$ChangeTensLevelSuccessImpl value,
          $Res Function(_$ChangeTensLevelSuccessImpl) then) =
      __$$ChangeTensLevelSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ChangeTensLevelSuccessImplCopyWithImpl<$Res>
    extends _$DeviceControlTensStateCopyWithImpl<$Res,
        _$ChangeTensLevelSuccessImpl>
    implements _$$ChangeTensLevelSuccessImplCopyWith<$Res> {
  __$$ChangeTensLevelSuccessImplCopyWithImpl(
      _$ChangeTensLevelSuccessImpl _value,
      $Res Function(_$ChangeTensLevelSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ChangeTensLevelSuccessImpl implements ChangeTensLevelSuccess {
  const _$ChangeTensLevelSuccessImpl();

  @override
  String toString() {
    return 'DeviceControlTensState.changeTensLevelSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeTensLevelSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeTensLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeTensLevelFailure,
    required TResult Function() changeModeSuccess,
    required TResult Function(RemoteFailure remoteFailure) changeModeFailure,
  }) {
    return changeTensLevelSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeTensLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult? Function()? changeModeSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeModeFailure,
  }) {
    return changeTensLevelSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeTensLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult Function()? changeModeSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (changeTensLevelSuccess != null) {
      return changeTensLevelSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeTensLevelSuccess value)
        changeTensLevelSuccess,
    required TResult Function(ChangeTensLevelFailure value)
        changeTensLevelFailure,
    required TResult Function(ChangeModeSuccess value) changeModeSuccess,
    required TResult Function(ChangeModeFailure value) changeModeFailure,
  }) {
    return changeTensLevelSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult? Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult? Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult? Function(ChangeModeFailure value)? changeModeFailure,
  }) {
    return changeTensLevelSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult Function(ChangeModeFailure value)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (changeTensLevelSuccess != null) {
      return changeTensLevelSuccess(this);
    }
    return orElse();
  }
}

abstract class ChangeTensLevelSuccess implements DeviceControlTensState {
  const factory ChangeTensLevelSuccess() = _$ChangeTensLevelSuccessImpl;
}

/// @nodoc
abstract class _$$ChangeTensLevelFailureImplCopyWith<$Res> {
  factory _$$ChangeTensLevelFailureImplCopyWith(
          _$ChangeTensLevelFailureImpl value,
          $Res Function(_$ChangeTensLevelFailureImpl) then) =
      __$$ChangeTensLevelFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RemoteFailure remoteFailure});

  $RemoteFailureCopyWith<$Res> get remoteFailure;
}

/// @nodoc
class __$$ChangeTensLevelFailureImplCopyWithImpl<$Res>
    extends _$DeviceControlTensStateCopyWithImpl<$Res,
        _$ChangeTensLevelFailureImpl>
    implements _$$ChangeTensLevelFailureImplCopyWith<$Res> {
  __$$ChangeTensLevelFailureImplCopyWithImpl(
      _$ChangeTensLevelFailureImpl _value,
      $Res Function(_$ChangeTensLevelFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remoteFailure = null,
  }) {
    return _then(_$ChangeTensLevelFailureImpl(
      null == remoteFailure
          ? _value.remoteFailure
          : remoteFailure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get remoteFailure {
    return $RemoteFailureCopyWith<$Res>(_value.remoteFailure, (value) {
      return _then(_value.copyWith(remoteFailure: value));
    });
  }
}

/// @nodoc

class _$ChangeTensLevelFailureImpl implements ChangeTensLevelFailure {
  const _$ChangeTensLevelFailureImpl(this.remoteFailure);

  @override
  final RemoteFailure remoteFailure;

  @override
  String toString() {
    return 'DeviceControlTensState.changeTensLevelFailure(remoteFailure: $remoteFailure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeTensLevelFailureImpl &&
            (identical(other.remoteFailure, remoteFailure) ||
                other.remoteFailure == remoteFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, remoteFailure);

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeTensLevelFailureImplCopyWith<_$ChangeTensLevelFailureImpl>
      get copyWith => __$$ChangeTensLevelFailureImplCopyWithImpl<
          _$ChangeTensLevelFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeTensLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeTensLevelFailure,
    required TResult Function() changeModeSuccess,
    required TResult Function(RemoteFailure remoteFailure) changeModeFailure,
  }) {
    return changeTensLevelFailure(remoteFailure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeTensLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult? Function()? changeModeSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeModeFailure,
  }) {
    return changeTensLevelFailure?.call(remoteFailure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeTensLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult Function()? changeModeSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (changeTensLevelFailure != null) {
      return changeTensLevelFailure(remoteFailure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeTensLevelSuccess value)
        changeTensLevelSuccess,
    required TResult Function(ChangeTensLevelFailure value)
        changeTensLevelFailure,
    required TResult Function(ChangeModeSuccess value) changeModeSuccess,
    required TResult Function(ChangeModeFailure value) changeModeFailure,
  }) {
    return changeTensLevelFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult? Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult? Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult? Function(ChangeModeFailure value)? changeModeFailure,
  }) {
    return changeTensLevelFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult Function(ChangeModeFailure value)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (changeTensLevelFailure != null) {
      return changeTensLevelFailure(this);
    }
    return orElse();
  }
}

abstract class ChangeTensLevelFailure implements DeviceControlTensState {
  const factory ChangeTensLevelFailure(final RemoteFailure remoteFailure) =
      _$ChangeTensLevelFailureImpl;

  RemoteFailure get remoteFailure;

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChangeTensLevelFailureImplCopyWith<_$ChangeTensLevelFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeModeSuccessImplCopyWith<$Res> {
  factory _$$ChangeModeSuccessImplCopyWith(_$ChangeModeSuccessImpl value,
          $Res Function(_$ChangeModeSuccessImpl) then) =
      __$$ChangeModeSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ChangeModeSuccessImplCopyWithImpl<$Res>
    extends _$DeviceControlTensStateCopyWithImpl<$Res, _$ChangeModeSuccessImpl>
    implements _$$ChangeModeSuccessImplCopyWith<$Res> {
  __$$ChangeModeSuccessImplCopyWithImpl(_$ChangeModeSuccessImpl _value,
      $Res Function(_$ChangeModeSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ChangeModeSuccessImpl implements ChangeModeSuccess {
  const _$ChangeModeSuccessImpl();

  @override
  String toString() {
    return 'DeviceControlTensState.changeModeSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ChangeModeSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeTensLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeTensLevelFailure,
    required TResult Function() changeModeSuccess,
    required TResult Function(RemoteFailure remoteFailure) changeModeFailure,
  }) {
    return changeModeSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeTensLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult? Function()? changeModeSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeModeFailure,
  }) {
    return changeModeSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeTensLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult Function()? changeModeSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (changeModeSuccess != null) {
      return changeModeSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeTensLevelSuccess value)
        changeTensLevelSuccess,
    required TResult Function(ChangeTensLevelFailure value)
        changeTensLevelFailure,
    required TResult Function(ChangeModeSuccess value) changeModeSuccess,
    required TResult Function(ChangeModeFailure value) changeModeFailure,
  }) {
    return changeModeSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult? Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult? Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult? Function(ChangeModeFailure value)? changeModeFailure,
  }) {
    return changeModeSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult Function(ChangeModeFailure value)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (changeModeSuccess != null) {
      return changeModeSuccess(this);
    }
    return orElse();
  }
}

abstract class ChangeModeSuccess implements DeviceControlTensState {
  const factory ChangeModeSuccess() = _$ChangeModeSuccessImpl;
}

/// @nodoc
abstract class _$$ChangeModeFailureImplCopyWith<$Res> {
  factory _$$ChangeModeFailureImplCopyWith(_$ChangeModeFailureImpl value,
          $Res Function(_$ChangeModeFailureImpl) then) =
      __$$ChangeModeFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RemoteFailure remoteFailure});

  $RemoteFailureCopyWith<$Res> get remoteFailure;
}

/// @nodoc
class __$$ChangeModeFailureImplCopyWithImpl<$Res>
    extends _$DeviceControlTensStateCopyWithImpl<$Res, _$ChangeModeFailureImpl>
    implements _$$ChangeModeFailureImplCopyWith<$Res> {
  __$$ChangeModeFailureImplCopyWithImpl(_$ChangeModeFailureImpl _value,
      $Res Function(_$ChangeModeFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remoteFailure = null,
  }) {
    return _then(_$ChangeModeFailureImpl(
      null == remoteFailure
          ? _value.remoteFailure
          : remoteFailure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get remoteFailure {
    return $RemoteFailureCopyWith<$Res>(_value.remoteFailure, (value) {
      return _then(_value.copyWith(remoteFailure: value));
    });
  }
}

/// @nodoc

class _$ChangeModeFailureImpl implements ChangeModeFailure {
  const _$ChangeModeFailureImpl(this.remoteFailure);

  @override
  final RemoteFailure remoteFailure;

  @override
  String toString() {
    return 'DeviceControlTensState.changeModeFailure(remoteFailure: $remoteFailure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeModeFailureImpl &&
            (identical(other.remoteFailure, remoteFailure) ||
                other.remoteFailure == remoteFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, remoteFailure);

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeModeFailureImplCopyWith<_$ChangeModeFailureImpl> get copyWith =>
      __$$ChangeModeFailureImplCopyWithImpl<_$ChangeModeFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeTensLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeTensLevelFailure,
    required TResult Function() changeModeSuccess,
    required TResult Function(RemoteFailure remoteFailure) changeModeFailure,
  }) {
    return changeModeFailure(remoteFailure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeTensLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult? Function()? changeModeSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeModeFailure,
  }) {
    return changeModeFailure?.call(remoteFailure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeTensLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult Function()? changeModeSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (changeModeFailure != null) {
      return changeModeFailure(remoteFailure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeTensLevelSuccess value)
        changeTensLevelSuccess,
    required TResult Function(ChangeTensLevelFailure value)
        changeTensLevelFailure,
    required TResult Function(ChangeModeSuccess value) changeModeSuccess,
    required TResult Function(ChangeModeFailure value) changeModeFailure,
  }) {
    return changeModeFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult? Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult? Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult? Function(ChangeModeFailure value)? changeModeFailure,
  }) {
    return changeModeFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult Function(ChangeModeFailure value)? changeModeFailure,
    required TResult orElse(),
  }) {
    if (changeModeFailure != null) {
      return changeModeFailure(this);
    }
    return orElse();
  }
}

abstract class ChangeModeFailure implements DeviceControlTensState {
  const factory ChangeModeFailure(final RemoteFailure remoteFailure) =
      _$ChangeModeFailureImpl;

  RemoteFailure get remoteFailure;

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChangeModeFailureImplCopyWith<_$ChangeModeFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
