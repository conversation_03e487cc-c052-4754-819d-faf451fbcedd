part of 'device_control_tens_bloc.dart';

@freezed
class DeviceControlTensState with _$DeviceControlTensState {
  const factory  DeviceControlTensState.initial() = Initial;
  const factory DeviceControlTensState.changeTensLevelSuccess() = ChangeTensLevelSuccess;
  const factory DeviceControlTensState.changeTensLevelFailure(RemoteFailure remoteFailure) = ChangeTensLevelFailure;
  const factory DeviceControlTensState.changeModeSuccess() = ChangeModeSuccess;
  const factory DeviceControlTensState.changeModeFailure(RemoteFailure remoteFailure) = ChangeModeFailure;
}
