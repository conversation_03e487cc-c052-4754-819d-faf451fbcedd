// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_status_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeviceStatusWatcherEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, DeviceModel> failureOrDeviceModel)
        dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, DeviceModel> failureOrDeviceModel)?
        dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, DeviceModel> failureOrDeviceModel)?
        dataReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceStatusWatcherEventCopyWith<$Res> {
  factory $DeviceStatusWatcherEventCopyWith(DeviceStatusWatcherEvent value,
          $Res Function(DeviceStatusWatcherEvent) then) =
      _$DeviceStatusWatcherEventCopyWithImpl<$Res, DeviceStatusWatcherEvent>;
}

/// @nodoc
class _$DeviceStatusWatcherEventCopyWithImpl<$Res,
        $Val extends DeviceStatusWatcherEvent>
    implements $DeviceStatusWatcherEventCopyWith<$Res> {
  _$DeviceStatusWatcherEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceStatusWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$WatchAllStartedImplCopyWith<$Res> {
  factory _$$WatchAllStartedImplCopyWith(_$WatchAllStartedImpl value,
          $Res Function(_$WatchAllStartedImpl) then) =
      __$$WatchAllStartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WatchAllStartedImplCopyWithImpl<$Res>
    extends _$DeviceStatusWatcherEventCopyWithImpl<$Res, _$WatchAllStartedImpl>
    implements _$$WatchAllStartedImplCopyWith<$Res> {
  __$$WatchAllStartedImplCopyWithImpl(
      _$WatchAllStartedImpl _value, $Res Function(_$WatchAllStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceStatusWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WatchAllStartedImpl implements _WatchAllStarted {
  const _$WatchAllStartedImpl();

  @override
  String toString() {
    return 'DeviceStatusWatcherEvent.watchAllStarted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WatchAllStartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, DeviceModel> failureOrDeviceModel)
        dataReceived,
  }) {
    return watchAllStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, DeviceModel> failureOrDeviceModel)?
        dataReceived,
  }) {
    return watchAllStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, DeviceModel> failureOrDeviceModel)?
        dataReceived,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    return watchAllStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    return watchAllStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchAllStarted implements DeviceStatusWatcherEvent {
  const factory _WatchAllStarted() = _$WatchAllStartedImpl;
}

/// @nodoc
abstract class _$$DataReceivedImplCopyWith<$Res> {
  factory _$$DataReceivedImplCopyWith(
          _$DataReceivedImpl value, $Res Function(_$DataReceivedImpl) then) =
      __$$DataReceivedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Either<RemoteFailure, DeviceModel> failureOrDeviceModel});
}

/// @nodoc
class __$$DataReceivedImplCopyWithImpl<$Res>
    extends _$DeviceStatusWatcherEventCopyWithImpl<$Res, _$DataReceivedImpl>
    implements _$$DataReceivedImplCopyWith<$Res> {
  __$$DataReceivedImplCopyWithImpl(
      _$DataReceivedImpl _value, $Res Function(_$DataReceivedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceStatusWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrDeviceModel = null,
  }) {
    return _then(_$DataReceivedImpl(
      null == failureOrDeviceModel
          ? _value.failureOrDeviceModel
          : failureOrDeviceModel // ignore: cast_nullable_to_non_nullable
              as Either<RemoteFailure, DeviceModel>,
    ));
  }
}

/// @nodoc

class _$DataReceivedImpl implements _DataReceived {
  const _$DataReceivedImpl(this.failureOrDeviceModel);

  @override
  final Either<RemoteFailure, DeviceModel> failureOrDeviceModel;

  @override
  String toString() {
    return 'DeviceStatusWatcherEvent.dataReceived(failureOrDeviceModel: $failureOrDeviceModel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DataReceivedImpl &&
            (identical(other.failureOrDeviceModel, failureOrDeviceModel) ||
                other.failureOrDeviceModel == failureOrDeviceModel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrDeviceModel);

  /// Create a copy of DeviceStatusWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DataReceivedImplCopyWith<_$DataReceivedImpl> get copyWith =>
      __$$DataReceivedImplCopyWithImpl<_$DataReceivedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, DeviceModel> failureOrDeviceModel)
        dataReceived,
  }) {
    return dataReceived(failureOrDeviceModel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, DeviceModel> failureOrDeviceModel)?
        dataReceived,
  }) {
    return dataReceived?.call(failureOrDeviceModel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, DeviceModel> failureOrDeviceModel)?
        dataReceived,
    required TResult orElse(),
  }) {
    if (dataReceived != null) {
      return dataReceived(failureOrDeviceModel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    return dataReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    return dataReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    if (dataReceived != null) {
      return dataReceived(this);
    }
    return orElse();
  }
}

abstract class _DataReceived implements DeviceStatusWatcherEvent {
  const factory _DataReceived(
          final Either<RemoteFailure, DeviceModel> failureOrDeviceModel) =
      _$DataReceivedImpl;

  Either<RemoteFailure, DeviceModel> get failureOrDeviceModel;

  /// Create a copy of DeviceStatusWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DataReceivedImplCopyWith<_$DataReceivedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DeviceStatusWatcherState {
  DeviceInfoModel get deviceInformation => throw _privateConstructorUsedError;
  bool get isDeviceConnected => throw _privateConstructorUsedError;
  bool get isDeviceOn => throw _privateConstructorUsedError;
  bool get isDeviceReady => throw _privateConstructorUsedError;
  bool get isDeviceError => throw _privateConstructorUsedError;
  bool get isDeviceBusy => throw _privateConstructorUsedError;
  bool get isDeviceOff => throw _privateConstructorUsedError;
  bool get isDeviceDisconnected => throw _privateConstructorUsedError;
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption =>
      throw _privateConstructorUsedError;

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceStatusWatcherStateCopyWith<DeviceStatusWatcherState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceStatusWatcherStateCopyWith<$Res> {
  factory $DeviceStatusWatcherStateCopyWith(DeviceStatusWatcherState value,
          $Res Function(DeviceStatusWatcherState) then) =
      _$DeviceStatusWatcherStateCopyWithImpl<$Res, DeviceStatusWatcherState>;
  @useResult
  $Res call(
      {DeviceInfoModel deviceInformation,
      bool isDeviceConnected,
      bool isDeviceOn,
      bool isDeviceReady,
      bool isDeviceError,
      bool isDeviceBusy,
      bool isDeviceOff,
      bool isDeviceDisconnected,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class _$DeviceStatusWatcherStateCopyWithImpl<$Res,
        $Val extends DeviceStatusWatcherState>
    implements $DeviceStatusWatcherStateCopyWith<$Res> {
  _$DeviceStatusWatcherStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceInformation = null,
    Object? isDeviceConnected = null,
    Object? isDeviceOn = null,
    Object? isDeviceReady = null,
    Object? isDeviceError = null,
    Object? isDeviceBusy = null,
    Object? isDeviceOff = null,
    Object? isDeviceDisconnected = null,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_value.copyWith(
      deviceInformation: null == deviceInformation
          ? _value.deviceInformation
          : deviceInformation // ignore: cast_nullable_to_non_nullable
              as DeviceInfoModel,
      isDeviceConnected: null == isDeviceConnected
          ? _value.isDeviceConnected
          : isDeviceConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceOn: null == isDeviceOn
          ? _value.isDeviceOn
          : isDeviceOn // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceReady: null == isDeviceReady
          ? _value.isDeviceReady
          : isDeviceReady // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceError: null == isDeviceError
          ? _value.isDeviceError
          : isDeviceError // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceBusy: null == isDeviceBusy
          ? _value.isDeviceBusy
          : isDeviceBusy // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceOff: null == isDeviceOff
          ? _value.isDeviceOff
          : isDeviceOff // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceDisconnected: null == isDeviceDisconnected
          ? _value.isDeviceDisconnected
          : isDeviceDisconnected // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeviceStatusWatcherStateImplCopyWith<$Res>
    implements $DeviceStatusWatcherStateCopyWith<$Res> {
  factory _$$DeviceStatusWatcherStateImplCopyWith(
          _$DeviceStatusWatcherStateImpl value,
          $Res Function(_$DeviceStatusWatcherStateImpl) then) =
      __$$DeviceStatusWatcherStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DeviceInfoModel deviceInformation,
      bool isDeviceConnected,
      bool isDeviceOn,
      bool isDeviceReady,
      bool isDeviceError,
      bool isDeviceBusy,
      bool isDeviceOff,
      bool isDeviceDisconnected,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class __$$DeviceStatusWatcherStateImplCopyWithImpl<$Res>
    extends _$DeviceStatusWatcherStateCopyWithImpl<$Res,
        _$DeviceStatusWatcherStateImpl>
    implements _$$DeviceStatusWatcherStateImplCopyWith<$Res> {
  __$$DeviceStatusWatcherStateImplCopyWithImpl(
      _$DeviceStatusWatcherStateImpl _value,
      $Res Function(_$DeviceStatusWatcherStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceInformation = null,
    Object? isDeviceConnected = null,
    Object? isDeviceOn = null,
    Object? isDeviceReady = null,
    Object? isDeviceError = null,
    Object? isDeviceBusy = null,
    Object? isDeviceOff = null,
    Object? isDeviceDisconnected = null,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_$DeviceStatusWatcherStateImpl(
      deviceInformation: null == deviceInformation
          ? _value.deviceInformation
          : deviceInformation // ignore: cast_nullable_to_non_nullable
              as DeviceInfoModel,
      isDeviceConnected: null == isDeviceConnected
          ? _value.isDeviceConnected
          : isDeviceConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceOn: null == isDeviceOn
          ? _value.isDeviceOn
          : isDeviceOn // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceReady: null == isDeviceReady
          ? _value.isDeviceReady
          : isDeviceReady // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceError: null == isDeviceError
          ? _value.isDeviceError
          : isDeviceError // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceBusy: null == isDeviceBusy
          ? _value.isDeviceBusy
          : isDeviceBusy // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceOff: null == isDeviceOff
          ? _value.isDeviceOff
          : isDeviceOff // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceDisconnected: null == isDeviceDisconnected
          ? _value.isDeviceDisconnected
          : isDeviceDisconnected // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ));
  }
}

/// @nodoc

class _$DeviceStatusWatcherStateImpl extends _DeviceStatusWatcherState {
  const _$DeviceStatusWatcherStateImpl(
      {required this.deviceInformation,
      required this.isDeviceConnected,
      required this.isDeviceOn,
      required this.isDeviceReady,
      required this.isDeviceError,
      required this.isDeviceBusy,
      required this.isDeviceOff,
      required this.isDeviceDisconnected,
      required this.failureOrSuccessOption})
      : super._();

  @override
  final DeviceInfoModel deviceInformation;
  @override
  final bool isDeviceConnected;
  @override
  final bool isDeviceOn;
  @override
  final bool isDeviceReady;
  @override
  final bool isDeviceError;
  @override
  final bool isDeviceBusy;
  @override
  final bool isDeviceOff;
  @override
  final bool isDeviceDisconnected;
  @override
  final Option<Either<RemoteFailure, Unit>> failureOrSuccessOption;

  @override
  String toString() {
    return 'DeviceStatusWatcherState(deviceInformation: $deviceInformation, isDeviceConnected: $isDeviceConnected, isDeviceOn: $isDeviceOn, isDeviceReady: $isDeviceReady, isDeviceError: $isDeviceError, isDeviceBusy: $isDeviceBusy, isDeviceOff: $isDeviceOff, isDeviceDisconnected: $isDeviceDisconnected, failureOrSuccessOption: $failureOrSuccessOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceStatusWatcherStateImpl &&
            (identical(other.deviceInformation, deviceInformation) ||
                other.deviceInformation == deviceInformation) &&
            (identical(other.isDeviceConnected, isDeviceConnected) ||
                other.isDeviceConnected == isDeviceConnected) &&
            (identical(other.isDeviceOn, isDeviceOn) ||
                other.isDeviceOn == isDeviceOn) &&
            (identical(other.isDeviceReady, isDeviceReady) ||
                other.isDeviceReady == isDeviceReady) &&
            (identical(other.isDeviceError, isDeviceError) ||
                other.isDeviceError == isDeviceError) &&
            (identical(other.isDeviceBusy, isDeviceBusy) ||
                other.isDeviceBusy == isDeviceBusy) &&
            (identical(other.isDeviceOff, isDeviceOff) ||
                other.isDeviceOff == isDeviceOff) &&
            (identical(other.isDeviceDisconnected, isDeviceDisconnected) ||
                other.isDeviceDisconnected == isDeviceDisconnected) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      deviceInformation,
      isDeviceConnected,
      isDeviceOn,
      isDeviceReady,
      isDeviceError,
      isDeviceBusy,
      isDeviceOff,
      isDeviceDisconnected,
      failureOrSuccessOption);

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceStatusWatcherStateImplCopyWith<_$DeviceStatusWatcherStateImpl>
      get copyWith => __$$DeviceStatusWatcherStateImplCopyWithImpl<
          _$DeviceStatusWatcherStateImpl>(this, _$identity);
}

abstract class _DeviceStatusWatcherState extends DeviceStatusWatcherState {
  const factory _DeviceStatusWatcherState(
      {required final DeviceInfoModel deviceInformation,
      required final bool isDeviceConnected,
      required final bool isDeviceOn,
      required final bool isDeviceReady,
      required final bool isDeviceError,
      required final bool isDeviceBusy,
      required final bool isDeviceOff,
      required final bool isDeviceDisconnected,
      required final Option<Either<RemoteFailure, Unit>>
          failureOrSuccessOption}) = _$DeviceStatusWatcherStateImpl;
  const _DeviceStatusWatcherState._() : super._();

  @override
  DeviceInfoModel get deviceInformation;
  @override
  bool get isDeviceConnected;
  @override
  bool get isDeviceOn;
  @override
  bool get isDeviceReady;
  @override
  bool get isDeviceError;
  @override
  bool get isDeviceBusy;
  @override
  bool get isDeviceOff;
  @override
  bool get isDeviceDisconnected;
  @override
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption;

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceStatusWatcherStateImplCopyWith<_$DeviceStatusWatcherStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
