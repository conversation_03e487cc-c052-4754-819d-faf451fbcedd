part of 'device_control_tens_watcher_bloc.dart';

@freezed
class DeviceControlTensWatcherState with _$DeviceControlTensWatcherState {
  const DeviceControlTensWatcherState._();

  const factory DeviceControlTensWatcherState({
    required int selectedTensLevel,
    required int actualTensLevel,
    required int selectedMode,
    int? applyingLevel,
    required Option<Either<RemoteFailure, Unit>> failureOrSuccessOption,
  }) = _DeviceControlTensWatcherState;

  factory DeviceControlTensWatcherState.initial() =>
      const DeviceControlTensWatcherState(
        selectedTensLevel: 0,
        actualTensLevel: 0,
        selectedMode: 1,
        applyingLevel: null,
        failureOrSuccessOption: None(),
      );
}
