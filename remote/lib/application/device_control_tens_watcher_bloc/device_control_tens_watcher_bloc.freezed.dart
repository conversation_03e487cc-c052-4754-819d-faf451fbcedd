// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_tens_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeviceControlTensWatcherEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, TensLevelModel> failureOrTensLevel)
        tensLevelChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, TensLevelModel> failureOrTensLevel)?
        tensLevelChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, TensLevelModel> failureOrTensLevel)?
        tensLevelChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_TensLevelChanged value) tensLevelChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_TensLevelChanged value)? tensLevelChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_TensLevelChanged value)? tensLevelChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlTensWatcherEventCopyWith<$Res> {
  factory $DeviceControlTensWatcherEventCopyWith(
          DeviceControlTensWatcherEvent value,
          $Res Function(DeviceControlTensWatcherEvent) then) =
      _$DeviceControlTensWatcherEventCopyWithImpl<$Res,
          DeviceControlTensWatcherEvent>;
}

/// @nodoc
class _$DeviceControlTensWatcherEventCopyWithImpl<$Res,
        $Val extends DeviceControlTensWatcherEvent>
    implements $DeviceControlTensWatcherEventCopyWith<$Res> {
  _$DeviceControlTensWatcherEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlTensWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$WatchAllStartedImplCopyWith<$Res> {
  factory _$$WatchAllStartedImplCopyWith(_$WatchAllStartedImpl value,
          $Res Function(_$WatchAllStartedImpl) then) =
      __$$WatchAllStartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WatchAllStartedImplCopyWithImpl<$Res>
    extends _$DeviceControlTensWatcherEventCopyWithImpl<$Res,
        _$WatchAllStartedImpl> implements _$$WatchAllStartedImplCopyWith<$Res> {
  __$$WatchAllStartedImplCopyWithImpl(
      _$WatchAllStartedImpl _value, $Res Function(_$WatchAllStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WatchAllStartedImpl implements _WatchAllStarted {
  const _$WatchAllStartedImpl();

  @override
  String toString() {
    return 'DeviceControlTensWatcherEvent.watchAllStarted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WatchAllStartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, TensLevelModel> failureOrTensLevel)
        tensLevelChanged,
  }) {
    return watchAllStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, TensLevelModel> failureOrTensLevel)?
        tensLevelChanged,
  }) {
    return watchAllStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, TensLevelModel> failureOrTensLevel)?
        tensLevelChanged,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_TensLevelChanged value) tensLevelChanged,
  }) {
    return watchAllStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_TensLevelChanged value)? tensLevelChanged,
  }) {
    return watchAllStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_TensLevelChanged value)? tensLevelChanged,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchAllStarted implements DeviceControlTensWatcherEvent {
  const factory _WatchAllStarted() = _$WatchAllStartedImpl;
}

/// @nodoc
abstract class _$$TensLevelChangedImplCopyWith<$Res> {
  factory _$$TensLevelChangedImplCopyWith(_$TensLevelChangedImpl value,
          $Res Function(_$TensLevelChangedImpl) then) =
      __$$TensLevelChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Either<RemoteFailure, TensLevelModel> failureOrTensLevel});
}

/// @nodoc
class __$$TensLevelChangedImplCopyWithImpl<$Res>
    extends _$DeviceControlTensWatcherEventCopyWithImpl<$Res,
        _$TensLevelChangedImpl>
    implements _$$TensLevelChangedImplCopyWith<$Res> {
  __$$TensLevelChangedImplCopyWithImpl(_$TensLevelChangedImpl _value,
      $Res Function(_$TensLevelChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrTensLevel = null,
  }) {
    return _then(_$TensLevelChangedImpl(
      null == failureOrTensLevel
          ? _value.failureOrTensLevel
          : failureOrTensLevel // ignore: cast_nullable_to_non_nullable
              as Either<RemoteFailure, TensLevelModel>,
    ));
  }
}

/// @nodoc

class _$TensLevelChangedImpl implements _TensLevelChanged {
  const _$TensLevelChangedImpl(this.failureOrTensLevel);

  @override
  final Either<RemoteFailure, TensLevelModel> failureOrTensLevel;

  @override
  String toString() {
    return 'DeviceControlTensWatcherEvent.tensLevelChanged(failureOrTensLevel: $failureOrTensLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TensLevelChangedImpl &&
            (identical(other.failureOrTensLevel, failureOrTensLevel) ||
                other.failureOrTensLevel == failureOrTensLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrTensLevel);

  /// Create a copy of DeviceControlTensWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TensLevelChangedImplCopyWith<_$TensLevelChangedImpl> get copyWith =>
      __$$TensLevelChangedImplCopyWithImpl<_$TensLevelChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, TensLevelModel> failureOrTensLevel)
        tensLevelChanged,
  }) {
    return tensLevelChanged(failureOrTensLevel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, TensLevelModel> failureOrTensLevel)?
        tensLevelChanged,
  }) {
    return tensLevelChanged?.call(failureOrTensLevel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, TensLevelModel> failureOrTensLevel)?
        tensLevelChanged,
    required TResult orElse(),
  }) {
    if (tensLevelChanged != null) {
      return tensLevelChanged(failureOrTensLevel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_TensLevelChanged value) tensLevelChanged,
  }) {
    return tensLevelChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_TensLevelChanged value)? tensLevelChanged,
  }) {
    return tensLevelChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_TensLevelChanged value)? tensLevelChanged,
    required TResult orElse(),
  }) {
    if (tensLevelChanged != null) {
      return tensLevelChanged(this);
    }
    return orElse();
  }
}

abstract class _TensLevelChanged implements DeviceControlTensWatcherEvent {
  const factory _TensLevelChanged(
          final Either<RemoteFailure, TensLevelModel> failureOrTensLevel) =
      _$TensLevelChangedImpl;

  Either<RemoteFailure, TensLevelModel> get failureOrTensLevel;

  /// Create a copy of DeviceControlTensWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TensLevelChangedImplCopyWith<_$TensLevelChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DeviceControlTensWatcherState {
  int get selectedTensLevel => throw _privateConstructorUsedError;
  int get actualTensLevel => throw _privateConstructorUsedError;
  int get selectedMode => throw _privateConstructorUsedError;
  int? get applyingLevel => throw _privateConstructorUsedError;
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption =>
      throw _privateConstructorUsedError;

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceControlTensWatcherStateCopyWith<DeviceControlTensWatcherState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlTensWatcherStateCopyWith<$Res> {
  factory $DeviceControlTensWatcherStateCopyWith(
          DeviceControlTensWatcherState value,
          $Res Function(DeviceControlTensWatcherState) then) =
      _$DeviceControlTensWatcherStateCopyWithImpl<$Res,
          DeviceControlTensWatcherState>;
  @useResult
  $Res call(
      {int selectedTensLevel,
      int actualTensLevel,
      int selectedMode,
      int? applyingLevel,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class _$DeviceControlTensWatcherStateCopyWithImpl<$Res,
        $Val extends DeviceControlTensWatcherState>
    implements $DeviceControlTensWatcherStateCopyWith<$Res> {
  _$DeviceControlTensWatcherStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedTensLevel = null,
    Object? actualTensLevel = null,
    Object? selectedMode = null,
    Object? applyingLevel = freezed,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_value.copyWith(
      selectedTensLevel: null == selectedTensLevel
          ? _value.selectedTensLevel
          : selectedTensLevel // ignore: cast_nullable_to_non_nullable
              as int,
      actualTensLevel: null == actualTensLevel
          ? _value.actualTensLevel
          : actualTensLevel // ignore: cast_nullable_to_non_nullable
              as int,
      selectedMode: null == selectedMode
          ? _value.selectedMode
          : selectedMode // ignore: cast_nullable_to_non_nullable
              as int,
      applyingLevel: freezed == applyingLevel
          ? _value.applyingLevel
          : applyingLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeviceControlTensWatcherStateImplCopyWith<$Res>
    implements $DeviceControlTensWatcherStateCopyWith<$Res> {
  factory _$$DeviceControlTensWatcherStateImplCopyWith(
          _$DeviceControlTensWatcherStateImpl value,
          $Res Function(_$DeviceControlTensWatcherStateImpl) then) =
      __$$DeviceControlTensWatcherStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int selectedTensLevel,
      int actualTensLevel,
      int selectedMode,
      int? applyingLevel,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class __$$DeviceControlTensWatcherStateImplCopyWithImpl<$Res>
    extends _$DeviceControlTensWatcherStateCopyWithImpl<$Res,
        _$DeviceControlTensWatcherStateImpl>
    implements _$$DeviceControlTensWatcherStateImplCopyWith<$Res> {
  __$$DeviceControlTensWatcherStateImplCopyWithImpl(
      _$DeviceControlTensWatcherStateImpl _value,
      $Res Function(_$DeviceControlTensWatcherStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedTensLevel = null,
    Object? actualTensLevel = null,
    Object? selectedMode = null,
    Object? applyingLevel = freezed,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_$DeviceControlTensWatcherStateImpl(
      selectedTensLevel: null == selectedTensLevel
          ? _value.selectedTensLevel
          : selectedTensLevel // ignore: cast_nullable_to_non_nullable
              as int,
      actualTensLevel: null == actualTensLevel
          ? _value.actualTensLevel
          : actualTensLevel // ignore: cast_nullable_to_non_nullable
              as int,
      selectedMode: null == selectedMode
          ? _value.selectedMode
          : selectedMode // ignore: cast_nullable_to_non_nullable
              as int,
      applyingLevel: freezed == applyingLevel
          ? _value.applyingLevel
          : applyingLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ));
  }
}

/// @nodoc

class _$DeviceControlTensWatcherStateImpl
    extends _DeviceControlTensWatcherState {
  const _$DeviceControlTensWatcherStateImpl(
      {required this.selectedTensLevel,
      required this.actualTensLevel,
      required this.selectedMode,
      this.applyingLevel,
      required this.failureOrSuccessOption})
      : super._();

  @override
  final int selectedTensLevel;
  @override
  final int actualTensLevel;
  @override
  final int selectedMode;
  @override
  final int? applyingLevel;
  @override
  final Option<Either<RemoteFailure, Unit>> failureOrSuccessOption;

  @override
  String toString() {
    return 'DeviceControlTensWatcherState(selectedTensLevel: $selectedTensLevel, actualTensLevel: $actualTensLevel, selectedMode: $selectedMode, applyingLevel: $applyingLevel, failureOrSuccessOption: $failureOrSuccessOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceControlTensWatcherStateImpl &&
            (identical(other.selectedTensLevel, selectedTensLevel) ||
                other.selectedTensLevel == selectedTensLevel) &&
            (identical(other.actualTensLevel, actualTensLevel) ||
                other.actualTensLevel == actualTensLevel) &&
            (identical(other.selectedMode, selectedMode) ||
                other.selectedMode == selectedMode) &&
            (identical(other.applyingLevel, applyingLevel) ||
                other.applyingLevel == applyingLevel) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedTensLevel,
      actualTensLevel, selectedMode, applyingLevel, failureOrSuccessOption);

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceControlTensWatcherStateImplCopyWith<
          _$DeviceControlTensWatcherStateImpl>
      get copyWith => __$$DeviceControlTensWatcherStateImplCopyWithImpl<
          _$DeviceControlTensWatcherStateImpl>(this, _$identity);
}

abstract class _DeviceControlTensWatcherState
    extends DeviceControlTensWatcherState {
  const factory _DeviceControlTensWatcherState(
      {required final int selectedTensLevel,
      required final int actualTensLevel,
      required final int selectedMode,
      final int? applyingLevel,
      required final Option<Either<RemoteFailure, Unit>>
          failureOrSuccessOption}) = _$DeviceControlTensWatcherStateImpl;
  const _DeviceControlTensWatcherState._() : super._();

  @override
  int get selectedTensLevel;
  @override
  int get actualTensLevel;
  @override
  int get selectedMode;
  @override
  int? get applyingLevel;
  @override
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption;

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceControlTensWatcherStateImplCopyWith<
          _$DeviceControlTensWatcherStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
