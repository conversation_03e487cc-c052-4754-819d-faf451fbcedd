import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/remote_control_facade.dart';
import '../../domain/failures/remote_failures.dart';
import '../../domain/model/tens_level_model.dart';

part 'device_control_tens_watcher_event.dart';
part 'device_control_tens_watcher_state.dart';
part 'device_control_tens_watcher_bloc.freezed.dart';

@injectable
class DeviceControlTensWatcherBloc
    extends Bloc<DeviceControlTensWatcherEvent, DeviceControlTensWatcherState> {
  final RemoteControlFacade _remoteRepository;
  StreamSubscription<Either<RemoteFailure, TensLevelModel>>?
      _remoteSubscription;
  DeviceControlTensWatcherBloc(this._remoteRepository)
      : super(DeviceControlTensWatcherState.initial()) {
    on<_WatchAllStarted>(_onWatchAllStarted);
    on<_TensLevelChanged>(_onTensLevelChanged);
  }

  void _onWatchAllStarted(_WatchAllStarted event,
      Emitter<DeviceControlTensWatcherState> emit) async {
    // First emit the initial state with None
    emit(DeviceControlTensWatcherState.initial());

    await _remoteSubscription?.cancel();
    _remoteSubscription = _remoteRepository.watchTensLevel().listen(
      (failureOrTensLevel) {
        if (isClosed) return; // Prevent adding events after close
        add(DeviceControlTensWatcherEvent.tensLevelChanged(failureOrTensLevel));
      },
    );
  }

  void _onTensLevelChanged(
      _TensLevelChanged event, Emitter<DeviceControlTensWatcherState> emit) {
    event.failureOrTensLevel.mapBoth(
      onLeft: (failure) => emit(state.copyWith(
        failureOrSuccessOption: Some(Left(failure)),
      )),
      onRight: (TensLevel) => emit(state.copyWith(
        actualTensLevel: TensLevel.actualTensLevel,
        selectedTensLevel: TensLevel.selectedTensLevel!,
        selectedMode: TensLevel.mode,
        applyingLevel: TensLevel.applyingLevel,
        failureOrSuccessOption: Some(const Right(unit)),
      )),
    );
  }

  @override
  Future<void> close() async {
    await _remoteSubscription?.cancel(); // Ensure subscription cleanup
    return super.close();
  }
}
