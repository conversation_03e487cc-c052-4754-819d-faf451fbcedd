part of 'device_control_heat_watcher_bloc.dart';

@freezed
class DeviceControlHeatWatcherState with _$DeviceControlHeatWatcherState {
  const DeviceControlHeatWatcherState._();

  const factory DeviceControlHeatWatcherState({
    required int selectedHeatLevel,
    required int actualHeatLevel,

    required Option<Either<RemoteFailure, Unit>> failureOrSuccessOption,
  }) = _DeviceControlHeatWatcherState;

  factory DeviceControlHeatWatcherState.initial() => const DeviceControlHeatWatcherState(
        selectedHeatLevel: 0,
        actualHeatLevel: 0,
        failureOrSuccessOption: None(),
      );
}


