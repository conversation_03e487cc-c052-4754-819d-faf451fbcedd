// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_heat_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeviceControlHeatWatcherEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)
        heatLevelChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)?
        heatLevelChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)?
        heatLevelChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_HeatLevelChanged value) heatLevelChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_HeatLevelChanged value)? heatLevelChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_HeatLevelChanged value)? heatLevelChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlHeatWatcherEventCopyWith<$Res> {
  factory $DeviceControlHeatWatcherEventCopyWith(
          DeviceControlHeatWatcherEvent value,
          $Res Function(DeviceControlHeatWatcherEvent) then) =
      _$DeviceControlHeatWatcherEventCopyWithImpl<$Res,
          DeviceControlHeatWatcherEvent>;
}

/// @nodoc
class _$DeviceControlHeatWatcherEventCopyWithImpl<$Res,
        $Val extends DeviceControlHeatWatcherEvent>
    implements $DeviceControlHeatWatcherEventCopyWith<$Res> {
  _$DeviceControlHeatWatcherEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlHeatWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$WatchAllStartedImplCopyWith<$Res> {
  factory _$$WatchAllStartedImplCopyWith(_$WatchAllStartedImpl value,
          $Res Function(_$WatchAllStartedImpl) then) =
      __$$WatchAllStartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WatchAllStartedImplCopyWithImpl<$Res>
    extends _$DeviceControlHeatWatcherEventCopyWithImpl<$Res,
        _$WatchAllStartedImpl> implements _$$WatchAllStartedImplCopyWith<$Res> {
  __$$WatchAllStartedImplCopyWithImpl(
      _$WatchAllStartedImpl _value, $Res Function(_$WatchAllStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlHeatWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WatchAllStartedImpl implements _WatchAllStarted {
  const _$WatchAllStartedImpl();

  @override
  String toString() {
    return 'DeviceControlHeatWatcherEvent.watchAllStarted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WatchAllStartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)
        heatLevelChanged,
  }) {
    return watchAllStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)?
        heatLevelChanged,
  }) {
    return watchAllStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)?
        heatLevelChanged,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_HeatLevelChanged value) heatLevelChanged,
  }) {
    return watchAllStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_HeatLevelChanged value)? heatLevelChanged,
  }) {
    return watchAllStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_HeatLevelChanged value)? heatLevelChanged,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchAllStarted implements DeviceControlHeatWatcherEvent {
  const factory _WatchAllStarted() = _$WatchAllStartedImpl;
}

/// @nodoc
abstract class _$$HeatLevelChangedImplCopyWith<$Res> {
  factory _$$HeatLevelChangedImplCopyWith(_$HeatLevelChangedImpl value,
          $Res Function(_$HeatLevelChangedImpl) then) =
      __$$HeatLevelChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel});
}

/// @nodoc
class __$$HeatLevelChangedImplCopyWithImpl<$Res>
    extends _$DeviceControlHeatWatcherEventCopyWithImpl<$Res,
        _$HeatLevelChangedImpl>
    implements _$$HeatLevelChangedImplCopyWith<$Res> {
  __$$HeatLevelChangedImplCopyWithImpl(_$HeatLevelChangedImpl _value,
      $Res Function(_$HeatLevelChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlHeatWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrHeatLevel = null,
  }) {
    return _then(_$HeatLevelChangedImpl(
      null == failureOrHeatLevel
          ? _value.failureOrHeatLevel
          : failureOrHeatLevel // ignore: cast_nullable_to_non_nullable
              as Either<RemoteFailure, HeatLevelModel>,
    ));
  }
}

/// @nodoc

class _$HeatLevelChangedImpl implements _HeatLevelChanged {
  const _$HeatLevelChangedImpl(this.failureOrHeatLevel);

  @override
  final Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel;

  @override
  String toString() {
    return 'DeviceControlHeatWatcherEvent.heatLevelChanged(failureOrHeatLevel: $failureOrHeatLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HeatLevelChangedImpl &&
            (identical(other.failureOrHeatLevel, failureOrHeatLevel) ||
                other.failureOrHeatLevel == failureOrHeatLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrHeatLevel);

  /// Create a copy of DeviceControlHeatWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HeatLevelChangedImplCopyWith<_$HeatLevelChangedImpl> get copyWith =>
      __$$HeatLevelChangedImplCopyWithImpl<_$HeatLevelChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)
        heatLevelChanged,
  }) {
    return heatLevelChanged(failureOrHeatLevel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)?
        heatLevelChanged,
  }) {
    return heatLevelChanged?.call(failureOrHeatLevel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel)?
        heatLevelChanged,
    required TResult orElse(),
  }) {
    if (heatLevelChanged != null) {
      return heatLevelChanged(failureOrHeatLevel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_HeatLevelChanged value) heatLevelChanged,
  }) {
    return heatLevelChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_HeatLevelChanged value)? heatLevelChanged,
  }) {
    return heatLevelChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_HeatLevelChanged value)? heatLevelChanged,
    required TResult orElse(),
  }) {
    if (heatLevelChanged != null) {
      return heatLevelChanged(this);
    }
    return orElse();
  }
}

abstract class _HeatLevelChanged implements DeviceControlHeatWatcherEvent {
  const factory _HeatLevelChanged(
          final Either<RemoteFailure, HeatLevelModel> failureOrHeatLevel) =
      _$HeatLevelChangedImpl;

  Either<RemoteFailure, HeatLevelModel> get failureOrHeatLevel;

  /// Create a copy of DeviceControlHeatWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HeatLevelChangedImplCopyWith<_$HeatLevelChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DeviceControlHeatWatcherState {
  int get selectedHeatLevel => throw _privateConstructorUsedError;
  int get actualHeatLevel => throw _privateConstructorUsedError;
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption =>
      throw _privateConstructorUsedError;

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceControlHeatWatcherStateCopyWith<DeviceControlHeatWatcherState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlHeatWatcherStateCopyWith<$Res> {
  factory $DeviceControlHeatWatcherStateCopyWith(
          DeviceControlHeatWatcherState value,
          $Res Function(DeviceControlHeatWatcherState) then) =
      _$DeviceControlHeatWatcherStateCopyWithImpl<$Res,
          DeviceControlHeatWatcherState>;
  @useResult
  $Res call(
      {int selectedHeatLevel,
      int actualHeatLevel,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class _$DeviceControlHeatWatcherStateCopyWithImpl<$Res,
        $Val extends DeviceControlHeatWatcherState>
    implements $DeviceControlHeatWatcherStateCopyWith<$Res> {
  _$DeviceControlHeatWatcherStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedHeatLevel = null,
    Object? actualHeatLevel = null,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_value.copyWith(
      selectedHeatLevel: null == selectedHeatLevel
          ? _value.selectedHeatLevel
          : selectedHeatLevel // ignore: cast_nullable_to_non_nullable
              as int,
      actualHeatLevel: null == actualHeatLevel
          ? _value.actualHeatLevel
          : actualHeatLevel // ignore: cast_nullable_to_non_nullable
              as int,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeviceControlHeatWatcherStateImplCopyWith<$Res>
    implements $DeviceControlHeatWatcherStateCopyWith<$Res> {
  factory _$$DeviceControlHeatWatcherStateImplCopyWith(
          _$DeviceControlHeatWatcherStateImpl value,
          $Res Function(_$DeviceControlHeatWatcherStateImpl) then) =
      __$$DeviceControlHeatWatcherStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int selectedHeatLevel,
      int actualHeatLevel,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class __$$DeviceControlHeatWatcherStateImplCopyWithImpl<$Res>
    extends _$DeviceControlHeatWatcherStateCopyWithImpl<$Res,
        _$DeviceControlHeatWatcherStateImpl>
    implements _$$DeviceControlHeatWatcherStateImplCopyWith<$Res> {
  __$$DeviceControlHeatWatcherStateImplCopyWithImpl(
      _$DeviceControlHeatWatcherStateImpl _value,
      $Res Function(_$DeviceControlHeatWatcherStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedHeatLevel = null,
    Object? actualHeatLevel = null,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_$DeviceControlHeatWatcherStateImpl(
      selectedHeatLevel: null == selectedHeatLevel
          ? _value.selectedHeatLevel
          : selectedHeatLevel // ignore: cast_nullable_to_non_nullable
              as int,
      actualHeatLevel: null == actualHeatLevel
          ? _value.actualHeatLevel
          : actualHeatLevel // ignore: cast_nullable_to_non_nullable
              as int,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ));
  }
}

/// @nodoc

class _$DeviceControlHeatWatcherStateImpl
    extends _DeviceControlHeatWatcherState {
  const _$DeviceControlHeatWatcherStateImpl(
      {required this.selectedHeatLevel,
      required this.actualHeatLevel,
      required this.failureOrSuccessOption})
      : super._();

  @override
  final int selectedHeatLevel;
  @override
  final int actualHeatLevel;
  @override
  final Option<Either<RemoteFailure, Unit>> failureOrSuccessOption;

  @override
  String toString() {
    return 'DeviceControlHeatWatcherState(selectedHeatLevel: $selectedHeatLevel, actualHeatLevel: $actualHeatLevel, failureOrSuccessOption: $failureOrSuccessOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceControlHeatWatcherStateImpl &&
            (identical(other.selectedHeatLevel, selectedHeatLevel) ||
                other.selectedHeatLevel == selectedHeatLevel) &&
            (identical(other.actualHeatLevel, actualHeatLevel) ||
                other.actualHeatLevel == actualHeatLevel) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, selectedHeatLevel, actualHeatLevel, failureOrSuccessOption);

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceControlHeatWatcherStateImplCopyWith<
          _$DeviceControlHeatWatcherStateImpl>
      get copyWith => __$$DeviceControlHeatWatcherStateImplCopyWithImpl<
          _$DeviceControlHeatWatcherStateImpl>(this, _$identity);
}

abstract class _DeviceControlHeatWatcherState
    extends DeviceControlHeatWatcherState {
  const factory _DeviceControlHeatWatcherState(
      {required final int selectedHeatLevel,
      required final int actualHeatLevel,
      required final Option<Either<RemoteFailure, Unit>>
          failureOrSuccessOption}) = _$DeviceControlHeatWatcherStateImpl;
  const _DeviceControlHeatWatcherState._() : super._();

  @override
  int get selectedHeatLevel;
  @override
  int get actualHeatLevel;
  @override
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption;

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceControlHeatWatcherStateImplCopyWith<
          _$DeviceControlHeatWatcherStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
