// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_heat_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeviceControlHeatEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseHeat,
    required TResult Function() decreaseHeat,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseHeat,
    TResult? Function()? decreaseHeat,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseHeat,
    TResult Function()? decreaseHeat,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseHeat value) increaseHeat,
    required TResult Function(DecreaseHeat value) decreaseHeat,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseHeat value)? increaseHeat,
    TResult? Function(DecreaseHeat value)? decreaseHeat,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseHeat value)? increaseHeat,
    TResult Function(DecreaseHeat value)? decreaseHeat,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlHeatEventCopyWith<$Res> {
  factory $DeviceControlHeatEventCopyWith(DeviceControlHeatEvent value,
          $Res Function(DeviceControlHeatEvent) then) =
      _$DeviceControlHeatEventCopyWithImpl<$Res, DeviceControlHeatEvent>;
}

/// @nodoc
class _$DeviceControlHeatEventCopyWithImpl<$Res,
        $Val extends DeviceControlHeatEvent>
    implements $DeviceControlHeatEventCopyWith<$Res> {
  _$DeviceControlHeatEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlHeatEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$IncreaseHeatImplCopyWith<$Res> {
  factory _$$IncreaseHeatImplCopyWith(
          _$IncreaseHeatImpl value, $Res Function(_$IncreaseHeatImpl) then) =
      __$$IncreaseHeatImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$IncreaseHeatImplCopyWithImpl<$Res>
    extends _$DeviceControlHeatEventCopyWithImpl<$Res, _$IncreaseHeatImpl>
    implements _$$IncreaseHeatImplCopyWith<$Res> {
  __$$IncreaseHeatImplCopyWithImpl(
      _$IncreaseHeatImpl _value, $Res Function(_$IncreaseHeatImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlHeatEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$IncreaseHeatImpl implements IncreaseHeat {
  const _$IncreaseHeatImpl();

  @override
  String toString() {
    return 'DeviceControlHeatEvent.increaseHeat()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$IncreaseHeatImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseHeat,
    required TResult Function() decreaseHeat,
  }) {
    return increaseHeat();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseHeat,
    TResult? Function()? decreaseHeat,
  }) {
    return increaseHeat?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseHeat,
    TResult Function()? decreaseHeat,
    required TResult orElse(),
  }) {
    if (increaseHeat != null) {
      return increaseHeat();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseHeat value) increaseHeat,
    required TResult Function(DecreaseHeat value) decreaseHeat,
  }) {
    return increaseHeat(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseHeat value)? increaseHeat,
    TResult? Function(DecreaseHeat value)? decreaseHeat,
  }) {
    return increaseHeat?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseHeat value)? increaseHeat,
    TResult Function(DecreaseHeat value)? decreaseHeat,
    required TResult orElse(),
  }) {
    if (increaseHeat != null) {
      return increaseHeat(this);
    }
    return orElse();
  }
}

abstract class IncreaseHeat implements DeviceControlHeatEvent {
  const factory IncreaseHeat() = _$IncreaseHeatImpl;
}

/// @nodoc
abstract class _$$DecreaseHeatImplCopyWith<$Res> {
  factory _$$DecreaseHeatImplCopyWith(
          _$DecreaseHeatImpl value, $Res Function(_$DecreaseHeatImpl) then) =
      __$$DecreaseHeatImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DecreaseHeatImplCopyWithImpl<$Res>
    extends _$DeviceControlHeatEventCopyWithImpl<$Res, _$DecreaseHeatImpl>
    implements _$$DecreaseHeatImplCopyWith<$Res> {
  __$$DecreaseHeatImplCopyWithImpl(
      _$DecreaseHeatImpl _value, $Res Function(_$DecreaseHeatImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlHeatEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DecreaseHeatImpl implements DecreaseHeat {
  const _$DecreaseHeatImpl();

  @override
  String toString() {
    return 'DeviceControlHeatEvent.decreaseHeat()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DecreaseHeatImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseHeat,
    required TResult Function() decreaseHeat,
  }) {
    return decreaseHeat();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseHeat,
    TResult? Function()? decreaseHeat,
  }) {
    return decreaseHeat?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseHeat,
    TResult Function()? decreaseHeat,
    required TResult orElse(),
  }) {
    if (decreaseHeat != null) {
      return decreaseHeat();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseHeat value) increaseHeat,
    required TResult Function(DecreaseHeat value) decreaseHeat,
  }) {
    return decreaseHeat(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseHeat value)? increaseHeat,
    TResult? Function(DecreaseHeat value)? decreaseHeat,
  }) {
    return decreaseHeat?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseHeat value)? increaseHeat,
    TResult Function(DecreaseHeat value)? decreaseHeat,
    required TResult orElse(),
  }) {
    if (decreaseHeat != null) {
      return decreaseHeat(this);
    }
    return orElse();
  }
}

abstract class DecreaseHeat implements DeviceControlHeatEvent {
  const factory DecreaseHeat() = _$DecreaseHeatImpl;
}

/// @nodoc
mixin _$DeviceControlHeatState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeHeatLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeHeatLevelFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeHeatLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeHeatLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeHeatLevelSuccess value)
        changeHeatLevelSuccess,
    required TResult Function(ChangeHeatLevelFailure value)
        changeHeatLevelFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult? Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlHeatStateCopyWith<$Res> {
  factory $DeviceControlHeatStateCopyWith(DeviceControlHeatState value,
          $Res Function(DeviceControlHeatState) then) =
      _$DeviceControlHeatStateCopyWithImpl<$Res, DeviceControlHeatState>;
}

/// @nodoc
class _$DeviceControlHeatStateCopyWithImpl<$Res,
        $Val extends DeviceControlHeatState>
    implements $DeviceControlHeatStateCopyWith<$Res> {
  _$DeviceControlHeatStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$DeviceControlHeatStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'DeviceControlHeatState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeHeatLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeHeatLevelFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeHeatLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeHeatLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeHeatLevelSuccess value)
        changeHeatLevelSuccess,
    required TResult Function(ChangeHeatLevelFailure value)
        changeHeatLevelFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult? Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements DeviceControlHeatState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$ChangeHeatLevelSuccessImplCopyWith<$Res> {
  factory _$$ChangeHeatLevelSuccessImplCopyWith(
          _$ChangeHeatLevelSuccessImpl value,
          $Res Function(_$ChangeHeatLevelSuccessImpl) then) =
      __$$ChangeHeatLevelSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ChangeHeatLevelSuccessImplCopyWithImpl<$Res>
    extends _$DeviceControlHeatStateCopyWithImpl<$Res,
        _$ChangeHeatLevelSuccessImpl>
    implements _$$ChangeHeatLevelSuccessImplCopyWith<$Res> {
  __$$ChangeHeatLevelSuccessImplCopyWithImpl(
      _$ChangeHeatLevelSuccessImpl _value,
      $Res Function(_$ChangeHeatLevelSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ChangeHeatLevelSuccessImpl implements ChangeHeatLevelSuccess {
  const _$ChangeHeatLevelSuccessImpl();

  @override
  String toString() {
    return 'DeviceControlHeatState.changeHeatLevelSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeHeatLevelSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeHeatLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeHeatLevelFailure,
  }) {
    return changeHeatLevelSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeHeatLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
  }) {
    return changeHeatLevelSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeHeatLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
    required TResult orElse(),
  }) {
    if (changeHeatLevelSuccess != null) {
      return changeHeatLevelSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeHeatLevelSuccess value)
        changeHeatLevelSuccess,
    required TResult Function(ChangeHeatLevelFailure value)
        changeHeatLevelFailure,
  }) {
    return changeHeatLevelSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult? Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
  }) {
    return changeHeatLevelSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
    required TResult orElse(),
  }) {
    if (changeHeatLevelSuccess != null) {
      return changeHeatLevelSuccess(this);
    }
    return orElse();
  }
}

abstract class ChangeHeatLevelSuccess implements DeviceControlHeatState {
  const factory ChangeHeatLevelSuccess() = _$ChangeHeatLevelSuccessImpl;
}

/// @nodoc
abstract class _$$ChangeHeatLevelFailureImplCopyWith<$Res> {
  factory _$$ChangeHeatLevelFailureImplCopyWith(
          _$ChangeHeatLevelFailureImpl value,
          $Res Function(_$ChangeHeatLevelFailureImpl) then) =
      __$$ChangeHeatLevelFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RemoteFailure remoteFailure});

  $RemoteFailureCopyWith<$Res> get remoteFailure;
}

/// @nodoc
class __$$ChangeHeatLevelFailureImplCopyWithImpl<$Res>
    extends _$DeviceControlHeatStateCopyWithImpl<$Res,
        _$ChangeHeatLevelFailureImpl>
    implements _$$ChangeHeatLevelFailureImplCopyWith<$Res> {
  __$$ChangeHeatLevelFailureImplCopyWithImpl(
      _$ChangeHeatLevelFailureImpl _value,
      $Res Function(_$ChangeHeatLevelFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remoteFailure = null,
  }) {
    return _then(_$ChangeHeatLevelFailureImpl(
      null == remoteFailure
          ? _value.remoteFailure
          : remoteFailure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get remoteFailure {
    return $RemoteFailureCopyWith<$Res>(_value.remoteFailure, (value) {
      return _then(_value.copyWith(remoteFailure: value));
    });
  }
}

/// @nodoc

class _$ChangeHeatLevelFailureImpl implements ChangeHeatLevelFailure {
  const _$ChangeHeatLevelFailureImpl(this.remoteFailure);

  @override
  final RemoteFailure remoteFailure;

  @override
  String toString() {
    return 'DeviceControlHeatState.changeHeatLevelFailure(remoteFailure: $remoteFailure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeHeatLevelFailureImpl &&
            (identical(other.remoteFailure, remoteFailure) ||
                other.remoteFailure == remoteFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, remoteFailure);

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeHeatLevelFailureImplCopyWith<_$ChangeHeatLevelFailureImpl>
      get copyWith => __$$ChangeHeatLevelFailureImplCopyWithImpl<
          _$ChangeHeatLevelFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeHeatLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeHeatLevelFailure,
  }) {
    return changeHeatLevelFailure(remoteFailure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeHeatLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
  }) {
    return changeHeatLevelFailure?.call(remoteFailure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeHeatLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
    required TResult orElse(),
  }) {
    if (changeHeatLevelFailure != null) {
      return changeHeatLevelFailure(remoteFailure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeHeatLevelSuccess value)
        changeHeatLevelSuccess,
    required TResult Function(ChangeHeatLevelFailure value)
        changeHeatLevelFailure,
  }) {
    return changeHeatLevelFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult? Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
  }) {
    return changeHeatLevelFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
    required TResult orElse(),
  }) {
    if (changeHeatLevelFailure != null) {
      return changeHeatLevelFailure(this);
    }
    return orElse();
  }
}

abstract class ChangeHeatLevelFailure implements DeviceControlHeatState {
  const factory ChangeHeatLevelFailure(final RemoteFailure remoteFailure) =
      _$ChangeHeatLevelFailureImpl;

  RemoteFailure get remoteFailure;

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChangeHeatLevelFailureImplCopyWith<_$ChangeHeatLevelFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}
