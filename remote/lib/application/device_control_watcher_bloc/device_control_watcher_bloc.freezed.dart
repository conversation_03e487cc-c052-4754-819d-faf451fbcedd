// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeviceControlWatcherEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(Either<RemoteFailure, bool> failureOrState)
        therapyStateChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, bool> failureOrState)?
        therapyStateChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, bool> failureOrState)?
        therapyStateChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_TherapyStateChanged value) therapyStateChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_TherapyStateChanged value)? therapyStateChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlWatcherEventCopyWith<$Res> {
  factory $DeviceControlWatcherEventCopyWith(DeviceControlWatcherEvent value,
          $Res Function(DeviceControlWatcherEvent) then) =
      _$DeviceControlWatcherEventCopyWithImpl<$Res, DeviceControlWatcherEvent>;
}

/// @nodoc
class _$DeviceControlWatcherEventCopyWithImpl<$Res,
        $Val extends DeviceControlWatcherEvent>
    implements $DeviceControlWatcherEventCopyWith<$Res> {
  _$DeviceControlWatcherEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$WatchAllStartedImplCopyWith<$Res> {
  factory _$$WatchAllStartedImplCopyWith(_$WatchAllStartedImpl value,
          $Res Function(_$WatchAllStartedImpl) then) =
      __$$WatchAllStartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WatchAllStartedImplCopyWithImpl<$Res>
    extends _$DeviceControlWatcherEventCopyWithImpl<$Res, _$WatchAllStartedImpl>
    implements _$$WatchAllStartedImplCopyWith<$Res> {
  __$$WatchAllStartedImplCopyWithImpl(
      _$WatchAllStartedImpl _value, $Res Function(_$WatchAllStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WatchAllStartedImpl implements _WatchAllStarted {
  const _$WatchAllStartedImpl();

  @override
  String toString() {
    return 'DeviceControlWatcherEvent.watchAllStarted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WatchAllStartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(Either<RemoteFailure, bool> failureOrState)
        therapyStateChanged,
  }) {
    return watchAllStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, bool> failureOrState)?
        therapyStateChanged,
  }) {
    return watchAllStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, bool> failureOrState)?
        therapyStateChanged,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_TherapyStateChanged value) therapyStateChanged,
  }) {
    return watchAllStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_TherapyStateChanged value)? therapyStateChanged,
  }) {
    return watchAllStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchAllStarted implements DeviceControlWatcherEvent {
  const factory _WatchAllStarted() = _$WatchAllStartedImpl;
}

/// @nodoc
abstract class _$$TherapyStateChangedImplCopyWith<$Res> {
  factory _$$TherapyStateChangedImplCopyWith(_$TherapyStateChangedImpl value,
          $Res Function(_$TherapyStateChangedImpl) then) =
      __$$TherapyStateChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Either<RemoteFailure, bool> failureOrState});
}

/// @nodoc
class __$$TherapyStateChangedImplCopyWithImpl<$Res>
    extends _$DeviceControlWatcherEventCopyWithImpl<$Res,
        _$TherapyStateChangedImpl>
    implements _$$TherapyStateChangedImplCopyWith<$Res> {
  __$$TherapyStateChangedImplCopyWithImpl(_$TherapyStateChangedImpl _value,
      $Res Function(_$TherapyStateChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrState = null,
  }) {
    return _then(_$TherapyStateChangedImpl(
      null == failureOrState
          ? _value.failureOrState
          : failureOrState // ignore: cast_nullable_to_non_nullable
              as Either<RemoteFailure, bool>,
    ));
  }
}

/// @nodoc

class _$TherapyStateChangedImpl implements _TherapyStateChanged {
  const _$TherapyStateChangedImpl(this.failureOrState);

  @override
  final Either<RemoteFailure, bool> failureOrState;

  @override
  String toString() {
    return 'DeviceControlWatcherEvent.therapyStateChanged(failureOrState: $failureOrState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TherapyStateChangedImpl &&
            (identical(other.failureOrState, failureOrState) ||
                other.failureOrState == failureOrState));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrState);

  /// Create a copy of DeviceControlWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TherapyStateChangedImplCopyWith<_$TherapyStateChangedImpl> get copyWith =>
      __$$TherapyStateChangedImplCopyWithImpl<_$TherapyStateChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(Either<RemoteFailure, bool> failureOrState)
        therapyStateChanged,
  }) {
    return therapyStateChanged(failureOrState);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, bool> failureOrState)?
        therapyStateChanged,
  }) {
    return therapyStateChanged?.call(failureOrState);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, bool> failureOrState)?
        therapyStateChanged,
    required TResult orElse(),
  }) {
    if (therapyStateChanged != null) {
      return therapyStateChanged(failureOrState);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_TherapyStateChanged value) therapyStateChanged,
  }) {
    return therapyStateChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_TherapyStateChanged value)? therapyStateChanged,
  }) {
    return therapyStateChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (therapyStateChanged != null) {
      return therapyStateChanged(this);
    }
    return orElse();
  }
}

abstract class _TherapyStateChanged implements DeviceControlWatcherEvent {
  const factory _TherapyStateChanged(
          final Either<RemoteFailure, bool> failureOrState) =
      _$TherapyStateChangedImpl;

  Either<RemoteFailure, bool> get failureOrState;

  /// Create a copy of DeviceControlWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TherapyStateChangedImplCopyWith<_$TherapyStateChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DeviceControlWatcherState {
  bool get isTherapyActive => throw _privateConstructorUsedError;
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption =>
      throw _privateConstructorUsedError;

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceControlWatcherStateCopyWith<DeviceControlWatcherState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlWatcherStateCopyWith<$Res> {
  factory $DeviceControlWatcherStateCopyWith(DeviceControlWatcherState value,
          $Res Function(DeviceControlWatcherState) then) =
      _$DeviceControlWatcherStateCopyWithImpl<$Res, DeviceControlWatcherState>;
  @useResult
  $Res call(
      {bool isTherapyActive,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class _$DeviceControlWatcherStateCopyWithImpl<$Res,
        $Val extends DeviceControlWatcherState>
    implements $DeviceControlWatcherStateCopyWith<$Res> {
  _$DeviceControlWatcherStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isTherapyActive = null,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_value.copyWith(
      isTherapyActive: null == isTherapyActive
          ? _value.isTherapyActive
          : isTherapyActive // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeviceControlWatcherStateImplCopyWith<$Res>
    implements $DeviceControlWatcherStateCopyWith<$Res> {
  factory _$$DeviceControlWatcherStateImplCopyWith(
          _$DeviceControlWatcherStateImpl value,
          $Res Function(_$DeviceControlWatcherStateImpl) then) =
      __$$DeviceControlWatcherStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isTherapyActive,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class __$$DeviceControlWatcherStateImplCopyWithImpl<$Res>
    extends _$DeviceControlWatcherStateCopyWithImpl<$Res,
        _$DeviceControlWatcherStateImpl>
    implements _$$DeviceControlWatcherStateImplCopyWith<$Res> {
  __$$DeviceControlWatcherStateImplCopyWithImpl(
      _$DeviceControlWatcherStateImpl _value,
      $Res Function(_$DeviceControlWatcherStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isTherapyActive = null,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_$DeviceControlWatcherStateImpl(
      isTherapyActive: null == isTherapyActive
          ? _value.isTherapyActive
          : isTherapyActive // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ));
  }
}

/// @nodoc

class _$DeviceControlWatcherStateImpl implements _DeviceControlWatcherState {
  const _$DeviceControlWatcherStateImpl(
      {required this.isTherapyActive, required this.failureOrSuccessOption});

  @override
  final bool isTherapyActive;
  @override
  final Option<Either<RemoteFailure, Unit>> failureOrSuccessOption;

  @override
  String toString() {
    return 'DeviceControlWatcherState(isTherapyActive: $isTherapyActive, failureOrSuccessOption: $failureOrSuccessOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceControlWatcherStateImpl &&
            (identical(other.isTherapyActive, isTherapyActive) ||
                other.isTherapyActive == isTherapyActive) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isTherapyActive, failureOrSuccessOption);

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceControlWatcherStateImplCopyWith<_$DeviceControlWatcherStateImpl>
      get copyWith => __$$DeviceControlWatcherStateImplCopyWithImpl<
          _$DeviceControlWatcherStateImpl>(this, _$identity);
}

abstract class _DeviceControlWatcherState implements DeviceControlWatcherState {
  const factory _DeviceControlWatcherState(
      {required final bool isTherapyActive,
      required final Option<Either<RemoteFailure, Unit>>
          failureOrSuccessOption}) = _$DeviceControlWatcherStateImpl;

  @override
  bool get isTherapyActive;
  @override
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption;

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceControlWatcherStateImplCopyWith<_$DeviceControlWatcherStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
