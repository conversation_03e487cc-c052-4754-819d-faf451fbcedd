part of 'device_control_bloc.dart';

@freezed
class DeviceControlState with _$DeviceControlState {
  const factory DeviceControlState.initial() = Initial;
  const factory DeviceControlState.actionSuccess() = ActionSuccess;
  const factory DeviceControlState.actionFailure(RemoteFailure remoteFailure) =
      ActionFailure;
  const factory DeviceControlState.therapyStateChanged(bool isActive) =
      TherapyStateChanged;
}
