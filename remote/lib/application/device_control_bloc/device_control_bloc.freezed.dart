// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeviceControlEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() powerOff,
    required TResult Function() toggleTherapy,
    required TResult Function(RemoteFailure failure) actionFailureEvent,
    required TResult Function(bool isActive) therapyStateChangedEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? powerOff,
    TResult? Function()? toggleTherapy,
    TResult? Function(RemoteFailure failure)? actionFailureEvent,
    TResult? Function(bool isActive)? therapyStateChangedEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? powerOff,
    TResult Function()? toggleTherapy,
    TResult Function(RemoteFailure failure)? actionFailureEvent,
    TResult Function(bool isActive)? therapyStateChangedEvent,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PowerOff value) powerOff,
    required TResult Function(ToggleTherapy value) toggleTherapy,
    required TResult Function(ActionFailureEvent value) actionFailureEvent,
    required TResult Function(TherapyStateChangedEvent value)
        therapyStateChangedEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PowerOff value)? powerOff,
    TResult? Function(ToggleTherapy value)? toggleTherapy,
    TResult? Function(ActionFailureEvent value)? actionFailureEvent,
    TResult? Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PowerOff value)? powerOff,
    TResult Function(ToggleTherapy value)? toggleTherapy,
    TResult Function(ActionFailureEvent value)? actionFailureEvent,
    TResult Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlEventCopyWith<$Res> {
  factory $DeviceControlEventCopyWith(
          DeviceControlEvent value, $Res Function(DeviceControlEvent) then) =
      _$DeviceControlEventCopyWithImpl<$Res, DeviceControlEvent>;
}

/// @nodoc
class _$DeviceControlEventCopyWithImpl<$Res, $Val extends DeviceControlEvent>
    implements $DeviceControlEventCopyWith<$Res> {
  _$DeviceControlEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PowerOffImplCopyWith<$Res> {
  factory _$$PowerOffImplCopyWith(
          _$PowerOffImpl value, $Res Function(_$PowerOffImpl) then) =
      __$$PowerOffImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PowerOffImplCopyWithImpl<$Res>
    extends _$DeviceControlEventCopyWithImpl<$Res, _$PowerOffImpl>
    implements _$$PowerOffImplCopyWith<$Res> {
  __$$PowerOffImplCopyWithImpl(
      _$PowerOffImpl _value, $Res Function(_$PowerOffImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PowerOffImpl implements PowerOff {
  const _$PowerOffImpl();

  @override
  String toString() {
    return 'DeviceControlEvent.powerOff()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PowerOffImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() powerOff,
    required TResult Function() toggleTherapy,
    required TResult Function(RemoteFailure failure) actionFailureEvent,
    required TResult Function(bool isActive) therapyStateChangedEvent,
  }) {
    return powerOff();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? powerOff,
    TResult? Function()? toggleTherapy,
    TResult? Function(RemoteFailure failure)? actionFailureEvent,
    TResult? Function(bool isActive)? therapyStateChangedEvent,
  }) {
    return powerOff?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? powerOff,
    TResult Function()? toggleTherapy,
    TResult Function(RemoteFailure failure)? actionFailureEvent,
    TResult Function(bool isActive)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    if (powerOff != null) {
      return powerOff();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PowerOff value) powerOff,
    required TResult Function(ToggleTherapy value) toggleTherapy,
    required TResult Function(ActionFailureEvent value) actionFailureEvent,
    required TResult Function(TherapyStateChangedEvent value)
        therapyStateChangedEvent,
  }) {
    return powerOff(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PowerOff value)? powerOff,
    TResult? Function(ToggleTherapy value)? toggleTherapy,
    TResult? Function(ActionFailureEvent value)? actionFailureEvent,
    TResult? Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
  }) {
    return powerOff?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PowerOff value)? powerOff,
    TResult Function(ToggleTherapy value)? toggleTherapy,
    TResult Function(ActionFailureEvent value)? actionFailureEvent,
    TResult Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    if (powerOff != null) {
      return powerOff(this);
    }
    return orElse();
  }
}

abstract class PowerOff implements DeviceControlEvent {
  const factory PowerOff() = _$PowerOffImpl;
}

/// @nodoc
abstract class _$$ToggleTherapyImplCopyWith<$Res> {
  factory _$$ToggleTherapyImplCopyWith(
          _$ToggleTherapyImpl value, $Res Function(_$ToggleTherapyImpl) then) =
      __$$ToggleTherapyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ToggleTherapyImplCopyWithImpl<$Res>
    extends _$DeviceControlEventCopyWithImpl<$Res, _$ToggleTherapyImpl>
    implements _$$ToggleTherapyImplCopyWith<$Res> {
  __$$ToggleTherapyImplCopyWithImpl(
      _$ToggleTherapyImpl _value, $Res Function(_$ToggleTherapyImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ToggleTherapyImpl implements ToggleTherapy {
  const _$ToggleTherapyImpl();

  @override
  String toString() {
    return 'DeviceControlEvent.toggleTherapy()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ToggleTherapyImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() powerOff,
    required TResult Function() toggleTherapy,
    required TResult Function(RemoteFailure failure) actionFailureEvent,
    required TResult Function(bool isActive) therapyStateChangedEvent,
  }) {
    return toggleTherapy();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? powerOff,
    TResult? Function()? toggleTherapy,
    TResult? Function(RemoteFailure failure)? actionFailureEvent,
    TResult? Function(bool isActive)? therapyStateChangedEvent,
  }) {
    return toggleTherapy?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? powerOff,
    TResult Function()? toggleTherapy,
    TResult Function(RemoteFailure failure)? actionFailureEvent,
    TResult Function(bool isActive)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    if (toggleTherapy != null) {
      return toggleTherapy();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PowerOff value) powerOff,
    required TResult Function(ToggleTherapy value) toggleTherapy,
    required TResult Function(ActionFailureEvent value) actionFailureEvent,
    required TResult Function(TherapyStateChangedEvent value)
        therapyStateChangedEvent,
  }) {
    return toggleTherapy(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PowerOff value)? powerOff,
    TResult? Function(ToggleTherapy value)? toggleTherapy,
    TResult? Function(ActionFailureEvent value)? actionFailureEvent,
    TResult? Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
  }) {
    return toggleTherapy?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PowerOff value)? powerOff,
    TResult Function(ToggleTherapy value)? toggleTherapy,
    TResult Function(ActionFailureEvent value)? actionFailureEvent,
    TResult Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    if (toggleTherapy != null) {
      return toggleTherapy(this);
    }
    return orElse();
  }
}

abstract class ToggleTherapy implements DeviceControlEvent {
  const factory ToggleTherapy() = _$ToggleTherapyImpl;
}

/// @nodoc
abstract class _$$ActionFailureEventImplCopyWith<$Res> {
  factory _$$ActionFailureEventImplCopyWith(_$ActionFailureEventImpl value,
          $Res Function(_$ActionFailureEventImpl) then) =
      __$$ActionFailureEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RemoteFailure failure});

  $RemoteFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$ActionFailureEventImplCopyWithImpl<$Res>
    extends _$DeviceControlEventCopyWithImpl<$Res, _$ActionFailureEventImpl>
    implements _$$ActionFailureEventImplCopyWith<$Res> {
  __$$ActionFailureEventImplCopyWithImpl(_$ActionFailureEventImpl _value,
      $Res Function(_$ActionFailureEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$ActionFailureEventImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get failure {
    return $RemoteFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$ActionFailureEventImpl implements ActionFailureEvent {
  const _$ActionFailureEventImpl(this.failure);

  @override
  final RemoteFailure failure;

  @override
  String toString() {
    return 'DeviceControlEvent.actionFailureEvent(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActionFailureEventImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ActionFailureEventImplCopyWith<_$ActionFailureEventImpl> get copyWith =>
      __$$ActionFailureEventImplCopyWithImpl<_$ActionFailureEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() powerOff,
    required TResult Function() toggleTherapy,
    required TResult Function(RemoteFailure failure) actionFailureEvent,
    required TResult Function(bool isActive) therapyStateChangedEvent,
  }) {
    return actionFailureEvent(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? powerOff,
    TResult? Function()? toggleTherapy,
    TResult? Function(RemoteFailure failure)? actionFailureEvent,
    TResult? Function(bool isActive)? therapyStateChangedEvent,
  }) {
    return actionFailureEvent?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? powerOff,
    TResult Function()? toggleTherapy,
    TResult Function(RemoteFailure failure)? actionFailureEvent,
    TResult Function(bool isActive)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    if (actionFailureEvent != null) {
      return actionFailureEvent(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PowerOff value) powerOff,
    required TResult Function(ToggleTherapy value) toggleTherapy,
    required TResult Function(ActionFailureEvent value) actionFailureEvent,
    required TResult Function(TherapyStateChangedEvent value)
        therapyStateChangedEvent,
  }) {
    return actionFailureEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PowerOff value)? powerOff,
    TResult? Function(ToggleTherapy value)? toggleTherapy,
    TResult? Function(ActionFailureEvent value)? actionFailureEvent,
    TResult? Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
  }) {
    return actionFailureEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PowerOff value)? powerOff,
    TResult Function(ToggleTherapy value)? toggleTherapy,
    TResult Function(ActionFailureEvent value)? actionFailureEvent,
    TResult Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    if (actionFailureEvent != null) {
      return actionFailureEvent(this);
    }
    return orElse();
  }
}

abstract class ActionFailureEvent implements DeviceControlEvent {
  const factory ActionFailureEvent(final RemoteFailure failure) =
      _$ActionFailureEventImpl;

  RemoteFailure get failure;

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ActionFailureEventImplCopyWith<_$ActionFailureEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TherapyStateChangedEventImplCopyWith<$Res> {
  factory _$$TherapyStateChangedEventImplCopyWith(
          _$TherapyStateChangedEventImpl value,
          $Res Function(_$TherapyStateChangedEventImpl) then) =
      __$$TherapyStateChangedEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isActive});
}

/// @nodoc
class __$$TherapyStateChangedEventImplCopyWithImpl<$Res>
    extends _$DeviceControlEventCopyWithImpl<$Res,
        _$TherapyStateChangedEventImpl>
    implements _$$TherapyStateChangedEventImplCopyWith<$Res> {
  __$$TherapyStateChangedEventImplCopyWithImpl(
      _$TherapyStateChangedEventImpl _value,
      $Res Function(_$TherapyStateChangedEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActive = null,
  }) {
    return _then(_$TherapyStateChangedEventImpl(
      null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$TherapyStateChangedEventImpl implements TherapyStateChangedEvent {
  const _$TherapyStateChangedEventImpl(this.isActive);

  @override
  final bool isActive;

  @override
  String toString() {
    return 'DeviceControlEvent.therapyStateChangedEvent(isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TherapyStateChangedEventImpl &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isActive);

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TherapyStateChangedEventImplCopyWith<_$TherapyStateChangedEventImpl>
      get copyWith => __$$TherapyStateChangedEventImplCopyWithImpl<
          _$TherapyStateChangedEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() powerOff,
    required TResult Function() toggleTherapy,
    required TResult Function(RemoteFailure failure) actionFailureEvent,
    required TResult Function(bool isActive) therapyStateChangedEvent,
  }) {
    return therapyStateChangedEvent(isActive);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? powerOff,
    TResult? Function()? toggleTherapy,
    TResult? Function(RemoteFailure failure)? actionFailureEvent,
    TResult? Function(bool isActive)? therapyStateChangedEvent,
  }) {
    return therapyStateChangedEvent?.call(isActive);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? powerOff,
    TResult Function()? toggleTherapy,
    TResult Function(RemoteFailure failure)? actionFailureEvent,
    TResult Function(bool isActive)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    if (therapyStateChangedEvent != null) {
      return therapyStateChangedEvent(isActive);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PowerOff value) powerOff,
    required TResult Function(ToggleTherapy value) toggleTherapy,
    required TResult Function(ActionFailureEvent value) actionFailureEvent,
    required TResult Function(TherapyStateChangedEvent value)
        therapyStateChangedEvent,
  }) {
    return therapyStateChangedEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PowerOff value)? powerOff,
    TResult? Function(ToggleTherapy value)? toggleTherapy,
    TResult? Function(ActionFailureEvent value)? actionFailureEvent,
    TResult? Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
  }) {
    return therapyStateChangedEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PowerOff value)? powerOff,
    TResult Function(ToggleTherapy value)? toggleTherapy,
    TResult Function(ActionFailureEvent value)? actionFailureEvent,
    TResult Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    if (therapyStateChangedEvent != null) {
      return therapyStateChangedEvent(this);
    }
    return orElse();
  }
}

abstract class TherapyStateChangedEvent implements DeviceControlEvent {
  const factory TherapyStateChangedEvent(final bool isActive) =
      _$TherapyStateChangedEventImpl;

  bool get isActive;

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TherapyStateChangedEventImplCopyWith<_$TherapyStateChangedEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DeviceControlState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionSuccess,
    required TResult Function(RemoteFailure remoteFailure) actionFailure,
    required TResult Function(bool isActive) therapyStateChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionSuccess,
    TResult? Function(RemoteFailure remoteFailure)? actionFailure,
    TResult? Function(bool isActive)? therapyStateChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionSuccess,
    TResult Function(RemoteFailure remoteFailure)? actionFailure,
    TResult Function(bool isActive)? therapyStateChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ActionSuccess value) actionSuccess,
    required TResult Function(ActionFailure value) actionFailure,
    required TResult Function(TherapyStateChanged value) therapyStateChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ActionSuccess value)? actionSuccess,
    TResult? Function(ActionFailure value)? actionFailure,
    TResult? Function(TherapyStateChanged value)? therapyStateChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ActionSuccess value)? actionSuccess,
    TResult Function(ActionFailure value)? actionFailure,
    TResult Function(TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceControlStateCopyWith<$Res> {
  factory $DeviceControlStateCopyWith(
          DeviceControlState value, $Res Function(DeviceControlState) then) =
      _$DeviceControlStateCopyWithImpl<$Res, DeviceControlState>;
}

/// @nodoc
class _$DeviceControlStateCopyWithImpl<$Res, $Val extends DeviceControlState>
    implements $DeviceControlStateCopyWith<$Res> {
  _$DeviceControlStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$DeviceControlStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'DeviceControlState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionSuccess,
    required TResult Function(RemoteFailure remoteFailure) actionFailure,
    required TResult Function(bool isActive) therapyStateChanged,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionSuccess,
    TResult? Function(RemoteFailure remoteFailure)? actionFailure,
    TResult? Function(bool isActive)? therapyStateChanged,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionSuccess,
    TResult Function(RemoteFailure remoteFailure)? actionFailure,
    TResult Function(bool isActive)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ActionSuccess value) actionSuccess,
    required TResult Function(ActionFailure value) actionFailure,
    required TResult Function(TherapyStateChanged value) therapyStateChanged,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ActionSuccess value)? actionSuccess,
    TResult? Function(ActionFailure value)? actionFailure,
    TResult? Function(TherapyStateChanged value)? therapyStateChanged,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ActionSuccess value)? actionSuccess,
    TResult Function(ActionFailure value)? actionFailure,
    TResult Function(TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements DeviceControlState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$ActionSuccessImplCopyWith<$Res> {
  factory _$$ActionSuccessImplCopyWith(
          _$ActionSuccessImpl value, $Res Function(_$ActionSuccessImpl) then) =
      __$$ActionSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ActionSuccessImplCopyWithImpl<$Res>
    extends _$DeviceControlStateCopyWithImpl<$Res, _$ActionSuccessImpl>
    implements _$$ActionSuccessImplCopyWith<$Res> {
  __$$ActionSuccessImplCopyWithImpl(
      _$ActionSuccessImpl _value, $Res Function(_$ActionSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ActionSuccessImpl implements ActionSuccess {
  const _$ActionSuccessImpl();

  @override
  String toString() {
    return 'DeviceControlState.actionSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ActionSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionSuccess,
    required TResult Function(RemoteFailure remoteFailure) actionFailure,
    required TResult Function(bool isActive) therapyStateChanged,
  }) {
    return actionSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionSuccess,
    TResult? Function(RemoteFailure remoteFailure)? actionFailure,
    TResult? Function(bool isActive)? therapyStateChanged,
  }) {
    return actionSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionSuccess,
    TResult Function(RemoteFailure remoteFailure)? actionFailure,
    TResult Function(bool isActive)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (actionSuccess != null) {
      return actionSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ActionSuccess value) actionSuccess,
    required TResult Function(ActionFailure value) actionFailure,
    required TResult Function(TherapyStateChanged value) therapyStateChanged,
  }) {
    return actionSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ActionSuccess value)? actionSuccess,
    TResult? Function(ActionFailure value)? actionFailure,
    TResult? Function(TherapyStateChanged value)? therapyStateChanged,
  }) {
    return actionSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ActionSuccess value)? actionSuccess,
    TResult Function(ActionFailure value)? actionFailure,
    TResult Function(TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (actionSuccess != null) {
      return actionSuccess(this);
    }
    return orElse();
  }
}

abstract class ActionSuccess implements DeviceControlState {
  const factory ActionSuccess() = _$ActionSuccessImpl;
}

/// @nodoc
abstract class _$$ActionFailureImplCopyWith<$Res> {
  factory _$$ActionFailureImplCopyWith(
          _$ActionFailureImpl value, $Res Function(_$ActionFailureImpl) then) =
      __$$ActionFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RemoteFailure remoteFailure});

  $RemoteFailureCopyWith<$Res> get remoteFailure;
}

/// @nodoc
class __$$ActionFailureImplCopyWithImpl<$Res>
    extends _$DeviceControlStateCopyWithImpl<$Res, _$ActionFailureImpl>
    implements _$$ActionFailureImplCopyWith<$Res> {
  __$$ActionFailureImplCopyWithImpl(
      _$ActionFailureImpl _value, $Res Function(_$ActionFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remoteFailure = null,
  }) {
    return _then(_$ActionFailureImpl(
      null == remoteFailure
          ? _value.remoteFailure
          : remoteFailure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get remoteFailure {
    return $RemoteFailureCopyWith<$Res>(_value.remoteFailure, (value) {
      return _then(_value.copyWith(remoteFailure: value));
    });
  }
}

/// @nodoc

class _$ActionFailureImpl implements ActionFailure {
  const _$ActionFailureImpl(this.remoteFailure);

  @override
  final RemoteFailure remoteFailure;

  @override
  String toString() {
    return 'DeviceControlState.actionFailure(remoteFailure: $remoteFailure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActionFailureImpl &&
            (identical(other.remoteFailure, remoteFailure) ||
                other.remoteFailure == remoteFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, remoteFailure);

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ActionFailureImplCopyWith<_$ActionFailureImpl> get copyWith =>
      __$$ActionFailureImplCopyWithImpl<_$ActionFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionSuccess,
    required TResult Function(RemoteFailure remoteFailure) actionFailure,
    required TResult Function(bool isActive) therapyStateChanged,
  }) {
    return actionFailure(remoteFailure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionSuccess,
    TResult? Function(RemoteFailure remoteFailure)? actionFailure,
    TResult? Function(bool isActive)? therapyStateChanged,
  }) {
    return actionFailure?.call(remoteFailure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionSuccess,
    TResult Function(RemoteFailure remoteFailure)? actionFailure,
    TResult Function(bool isActive)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (actionFailure != null) {
      return actionFailure(remoteFailure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ActionSuccess value) actionSuccess,
    required TResult Function(ActionFailure value) actionFailure,
    required TResult Function(TherapyStateChanged value) therapyStateChanged,
  }) {
    return actionFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ActionSuccess value)? actionSuccess,
    TResult? Function(ActionFailure value)? actionFailure,
    TResult? Function(TherapyStateChanged value)? therapyStateChanged,
  }) {
    return actionFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ActionSuccess value)? actionSuccess,
    TResult Function(ActionFailure value)? actionFailure,
    TResult Function(TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (actionFailure != null) {
      return actionFailure(this);
    }
    return orElse();
  }
}

abstract class ActionFailure implements DeviceControlState {
  const factory ActionFailure(final RemoteFailure remoteFailure) =
      _$ActionFailureImpl;

  RemoteFailure get remoteFailure;

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ActionFailureImplCopyWith<_$ActionFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TherapyStateChangedImplCopyWith<$Res> {
  factory _$$TherapyStateChangedImplCopyWith(_$TherapyStateChangedImpl value,
          $Res Function(_$TherapyStateChangedImpl) then) =
      __$$TherapyStateChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isActive});
}

/// @nodoc
class __$$TherapyStateChangedImplCopyWithImpl<$Res>
    extends _$DeviceControlStateCopyWithImpl<$Res, _$TherapyStateChangedImpl>
    implements _$$TherapyStateChangedImplCopyWith<$Res> {
  __$$TherapyStateChangedImplCopyWithImpl(_$TherapyStateChangedImpl _value,
      $Res Function(_$TherapyStateChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActive = null,
  }) {
    return _then(_$TherapyStateChangedImpl(
      null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$TherapyStateChangedImpl implements TherapyStateChanged {
  const _$TherapyStateChangedImpl(this.isActive);

  @override
  final bool isActive;

  @override
  String toString() {
    return 'DeviceControlState.therapyStateChanged(isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TherapyStateChangedImpl &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isActive);

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TherapyStateChangedImplCopyWith<_$TherapyStateChangedImpl> get copyWith =>
      __$$TherapyStateChangedImplCopyWithImpl<_$TherapyStateChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionSuccess,
    required TResult Function(RemoteFailure remoteFailure) actionFailure,
    required TResult Function(bool isActive) therapyStateChanged,
  }) {
    return therapyStateChanged(isActive);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionSuccess,
    TResult? Function(RemoteFailure remoteFailure)? actionFailure,
    TResult? Function(bool isActive)? therapyStateChanged,
  }) {
    return therapyStateChanged?.call(isActive);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionSuccess,
    TResult Function(RemoteFailure remoteFailure)? actionFailure,
    TResult Function(bool isActive)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (therapyStateChanged != null) {
      return therapyStateChanged(isActive);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ActionSuccess value) actionSuccess,
    required TResult Function(ActionFailure value) actionFailure,
    required TResult Function(TherapyStateChanged value) therapyStateChanged,
  }) {
    return therapyStateChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ActionSuccess value)? actionSuccess,
    TResult? Function(ActionFailure value)? actionFailure,
    TResult? Function(TherapyStateChanged value)? therapyStateChanged,
  }) {
    return therapyStateChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ActionSuccess value)? actionSuccess,
    TResult Function(ActionFailure value)? actionFailure,
    TResult Function(TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) {
    if (therapyStateChanged != null) {
      return therapyStateChanged(this);
    }
    return orElse();
  }
}

abstract class TherapyStateChanged implements DeviceControlState {
  const factory TherapyStateChanged(final bool isActive) =
      _$TherapyStateChangedImpl;

  bool get isActive;

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TherapyStateChangedImplCopyWith<_$TherapyStateChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
