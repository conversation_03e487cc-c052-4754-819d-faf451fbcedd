import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:fpdart/fpdart.dart';
import 'package:remote/application/device_control_heat_bloc/device_control_heat_bloc.dart';
import 'package:remote/domain/failures/remote_failures.dart';

import 'mock_classes/facade_mock_class.dart';

void main() {
  late MockRemoteControlFacade mockRemoteRepository;
  late DeviceControlHeatBloc bloc;

  setUp(() {
    mockRemoteRepository = MockRemoteControlFacade();
    bloc = DeviceControlHeatBloc(mockRemoteRepository);
  });

  group('DeviceControlHeatBloc', () {
    const remoteFailure = RemoteFailure.unexpectedFailure('Unexpected Failure');

    blocTest<DeviceControlHeatBloc, DeviceControlHeatState>(
      'emits [changeHeatLevelSuccess] when increaseHeat succeeds',
      build: () {
        when(() => mockRemoteRepository.increaseHeat())
            .thenAnswer((_) async => const Right(unit));
        return bloc;
      },
      act: (bloc) => bloc.add(const DeviceControlHeatEvent.increaseHeat()),
      expect: () => [const DeviceControlHeatState.changeHeatLevelSuccess()],
      verify: (_) {
        verify(() => mockRemoteRepository.increaseHeat()).called(1);
      },
    );

    blocTest<DeviceControlHeatBloc, DeviceControlHeatState>(
      'emits [changeHeatLevelFailure] when increaseHeat fails',
      build: () {
        when(() => mockRemoteRepository.increaseHeat())
            .thenAnswer((_) async => const Left(remoteFailure));
        return bloc;
      },
      act: (bloc) => bloc.add(const DeviceControlHeatEvent.increaseHeat()),
      expect: () => [
        const DeviceControlHeatState.changeHeatLevelFailure(remoteFailure)
      ],
      verify: (_) {
        verify(() => mockRemoteRepository.increaseHeat()).called(1);
      },
    );

    blocTest<DeviceControlHeatBloc, DeviceControlHeatState>(
      'emits [changeHeatLevelSuccess] when decreaseHeat succeeds',
      build: () {
        when(() => mockRemoteRepository.decreaseHeat())
            .thenAnswer((_) async => const Right(unit));
        return bloc;
      },
      act: (bloc) => bloc.add(const DeviceControlHeatEvent.decreaseHeat()),
      expect: () => [const DeviceControlHeatState.changeHeatLevelSuccess()],
      verify: (_) {
        verify(() => mockRemoteRepository.decreaseHeat()).called(1);
      },
    );

    blocTest<DeviceControlHeatBloc, DeviceControlHeatState>(
      'emits [changeHeatLevelFailure] when decreaseHeat fails',
      build: () {
        when(() => mockRemoteRepository.decreaseHeat())
            .thenAnswer((_) async => const Left(remoteFailure));
        return bloc;
      },
      act: (bloc) => bloc.add(const DeviceControlHeatEvent.decreaseHeat()),
      expect: () => [
        const DeviceControlHeatState.changeHeatLevelFailure(remoteFailure)
      ],
      verify: (_) {
        verify(() => mockRemoteRepository.decreaseHeat()).called(1);
      },
    );
  });
}
